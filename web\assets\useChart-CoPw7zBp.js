var t=Object.defineProperty,e=Object.defineProperties,r=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,a=(e,r,n)=>r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[r]=n,l=(t,e)=>{for(var r in e||(e={}))o.call(e,r)&&a(t,r,e[r]);if(n)for(var r of n(e))i.call(e,r)&&a(t,r,e[r]);return t},s=(t,n)=>e(t,r(n));import{i as c}from"./index-DEP0vMzR.js";import{c as u,s as h,r as m,w as p,d,bw as f,p as g,n as b}from"./vendor-9ydHGNSq.js";import{O as v,u as y}from"./index-C5Q-N6Yp.js";const w=()=>({chartHeight:"16rem",fontSize:13,fontColor:"#999",themeColor:v("--el-color-primary-light-1"),colors:[v("--el-color-primary-light-1"),"#4ABEFF","#EDF2FF","#14DEBA","#FFAF20","#FA8A6C","#FFAF20"]}),E=[50,100,200,350],O=[50,100,200];function C(t={}){const{initOptions:e,initDelay:r=0,threshold:n=.1,autoTheme:o=!0}=t,i=y(),{isDark:a,menuOpen:u,menuType:v}=h(i),C=m();let F=null,S=null,A=null,D=null,z=null,L=!1;const T=()=>{z&&cancelAnimationFrame(z),z=requestAnimationFrame((()=>{q(),z=null}))},j=()=>{D&&clearTimeout(D),D=window.setTimeout((()=>{T(),D=null}),100)},x=t=>{b(T),t.forEach((t=>{setTimeout(T,t)}))};p(u,(()=>x(E))),p(v,(()=>{b(T),setTimeout((()=>x(O)),0)})),o&&p(a,(()=>{F&&!L&&requestAnimationFrame((()=>{if(F&&!L){const t=F.getOption();t&&V(t)}}))}));const R=(t,e=1,r)=>l({color:t,width:e},r&&{type:r}),k=()=>{S&&(S.disconnect(),S=null)},I=t=>{F||!C.value||L||(F=c(C.value)),F&&!L&&(F.setOption(t),A=null)},P=(t={})=>{if(!C.value||L)return;const o=l(l({},e),t);try{(t=>{const e=t.getBoundingClientRect();return e.width>0&&e.height>0&&e.top<window.innerHeight&&e.bottom>0})(C.value)?r>0?setTimeout((()=>I(o)),r):I(o):(A=o,!S&&C.value&&(S=new IntersectionObserver((t=>{t.forEach((t=>{t.isIntersecting&&A&&!L&&requestAnimationFrame((()=>{if(!L&&A)try{F||(F=c(t.target));const e=new CustomEvent("chartVisible",{detail:{options:A}});t.target.dispatchEvent(e),A=null,k()}catch(e){}}))}))}),{threshold:n}),S.observe(C.value)))}catch(i){}},V=t=>{if(!L)try{if(!F)return void P(t);F.setOption(t)}catch(e){}},q=()=>{if(F&&!L)try{F.resize()}catch(t){}},B=()=>{if(L=!0,F)try{F.dispose()}catch(t){}finally{F=null}k(),D&&(clearTimeout(D),D=null),z&&(cancelAnimationFrame(z),z=null),A=null};return d((()=>{window.addEventListener("resize",j)})),f((()=>{window.removeEventListener("resize",j)})),g((()=>{B()})),{isDark:a,chartRef:C,initChart:P,updateChart:V,handleResize:q,destroyChart:B,getChartInstance:()=>F,isChartInitialized:()=>null!==F,getAxisLineStyle:(t=!0)=>({show:t,lineStyle:R(a.value?"#444":"#EDEDED")}),getSplitLineStyle:(t=!0)=>({show:t,lineStyle:R(a.value?"#444":"#EDEDED",1,"dashed")}),getAxisLabelStyle:(t=!0)=>{const{fontColor:e,fontSize:r}=w();return{show:t,color:e,fontSize:r}},getAxisTickStyle:()=>({show:!1}),getAnimationConfig:(t=50,e=1500)=>({animationDelay:e=>e*t+200,animationDuration:t=>e-50*t,animationEasing:"quarticOut"}),getTooltipStyle:(t="axis",e={})=>l({trigger:t,backgroundColor:a.value?"rgba(0, 0, 0, 0.8)":"rgba(255, 255, 255, 0.9)",borderColor:a.value?"#333":"#ddd",borderWidth:1,textStyle:{color:a.value?"#fff":"#333"}},e),getLegendStyle:(t="bottom",e={})=>{const r=l({textStyle:{color:a.value?"#fff":"#333"},itemWidth:12,itemHeight:12,itemGap:20},e);switch(t){case"bottom":return s(l({},r),{bottom:0,left:"center",orient:"horizontal",icon:"roundRect"});case"top":return s(l({},r),{top:0,left:"center",orient:"horizontal",icon:"roundRect"});case"left":return s(l({},r),{left:0,top:"center",orient:"vertical",icon:"roundRect"});case"right":return s(l({},r),{right:0,top:"center",orient:"vertical",icon:"roundRect"});default:return r}},useChartOps:w,getGridWithLegend:(t,e="bottom",r={})=>{const n=l({top:15,right:15,bottom:8,left:0,containLabel:!0},r);if(!t)return n;switch(e){case"bottom":return s(l({},n),{bottom:40});case"top":return s(l({},n),{top:40});case"left":return s(l({},n),{left:120});case"right":return s(l({},n),{right:120});default:return n}}}}function F(t){const{props:e,generateOptions:r,checkEmpty:n,watchSources:o=[],onVisible:i,chartOptions:a={}}=t,c=C(a),{chartRef:h,initChart:m,isDark:g}=c,b=u((()=>!!e.isEmpty||!!n&&n())),v=()=>{b.value||m(r())},y=()=>{i?i():v()};return o.length>0&&p(o,v,{deep:!0}),p(g,v),d((()=>{v(),h.value&&h.value.addEventListener("chartVisible",y)})),f((()=>{h.value&&h.value.removeEventListener("chartVisible",y)})),s(l({},c),{isEmpty:b,updateChart:v,handleChartVisible:y})}export{F as a,C as b,w as u};
