import api from '@/utils/http'

// 内存卡接口类型定义
export interface MemoryCard {
  _id: string
  size: string
  description: string
  price: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  __v?: number
}

// 获取内存卡列表的参数
export interface GetMemoryCardsParams {
  isActive?: boolean
  sortBy?: string
  order?: string
}

// API响应类型
export interface MemoryCardsResponse {
  code: number
  message: string
  data: MemoryCard[]
}

/**
 * 内存卡API服务
 */
export class MemoryCardsService {
  /**
   * 获取内存卡列表
   */
  static async getMemoryCards(params?: GetMemoryCardsParams): Promise<MemoryCard[]> {
    try {
      const response = await api.get<MemoryCardsResponse>({
        url: '/memory-cards',
        params
      })
      
      return response.data || []
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取激活的内存卡列表
   */
  static async getActiveMemoryCards(): Promise<MemoryCard[]> {
    return this.getMemoryCards({ isActive: true })
  }

  /**
   * 根据ID获取内存卡详情
   */
  static async getMemoryCardById(id: string): Promise<MemoryCard | null> {
    try {
      const response = await this.getMemoryCards()
      return response.find(card => card._id === id) || null
    } catch (error) {
      throw error
    }
  }

  /**
   * 创建内存卡
   */
  static async createMemoryCard(data: Partial<MemoryCard>): Promise<MemoryCard> {
    return api.post<MemoryCard>({
      url: '/memory-cards',
      data,
      showErrorMessage: true
    })
  }

  /**
   * 更新内存卡
   */
  static async updateMemoryCard(id: string, data: Partial<MemoryCard>): Promise<MemoryCard> {
    return api.put<MemoryCard>({
      url: `/memory-cards/${id}`,
      data,
      showErrorMessage: true
    })
  }

  /**
   * 删除内存卡
   */
  static async deleteMemoryCard(id: string): Promise<void> {
    return api.del<void>({
      url: `/memory-cards/${id}`,
      showErrorMessage: true
    })
  }
}

export default MemoryCardsService
