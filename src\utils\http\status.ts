/**
 * 接口状态码
 */
export enum ApiStatus {
  /**
   * 成功
   */
  success = 200,

  /**
   * 创建成功
   */
  created = 201,

  /**
   * 请求已接收
   */
  accepted = 202,

  /**
   * 操作已执行
   */
  handled = 204,

  /**
   * 请求错误
   */
  error = 400,

  /**
   * 未授权
   */
  unauthorized = 401,

  /**
   * 访问受限
   */
  forbidden = 403,

  /**
   * 资源不存在
   */
  notFound = 404,

  /**
   * 不允许此方法
   */
  methodNotAllowed = 405,

  /**
   * 请求超时
   */
  timeout = 408,

  /**
   * 请求过多
   */
  tooManyRequests = 429,

  /**
   * 服务端异常
   */
  internalServerError = 500,

  /**
   * 网关错误
   */
  badGateway = 502,

  /**
   * 服务不可用
   */
  serviceUnavailable = 503,

  /**
   * 网关超时
   */
  gatewayTimeout = 504,

  /**
   * 网络错误
   */
  networkError = 0
}
