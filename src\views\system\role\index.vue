<template>
  <div class="page-content role-management-container">
    <!-- PC端搜索栏 -->
    <div v-if="!isMobile" class="pc-search-bar">
      <ElRow :gutter="12">
        <ElCol :xs="24" :sm="14" :md="16" :lg="18" :xl="20">
          <ElInput v-model="searchKeyword" placeholder="角色名称/描述" @keyup.enter="searchRoles" @clear="handleClearSearch"
            @input="handleInputChange" clearable></ElInput>
        </ElCol>
        <ElCol :xs="24" :sm="10" :md="8" :lg="6" :xl="4" class="el-col2">
          <ElButton v-ripple @click="searchRoles">搜索</ElButton>
          <ElButton @click="showDialog('add')" v-ripple>新增角色</ElButton>
        </ElCol>
      </ElRow>
    </div>

    <!-- 移动端搜索栏 -->
    <div v-if="isMobile" class="mobile-search-bar">
      <div class="search-input-wrapper">
        <ElInput v-model="searchKeyword" placeholder="搜索角色名称、描述" @keyup.enter="searchRoles" @clear="handleClearSearch"
          @input="handleInputChange" clearable class="search-input">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </ElInput>
        <ElButton type="primary" @click="searchRoles" class="search-button">
          <el-icon>
            <Search />
          </el-icon>
        </ElButton>
      </div>
    </div>

    <!-- PC端表格 -->
    <ArtTable v-if="!isMobile" :data="roleList" index v-loading="tableLoading">
      <template #default>
        <ElTableColumn label="角色名称" prop="name">
          <template #default="scope">
            {{ getRoleDisplayName(scope.row.name) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="描述" prop="description" />
        <ElTableColumn label="权限数" width="100">
          <template #default="scope">
            <span>{{ scope.row.permissions?.length || 0 }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="系统角色" width="100">
          <template #default="scope">
            <ElTag :type="scope.row.isSystem ? 'info' : 'primary'">
              {{ scope.row.isSystem ? '是' : '否' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="创建时间" prop="createdAt">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </ElTableColumn>
        <ElTableColumn fixed="right" label="操作" width="200px">
          <template #default="scope">
            <ElRow>
              <el-button link @click="showPermissionDialog(scope.row)"> 菜单权限 </el-button>
              <el-button link @click="showDialog('edit', scope.row)" :disabled="scope.row.isSystem">
                编辑
              </el-button>
              <el-button link @click="handleDeleteRole(scope.row._id)" type="danger" :disabled="scope.row.isSystem">
                删除
              </el-button>
            </ElRow>
          </template>
        </ElTableColumn>
      </template>
    </ArtTable>

    <!-- 移动端卡片列表 -->
    <div v-if="isMobile" class="mobile-card-list">
      <!-- 加载状态 -->
      <div v-if="tableLoading" class="mobile-skeleton">
        <div v-for="i in 3" :key="i" class="mobile-skeleton-card">
          <el-skeleton animated>
            <template #template>
              <div class="skeleton-header">
                <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
                <div class="skeleton-content">
                  <el-skeleton-item variant="text" style="width: 60%; height: 16px;" />
                  <el-skeleton-item variant="text" style="width: 40%; height: 14px;" />
                </div>
              </div>
              <el-skeleton-item variant="rect" style="width: 100%; height: 60px; margin-top: 12px;" />
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- 角色卡片列表 -->
      <transition-group name="card-list" tag="div" class="card-container" v-if="!tableLoading">
        <div v-for="(role, index) in roleList" :key="role._id" class="role-card">
          <!-- 卡片头部 -->
          <div class="card-header-mobile">
            <div class="role-info">
              <div class="role-avatar">
                <div class="role-icon">
                  <el-icon :size="20">
                    <User />
                  </el-icon>
                </div>
              </div>
              <div class="role-details">
                <div class="role-name">{{ getRoleDisplayName(role.name) }}</div>
                <div class="role-description">{{ role.description || '-' }}</div>
              </div>
            </div>
            <div class="role-status">
              <el-tag :type="role.isSystem ? 'info' : 'primary'" size="small">
                {{ role.isSystem ? '系统角色' : '自定义' }}
              </el-tag>
            </div>
          </div>

          <!-- 角色信息 -->
          <div class="role-content">
            <div class="info-section">
              <div class="info-item">
                <span class="info-label">权限数量</span>
                <span class="info-value">{{ role.permissions?.length || 0 }} 个</span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value">{{ formatDate(role.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="card-footer">
            <div class="action-buttons-mobile">
              <div class="action-buttons">
                <el-button size="small" type="primary" plain @click="showPermissionDialog(role)">
                  <el-icon>
                    <Setting />
                  </el-icon>
                  <span>菜单权限</span>
                </el-button>
                <el-button size="small" type="success" plain @click="showDialog('edit', role)"
                  :disabled="role.isSystem">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  <span>编辑</span>
                </el-button>
                <el-button size="small" type="danger" plain @click="handleDeleteRole(role._id)"
                  :disabled="role.isSystem">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  <span>删除</span>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- 空状态 -->
      <div v-if="!tableLoading && roleList.length === 0" class="empty-state">
        <div class="empty-icon">👥</div>
        <div class="empty-text">暂无角色数据</div>
        <el-button type="primary" @click="showDialog('add')" class="empty-action">
          <el-icon>
            <Plus />
          </el-icon>
          <span>新增角色</span>
        </el-button>
      </div>
    </div>

    <!-- 移动端底部操作栏 -->
    <div v-if="isMobile" class="mobile-action-bar">
      <div class="action-bar-container">
        <!-- 新增按钮 -->
        <div class="action-item primary-action" @click="showDialog('add')">
          <div class="action-icon add-icon">
            <el-icon :size="24">
              <Plus />
            </el-icon>
          </div>
          <span class="action-label">新增</span>
        </div>

        <!-- 刷新按钮 -->
        <div class="action-item" @click="getRoleList">
          <div class="action-icon refresh-icon">
            <el-icon :size="20">
              <Refresh />
            </el-icon>
          </div>
          <span class="action-label">刷新</span>
        </div>
      </div>
    </div>

    <!-- PC端分页 -->
    <div v-if="!isMobile" class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        :total="total" :layout="paginationLayout" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :small="isMobileScreen" :pager-count="isMobileScreen ? 5 : 7" background />
    </div>

    <ElDialog v-model="dialogVisible" :title="dialogType === 'add'
      ? '新增角色'
      : `编辑角色 - ${currentRole?.name ? getRoleDisplayName(currentRole.name) : ''}`
      " width="30%" align-center>
      <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px">
        <ElFormItem label="角色名称" prop="name">
          <ElInput v-model="form.name" />
          <div v-if="dialogType === 'edit' && roleNameMap[form.name]" class="form-tip">
            系统预设角色：{{ getRoleDisplayName(form.name) }}
          </div>
        </ElFormItem>
        <ElFormItem label="描述" prop="description">
          <ElInput v-model="form.description" type="textarea" :rows="3" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="dialogVisible = false">取消</ElButton>
          <ElButton type="primary" @click="handleSubmit" :loading="submitting">提交</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- PC端权限对话框 -->
    <ElDialog v-if="!isMobile" v-model="permissionDialog"
      :title="`菜单权限 - ${currentRole?.name ? getRoleDisplayName(currentRole.name) : ''}`" width="520px" align-center
      class="el-dialog-border" @close="handlePermissionDialogClose">
      <ElScrollbar height="70vh">
        <ElTree ref="treeRef" :data="processedMenuList" show-checkbox node-key="name" :default-expand-all="isExpandAll"
          :default-checked-keys="selectedPermissions" :props="defaultProps" @check="handleTreeCheck">
          <template #default="{ data }">
            <div style="display: flex; align-items: center">
              <span v-if="data.isAuth">
                {{ data.label }}
              </span>
              <span v-else>{{ defaultProps.label(data) }}</span>
            </div>
          </template>
        </ElTree>
      </ElScrollbar>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="toggleExpandAll">{{ isExpandAll ? '全部收起' : '全部展开' }}</ElButton>
          <ElButton @click="toggleSelectAll" style="margin-left: 8px">{{
            isSelectAll ? '取消全选' : '全部选择'
          }}</ElButton>
          <ElButton type="primary" @click="savePermission" :loading="submitting">保存</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 移动端权限对话框 -->
    <ElDialog v-if="isMobile" v-model="permissionDialog"
      :title="`菜单权限 - ${currentRole?.name ? getRoleDisplayName(currentRole.name) : ''}`" width="95%" align-center
      class="el-dialog-border mobile-permission-dialog" @close="handlePermissionDialogClose">

      <!-- 移动端顶部操作栏 -->
      <div class="mobile-permission-header">
        <div class="permission-stats">
          <span class="stats-text">已选择 {{ getActualSelectedCount() }} 项权限</span>
        </div>
        <div class="header-actions">
          <el-button size="small" @click="toggleExpandAll" class="action-btn">
            <el-icon>
              <Setting />
            </el-icon>
            {{ isExpandAll ? '收起' : '展开' }}
          </el-button>
          <el-button size="small" @click="toggleSelectAll" class="action-btn" type="primary" plain>
            <el-icon>
              <User />
            </el-icon>
            {{ isAllSelected ? '取消全选' : '全选' }}
          </el-button>
        </div>
      </div>

      <!-- 移动端权限列表 -->
      <div class="mobile-permission-content">
        <div class="permission-list">
          <div v-for="(menu, index) in processedMenuList" :key="menu.name" class="permission-group">
            <!-- 一级菜单 -->
            <div class="permission-item level-1" @click="toggleMenuItem(menu)">
              <div class="item-content">
                <div class="item-left">
                  <el-checkbox :model-value="isMenuChecked(menu)" :indeterminate="isMenuIndeterminate(menu)"
                    @change="(val) => handleMenuCheck(menu, val)" @click.stop class="item-checkbox">
                  </el-checkbox>
                  <div class="item-info">
                    <div class="item-title">{{ getMenuTitle(menu) }}</div>
                    <div class="item-subtitle">{{ getMenuSubtitle(menu) }}</div>
                  </div>
                </div>
                <div class="item-right">
                  <el-icon class="expand-icon" :class="{ 'expanded': isMenuExpanded(menu) }">
                    <ArrowRight />
                  </el-icon>
                </div>
              </div>
            </div>

            <!-- 二级菜单 -->
            <div v-if="isMenuExpanded(menu) && menu.children" class="children-container">
              <div v-for="child in menu.children" :key="child.name" class="permission-item level-2">
                <div class="item-content">
                  <div class="item-left">
                    <el-checkbox :model-value="isMenuChecked(child)" :indeterminate="isMenuIndeterminate(child)"
                      @change="(val) => handleMenuCheck(child, val)" class="item-checkbox">
                    </el-checkbox>
                    <div class="item-info">
                      <div class="item-title">{{ getMenuTitle(child) }}</div>
                      <div class="item-subtitle">{{ getMenuSubtitle(child) }}</div>
                    </div>
                  </div>
                </div>

                <!-- 三级菜单 -->
                <div v-if="child.children" class="children-container level-3">
                  <div v-for="grandChild in child.children" :key="grandChild.name" class="permission-item level-3">
                    <div class="item-content">
                      <div class="item-left">
                        <el-checkbox :model-value="isMenuChecked(grandChild)"
                          @change="(val) => handleMenuCheck(grandChild, val)" class="item-checkbox">
                        </el-checkbox>
                        <div class="item-info">
                          <div class="item-title">{{ getMenuTitle(grandChild) }}</div>
                          <div class="item-subtitle">{{ getMenuSubtitle(grandChild) }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端底部操作栏 -->
      <template #footer>
        <div class="mobile-permission-footer">
          <div class="footer-stats">
            <span class="selected-count">已选择 {{ getActualSelectedCount() }} 项</span>
          </div>
          <div class="footer-actions">
            <ElButton @click="handlePermissionDialogCancel" class="cancel-btn">取消</ElButton>
            <ElButton type="primary" @click="savePermission" :loading="submitting" class="save-btn">
              保存权限
            </ElButton>
          </div>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref as vueRef, onMounted, onUnmounted, computed } from 'vue'
import { useMenuStore } from '@/store/modules/menu'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, User, Edit, Delete, Plus, Refresh, Setting, ArrowRight } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { formatMenuTitle } from '@/router/utils/utils'
import { RoleService, Role as ApiRole } from '@/api/roleApi'
import { useWindowSize } from '@/composables/useWindowSize'

defineOptions({ name: 'Role' })

const dialogVisible = ref(false)
const permissionDialog = ref(false)
const { menuList } = storeToRefs(useMenuStore())
const treeRef = ref()
const isExpandAll = ref(true)
const isSelectAll = ref(false)

// 计算是否全选状态（用于移动端）
const isAllSelected = computed(() => {
  if (isMobile.value) {
    // 获取所有可选择的菜单项
    const allKeys = getAllNodeKeys(processedMenuList.value)

    // 检查是否所有菜单都被选中
    if (allKeys.length === 0) return false

    // 检查每个菜单项是否都在selectedPermissions中
    const allSelected = allKeys.every(key => selectedPermissions.value.includes(key))

    return allSelected
  }
  return isSelectAll.value
})
const tableLoading = ref(false)
const submitting = ref(false)
const searchKeyword = ref('')
const selectedPermissions = ref<string[]>([])
const originalPermissions = ref<string[]>([]) // 备份原始权限，用于取消时恢复
const currentRole = ref<ApiRole | null>(null)
const expandedMenus = ref<Record<string, boolean>>({})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 判断是否为移动屏幕
const { width } = useWindowSize()
const isMobileScreen = computed(() => width.value < 768)

// 移动端检测
const isMobile = vueRef(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 角色名称中英文映射
const roleNameMap: Record<string, string> = {
  user: '普通用户',
  admin: '管理员',
  superadmin: '超级管理员'
}

// 获取角色中文名称
const getRoleDisplayName = (roleName: string): string => {
  return roleNameMap[roleName] || roleName
}

// 处理API响应数据，适配不同格式
const processApiResponse = (response: any): { items: ApiRole[]; total: number } => {
  // 检查各种可能的响应格式

  // 情况1: 标准格式 { data: { items: [...], total: number } }
  if (response?.data?.items && Array.isArray(response.data.items)) {
    return {
      items: response.data.items.map(adaptRoleItem),
      total: response.data.total || 0
    }
  }

  // 情况2: 直接返回 { items: [...], total: number }
  if (response?.items && Array.isArray(response.items)) {
    return {
      items: response.items.map(adaptRoleItem),
      total: response.total || 0
    }
  }

  // 情况3: 直接返回数组 [...]
  if (Array.isArray(response)) {
    return {
      items: response.map(adaptRoleItem),
      total: response.length
    }
  }

  // 情况4: { data: [...] }
  if (response?.data && Array.isArray(response.data)) {
    return {
      items: response.data.map(adaptRoleItem),
      total: response.data.length
    }
  }

  // 默认返回空数据
  return { items: [], total: 0 }
}

// 适配角色数据项，确保字段名一致
const adaptRoleItem = (item: any): ApiRole => {
  // 提供所有可能的字段映射
  return {
    _id: item._id || item.id || '',
    name: item.name || item.roleName || '',
    description: item.description || item.desc || item.des || '',
    permissions: item.permissions || item.perms || [],
    isSystem: typeof item.isSystem === 'boolean' ? item.isSystem : !!item.isSystem,
    createdAt: item.createdAt || item.createTime || item.createAt || item.date || '',
    updatedAt: item.updatedAt || item.updateTime || item.updateAt || ''
  }
}

// 处理菜单数据，将 authList 转换为子节点
const processedMenuList = computed(() => {
  // 确保菜单列表不为空
  if (!menuList.value || menuList.value.length === 0) {
    return []
  }

  const processNode = (node: any) => {
    const processed = {
      ...node,
      expanded: node.expanded !== undefined ? node.expanded : false // 添加expanded属性
    }

    // 如果有 authList，将其转换为子节点
    if (node.meta && node.meta.authList && node.meta.authList.length) {
      const authNodes = node.meta.authList.map((auth: any) => ({
        id: `${node.id}_${auth.auth_mark}`,
        name: `${node.name}_${auth.auth_mark}`,
        label: auth.title,
        auth_mark: auth.auth_mark,
        isAuth: true,
        checked: auth.checked || false,
        expanded: false // 为auth节点也添加expanded属性
      }))

      processed.children = processed.children ? [...processed.children, ...authNodes] : authNodes
    }

    // 递归处理子节点
    if (processed.children) {
      processed.children = processed.children.map(processNode)
    }

    return processed
  }

  // 处理并返回所有菜单项，不做任何过滤
  const result = menuList.value.map(processNode)
  return result
})

const formRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  description: [{ required: true, message: '请输入角色描述', trigger: 'blur' }]
})

const form = reactive({
  name: '',
  description: '',
  permissions: [] as string[]
})

const roleList = ref<ApiRole[]>([])

onMounted(() => {
  fetchRoles()
})

// 获取角色列表
const fetchRoles = async () => {
  tableLoading.value = true
  try {
    const response = await RoleService.getRoles({
      page: currentPage.value,
      limit: pageSize.value,
      keyword: searchKeyword.value
    })

    // 使用处理函数解析响应
    const { items, total: totalCount } = processApiResponse(response)
    roleList.value = items
    total.value = totalCount
  } catch (error) {
    ElMessage.error('获取角色列表失败')
    roleList.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

const searchRoles = () => {
  currentPage.value = 1
  fetchRoles()
}

// 清空搜索处理
const handleClearSearch = () => {
  currentPage.value = 1 // 重置到第一页
  fetchRoles()
}

// 输入变化处理 - 自动检测空值并重置
let inputTimer: NodeJS.Timeout | null = null
const handleInputChange = (value: string) => {
  // 清除之前的定时器
  if (inputTimer) {
    clearTimeout(inputTimer)
  }

  // 如果输入为空，延迟500ms后自动重置
  if (!value || value.trim() === '') {
    inputTimer = setTimeout(() => {
      if (!searchKeyword.value || searchKeyword.value.trim() === '') {
        currentPage.value = 1
        fetchRoles()
      }
    }, 500)
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchRoles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchRoles()
}

const dialogType = ref('add')

const showDialog = (type: string, row?: ApiRole) => {
  dialogVisible.value = true
  dialogType.value = type

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }

  if (type === 'edit' && row) {
    form.name = row.name
    form.description = row.description
    form.permissions = row.permissions || []
    currentRole.value = row
  } else {
    form.name = ''
    form.description = ''
    form.permissions = []
    currentRole.value = null
  }
}

const showPermissionDialog = (row: ApiRole) => {
  currentRole.value = row
  // 获取当前角色的权限列表并备份原始权限
  const permissions = row.permissions || []
  selectedPermissions.value = [...permissions] // 创建副本
  originalPermissions.value = [...permissions] // 备份原始权限

  // 重置展开状态
  expandedMenus.value = {}

  // 确保弹窗打开时，树形控件会加载所有可能的菜单项
  // 这里不做任何过滤，确保角色管理页面显示所有可能的菜单权限

  permissionDialog.value = true

  // 等待DOM更新后重置树组件状态
  nextTick(() => {
    resetTreeState()
    // 移动端也需要同步权限状态
    if (isMobile.value) {
      syncMobilePermissionState()
    }
  })
}

// 处理权限弹窗关闭事件
const handlePermissionDialogClose = () => {
  // 恢复原始权限状态
  selectedPermissions.value = [...originalPermissions.value]
}

// 处理权限弹窗取消事件
const handlePermissionDialogCancel = () => {
  // 恢复原始权限状态
  selectedPermissions.value = [...originalPermissions.value]
  // 关闭弹窗
  permissionDialog.value = false
}

// 同步移动端权限状态
const syncMobilePermissionState = () => {
  if (!selectedPermissions.value || selectedPermissions.value.length === 0) {
    return
  }

  // 保存原始权限数据
  const currentPermissions = [...selectedPermissions.value]

  // 直接使用原始权限数据，不进行转换
  // 这样可以保持权限数据的原始格式和数量
  // selectedPermissions.value 保持不变，让 isMenuChecked 函数来处理匹配逻辑
}

// 将权限字符串转换为菜单节点名称
const convertPermissionsToMenuNames = (permissions: string[]): string[] => {
  const menuNames: string[] = []
  const processedPermissions = new Set<string>()

  // 递归遍历菜单，找到匹配的权限
  const findMatchingMenus = (menus: any[], permissionList: string[]) => {
    menus.forEach(menu => {
      if (menu.name) {
        // 检查是否有匹配的权限
        const matchedPermissions = permissionList.filter(permission => {
          // 避免重复处理同一个权限
          if (processedPermissions.has(permission)) return false

          // 1. 直接匹配菜单名
          if (permission === menu.name) return true

          // 2. 路径匹配（如果权限是路径格式）
          if (menu.path && permission === menu.path) return true

          // 3. 组件名匹配
          if (menu.component && permission.includes(menu.component)) return true

          // 4. 权限标识匹配（如 appointment:list）
          if (menu.name && permission.includes(':')) {
            const [module, action] = permission.split(':')
            if (menu.name.toLowerCase().includes(module.toLowerCase()) &&
              menu.name.toLowerCase().includes(action.toLowerCase())) {
              return true
            }
          }

          // 5. 路径片段匹配（如 /appointment/list 匹配 appointment-list）
          if (menu.path && permission.startsWith('/')) {
            const pathSegments = permission.split('/').filter(Boolean)
            const menuNameLower = menu.name.toLowerCase()
            if (pathSegments.every(segment => menuNameLower.includes(segment.toLowerCase()))) {
              return true
            }
          }

          // 6. 模糊匹配（去除特殊字符后匹配）
          const normalizeString = (str: string) => str.toLowerCase().replace(/[-_]/g, '')
          if (normalizeString(permission).includes(normalizeString(menu.name)) ||
            normalizeString(menu.name).includes(normalizeString(permission))) {
            return true
          }

          return false
        })

        // 如果有匹配的权限，添加菜单名称，并标记权限为已处理
        if (matchedPermissions.length > 0) {
          console.log(`菜单 "${menu.name}" 匹配到权限:`, matchedPermissions)
          menuNames.push(menu.name)
          matchedPermissions.forEach(permission => processedPermissions.add(permission))
        }
      }

      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        findMatchingMenus(menu.children, permissionList)
      }
    })
  }

  findMatchingMenus(processedMenuList.value, permissions)

  // 添加未能匹配的原始权限，确保不丢失权限
  permissions.forEach(permission => {
    if (!processedPermissions.has(permission)) {
      menuNames.push(permission)
    }
  })

  return menuNames
}

// 重置树组件状态的函数
const resetTreeState = () => {
  const tree = treeRef.value
  if (!tree) return

  try {
    // 清除所有选中状态
    tree.setCheckedKeys([])

    // 设置新的选中状态 - 修复权限显示问题
    if (selectedPermissions.value && selectedPermissions.value.length > 0) {
      // 将权限字符串转换为对应的节点名称
      const nodeKeysToCheck = convertPermissionsToNodeKeys(selectedPermissions.value)
      tree.setCheckedKeys(nodeKeysToCheck)
    }
  } catch (error) {
  }
}

// 将权限字符串转换为树形组件的节点键
const convertPermissionsToNodeKeys = (permissions: string[]) => {
  const nodeKeys: string[] = []

  permissions.forEach(permission => {
    // 权限格式：System, System:User, System:Nested:Menu1 等
    const parts = permission.split(':')

    if (parts.length === 1) {
      // 顶级权限，如 "System" - 检查是否有对应的子权限
      const hasChildPermissions = permissions.some(p => p.startsWith(permission + ':'))
      if (!hasChildPermissions) {
        // 如果没有子权限，说明这是一个独立的顶级权限
        nodeKeys.push(parts[0])
      }
      // 如果有子权限，则不添加父节点，避免自动选中所有子节点
    } else {
      // 子权限，如 "System:User", "System:Nested:Menu1"
      // 只添加最后一级的名称
      nodeKeys.push(parts[parts.length - 1])
    }
  })

  return [...new Set(nodeKeys)] // 去重
}

const defaultProps = {
  children: 'children',
  label: (data: any) => formatMenuTitle(data.meta?.title) || ''
}

const handleDeleteRole = (id: string) => {
  ElMessageBox.confirm('确定删除该角色吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(async () => {
    try {
      await RoleService.deleteRole(id)
      ElMessage.success('删除成功')
      fetchRoles()
    } catch (error) {
      ElMessage.error('删除角色失败')
    }
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (dialogType.value === 'add') {
          await RoleService.createRole({
            name: form.name,
            description: form.description,
            permissions: form.permissions
          })
          ElMessage.success('新增成功')
        } else if (dialogType.value === 'edit' && currentRole.value) {
          await RoleService.updateRole(currentRole.value._id, {
            name: form.name,
            description: form.description,
            permissions: form.permissions
          })
          ElMessage.success('修改成功')
        }
        dialogVisible.value = false
        fetchRoles()
      } catch (error) {
        ElMessage.error('操作失败，请重试')
      } finally {
        submitting.value = false
      }
    }
  })
}

const savePermission = async () => {
  if (!currentRole.value) return

  submitting.value = true

  try {
    let processedPermissions: string[]

    // 如果是移动端，使用移动端的权限数据
    if (isMobile.value) {
      // 移动端也需要进行权限层级关系处理，与PC端保持一致
      processedPermissions = processPermissionsAdvanced(selectedPermissions.value, [])
    } else {
      // PC端的权限处理逻辑
      const tree = treeRef.value
      if (!tree) {
        submitting.value = false
        return
      }

      // 获取所有选中的节点，包括父节点和子节点
      const checkedKeys = tree.getCheckedKeys()
      // 获取半选中的节点（父节点）
      const halfCheckedKeys = tree.getHalfCheckedKeys()

      // 智能处理权限数据，生成层级关系和多粒度权限
      processedPermissions = processPermissionsAdvanced(checkedKeys, halfCheckedKeys)
    }

    // 更新角色的权限
    await RoleService.updateRole(currentRole.value._id, {
      permissions: processedPermissions
    })

    ElMessage.success('权限保存成功')
    permissionDialog.value = false
    fetchRoles()
  } catch (error) {
    ElMessage.error('保存权限失败')
  } finally {
    submitting.value = false
  }
}

// 简化权限处理函数 - 只保存真正选中的权限，不包含半选中的父节点
const processPermissionsAdvanced = (checkedKeys: string[], halfCheckedKeys: string[]) => {
  const permissionSet = new Set<string>()

  // 定义简化的菜单层级关系映射
  const menuHierarchy: Record<string, string> = {
    // 首页（无父级）
    // 'Home': null,

    // 预约管理模块
    'AppointmentList': 'Appointment',
    'AppointmentConfig': 'Appointment',

    // 系统管理模块
    'User': 'System',
    'Role': 'System',
    'Menus': 'System',

    // 仪表盘模块（一级菜单，无父级）
    'Console': 'Dashboard',
    'Analysis': 'Dashboard',
    'Ecommerce': 'Dashboard',

    // 模板中心模块（一级菜单，无父级）
    'Cards': 'Template',
    'Banners': 'Template',
    'Charts': 'Template',
    'Map': 'Template',
    'Chat': 'Template',
    'Calendar': 'Template',
    'Pricing': 'Template',

    // 组件中心模块（一级菜单，无父级）
    'IconList': 'Widgets',
    'IconSelector': 'Widgets',
    'ImageCrop': 'Widgets',
    'Excel': 'Widgets',
    'Video': 'Widgets',
    'CountTo': 'Widgets',
    'WangEditor': 'Widgets',
    'Watermark': 'Widgets',
    'ContextMenu': 'Widgets',
    'Qrcode': 'Widgets',
    'Drag': 'Widgets',
    'TextScroll': 'Widgets',
    'Fireworks': 'Widgets',
    'ElementUI': 'Widgets',
    'Menu': 'Widgets',
    'Nested': 'Widgets',

    // 文章管理模块（一级菜单，无父级）
    'ArticleList': 'Article',
    'ArticleDetail': 'Article',
    'CommentManagement': 'Article',
    'PublishArticle': 'Article',

    // 结果页面模块（一级菜单，无父级）
    'ResultSuccess': 'Result',
    'ResultFail': 'Result',

    // 异常页面模块（一级菜单，无父级）
    'Exception403': 'Exception',
    'Exception404': 'Exception',
    'Exception500': 'Exception',

    // 系统监控模块（一级菜单，无父级）
    'ServerMonitor': 'Safeguard'
  }

  // 只处理真正选中的节点（checkedKeys），不包含半选中的父节点（halfCheckedKeys）
  // 这样可以避免父节点被自动包含在权限中
  checkedKeys.forEach(key => {
    const parentMenu = menuHierarchy[key]

    if (parentMenu) {
      // 如果是子菜单，只有当子菜单被真正选中时，才添加父菜单和层级关系权限
      permissionSet.add(parentMenu)  // 父菜单权限
      permissionSet.add(`${parentMenu}:${key}`)  // 层级关系权限
    } else {
      // 如果是父菜单或未定义的权限，直接添加
      permissionSet.add(key)
    }
  })

  const result = Array.from(permissionSet)
  return result
}

const toggleExpandAll = () => {
  // 如果是移动端，使用移动端的展开逻辑
  if (isMobile.value) {
    const newExpandState = !isExpandAll.value

    // 遍历所有菜单项，设置展开状态
    const setExpandStateRecursively = (menus: any[]) => {
      menus.forEach(menu => {
        if (menu.name && menu.children && menu.children.length > 0) {
          expandedMenus.value[menu.name] = newExpandState
        }
        if (menu.children) {
          setExpandStateRecursively(menu.children)
        }
      })
    }

    setExpandStateRecursively(processedMenuList.value)
    isExpandAll.value = newExpandState
    return
  }

  // PC端的展开逻辑
  const tree = treeRef.value
  if (!tree) return

  // 使用store.nodesMap直接控制所有节点的展开状态
  const nodes = tree.store.nodesMap
  for (const node in nodes) {
    nodes[node].expanded = !isExpandAll.value
  }

  isExpandAll.value = !isExpandAll.value
}

const toggleSelectAll = () => {
  // 如果是移动端，使用移动端的全选逻辑
  if (isMobile.value) {
    const allKeys = getAllNodeKeys(processedMenuList.value)
    const currentlyAllSelected = isAllSelected.value

    if (!currentlyAllSelected) {
      // 全选：获取所有节点的key并设置为选中
      selectedPermissions.value = [...allKeys]
    } else {
      // 取消全选：清空所有选中
      selectedPermissions.value = []
    }

    // 强制触发响应式更新
    nextTick(() => {
      // 这里可以添加额外的状态同步逻辑
    })
    return
  }

  // PC端的全选逻辑
  const tree = treeRef.value
  if (!tree) return

  if (!isSelectAll.value) {
    // 全选：获取所有节点的key并设置为选中
    const allKeys = getAllNodeKeys(processedMenuList.value)
    tree.setCheckedKeys(allKeys)
  } else {
    // 取消全选：清空所有选中
    tree.setCheckedKeys([])
  }

  isSelectAll.value = !isSelectAll.value
}

const getAllNodeKeys = (nodes: any[]): string[] => {
  const keys: string[] = []
  const traverse = (nodeList: any[]) => {
    nodeList.forEach((node) => {
      if (node.name) {
        keys.push(node.name)
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return keys
}

const handleTreeCheck = () => {
  const tree = treeRef.value
  if (!tree) return

  // 使用树组件的getCheckedKeys方法获取选中的节点
  const checkedKeys = tree.getCheckedKeys()
  const allKeys = getAllNodeKeys(processedMenuList.value)

  // 判断是否全选：选中的节点数量等于总节点数量
  isSelectAll.value = checkedKeys.length === allKeys.length && allKeys.length > 0
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    .replace(/\//g, '-')
}

// 移动端权限管理方法
const getMenuTitle = (menu: any) => {
  // 如果是auth节点，直接返回label
  if (menu.isAuth) {
    return menu.label || menu.title || menu.name
  }

  // 使用与PC端相同的格式化逻辑
  return formatMenuTitle(menu.meta?.title) || menu.title || menu.label || menu.name || '未知菜单'
}

const isMenuExpanded = (menu: any) => {
  return expandedMenus.value[menu.name] || false
}

const toggleMenuItem = (menu: any) => {
  if (!menu.name) return

  // 使用响应式的展开状态管理
  expandedMenus.value[menu.name] = !expandedMenus.value[menu.name]
}

// 检查菜单是否匹配权限（用于智能匹配）
const isMenuMatchedByPermission = (menu: any, permission: string) => {
  if (!menu.name || !permission) return false

  // 1. 直接匹配菜单名
  if (permission === menu.name) return true

  // 2. 路径匹配（如果权限是路径格式）
  if (menu.path && permission === menu.path) return true

  // 3. 组件名匹配
  if (menu.component && permission.includes(menu.component)) return true

  // 4. 权限标识匹配（如 appointment:list）
  if (permission.includes(':')) {
    const [module, action] = permission.split(':')
    if (menu.name.toLowerCase().includes(module.toLowerCase()) &&
      menu.name.toLowerCase().includes(action.toLowerCase())) {
      return true
    }
  }

  // 5. 路径片段匹配（如 /appointment/list 匹配 appointment-list）
  if (permission.startsWith('/')) {
    const pathSegments = permission.split('/').filter(Boolean)
    const menuNameLower = menu.name.toLowerCase()
    if (pathSegments.every(segment => menuNameLower.includes(segment.toLowerCase()))) {
      return true
    }
  }

  return false
}

const isMenuChecked = (menu: any) => {
  if (!menu.name) return false

  // 直接匹配 - 优先使用精确匹配
  if (selectedPermissions.value.includes(menu.name)) return true

  // 智能匹配权限 - 只在精确匹配失败时使用
  return selectedPermissions.value.some(permission =>
    isMenuMatchedByPermission(menu, permission)
  )
}

const isMenuIndeterminate = (menu: any) => {
  if (!menu.children || menu.children.length === 0) return false

  const checkedChildren = menu.children.filter((child: any) => isMenuChecked(child))

  return checkedChildren.length > 0 && checkedChildren.length < menu.children.length
}

const handleMenuCheck = (menu: any, checked: boolean) => {
  if (!menu || !menu.name) return

  if (checked) {
    // 选中当前菜单 - 使用精确匹配添加
    if (!selectedPermissions.value.includes(menu.name)) {
      selectedPermissions.value.push(menu.name)
    }

    // 递归选中所有子菜单
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach((child: any) => {
        handleMenuCheck(child, true)
      })
    }
  } else {
    // 取消选中当前菜单 - 需要处理智能匹配的情况
    // 1. 先尝试精确匹配移除
    const exactIndex = selectedPermissions.value.indexOf(menu.name)
    if (exactIndex > -1) {
      selectedPermissions.value.splice(exactIndex, 1)
    } else {
      // 2. 如果精确匹配失败，查找智能匹配的权限并移除
      const matchedPermissions = selectedPermissions.value.filter(permission =>
        isMenuMatchedByPermission(menu, permission)
      )
      matchedPermissions.forEach(permission => {
        const index = selectedPermissions.value.indexOf(permission)
        if (index > -1) {
          selectedPermissions.value.splice(index, 1)
        }
      })
    }

    // 递归取消选中所有子菜单
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach((child: any) => {
        handleMenuCheck(child, false)
      })
    }
  }

  // 触发响应式更新
  selectedPermissions.value = [...selectedPermissions.value]
}

// 获取实际选中的权限数量（只统计真正被勾选的菜单项）
const getActualSelectedCount = () => {
  if (!processedMenuList.value || processedMenuList.value.length === 0) {
    return 0
  }

  // 递归统计所有被勾选的菜单项
  const countSelectedMenus = (menus: any[]): number => {
    let count = 0
    menus.forEach(menu => {
      // 只统计有name且被精确匹配选中的菜单项
      if (menu.name && selectedPermissions.value.includes(menu.name)) {
        count++
      }
      // 递归统计子菜单
      if (menu.children && menu.children.length > 0) {
        count += countSelectedMenus(menu.children)
      }
    })
    return count
  }

  return countSelectedMenus(processedMenuList.value)
}

const getMenuSubtitle = (menu: any) => {
  // 如果是auth节点，显示权限标识
  if (menu.isAuth) {
    return menu.auth_mark || '权限项'
  }

  // 如果有子菜单，显示选择统计 - 只统计精确匹配的权限
  if (menu.children && menu.children.length > 0) {
    // 递归统计所有子菜单中被精确匹配选中的项目
    const countCheckedMenus = (menus: any[]): number => {
      let count = 0
      menus.forEach(child => {
        // 只统计精确匹配的权限，避免智能匹配导致的错误统计
        if (child.name && selectedPermissions.value.includes(child.name)) {
          count++
        }
        if (child.children && child.children.length > 0) {
          count += countCheckedMenus(child.children)
        }
      })
      return count
    }

    // 递归统计所有子菜单总数（包括嵌套子菜单）
    const countTotalMenus = (menus: any[]): number => {
      let count = 0
      menus.forEach(child => {
        if (child.name) {
          count++
        }
        if (child.children && child.children.length > 0) {
          count += countTotalMenus(child.children)
        }
      })
      return count
    }

    const checkedCount = countCheckedMenus(menu.children)
    const totalCount = countTotalMenus(menu.children)
    return `${checkedCount}/${totalCount} 项已选择`
  }

  // 显示路径或其他描述信息
  return menu.path || menu.meta?.title || '菜单项'
}



// 分页布局，根据屏幕大小调整
const paginationLayout = computed(() => {
  return isMobileScreen.value
    ? 'total, prev, pager, next'
    : 'total, sizes, prev, pager, next, jumper'
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  // 清理输入定时器
  if (inputTimer) {
    clearTimeout(inputTimer)
    inputTimer = null
  }
})
</script>

<style lang="scss" scoped>
.page-content {
  .el-col2 {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    justify-content: flex-start;

    @media (min-width: 576px) {
      margin-top: 0;
    }

    @media (min-width: 768px) and (max-width: 1199px) {
      gap: 6px;
      flex-wrap: nowrap;

      .el-button {
        padding: 8px 12px;
        font-size: 13px;
        white-space: nowrap;
        min-width: auto;
      }
    }

    @media (min-width: 1200px) {
      gap: 12px;
    }
  }

  .svg-icon {
    width: 1.8em;
    height: 1.8em;
    overflow: hidden;
    vertical-align: -8px;
    fill: currentcolor;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    width: 100%;

    :deep(.el-pagination) {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 5px;

      @media (max-width: 768px) {
        .el-pagination__sizes {
          margin-right: 0;
        }

        .el-pagination__jump {
          margin-left: 0;
        }

        .el-pager {
          margin: 0 5px;
        }
      }
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 4px;
    padding-left: 2px;
  }
}

/* 移动端样式 */
@media screen and (max-width: 768px) {
  .role-management-container {
    padding-bottom: 100px;
    /* 为底部操作栏留出足够空间 */

    /* 移动端搜索栏样式 */
    .mobile-search-bar {
      margin-bottom: 16px;

      .search-input-wrapper {
        display: flex;
        gap: 8px;
        align-items: center;

        .search-input {
          flex: 1;

          :deep(.el-input__wrapper) {
            border-radius: 20px;
            padding: 0 16px;
          }

          :deep(.el-input__prefix) {
            color: #9ca3af;
          }
        }

        .search-button {
          width: 36px;
          height: 36px;
          min-width: 36px;
          border-radius: 50%;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          :deep(.el-icon) {
            margin: 0;
            font-size: 16px;
          }
        }
      }
    }

    /* 移动端卡片列表样式 */
    .mobile-card-list {
      margin-bottom: 120px;
      /* 为底部操作栏留出足够空间 */

      .mobile-skeleton {
        .mobile-skeleton-card {
          background: white;
          border-radius: 12px;
          padding: 16px;
          margin-bottom: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          .skeleton-header {
            display: flex;
            align-items: center;
            gap: 12px;

            .skeleton-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
        }
      }

      .card-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .role-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #f1f5f9;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .card-header-mobile {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .role-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;

            .role-avatar {
              width: 44px;
              height: 44px;
              border-radius: 50%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;

              .role-icon {
                color: white;
              }
            }

            .role-details {
              flex: 1;

              .role-name {
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 2px;
              }

              .role-description {
                font-size: 12px;
                color: #6b7280;
              }
            }
          }

          .role-status {
            flex-shrink: 0;
          }
        }

        .role-content {
          margin-bottom: 16px;

          .info-section {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #f8fafc;
              border-radius: 8px;

              .info-label {
                font-size: 14px;
                color: #6b7280;
                font-weight: 500;
              }

              .info-value {
                font-size: 14px;
                color: #374151;
                font-weight: 500;
              }
            }
          }
        }

        .card-footer {
          .action-buttons-mobile {
            .action-buttons {
              display: grid;
              grid-template-columns: 1fr 1fr 1fr;
              gap: 8px;

              .el-button {
                font-size: 12px;
                padding: 8px 12px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .el-icon {
                  font-size: 14px;
                }

                span {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 12px;
          opacity: 0.5;
        }

        .empty-text {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 16px;
        }

        .empty-action {
          font-size: 14px;
        }
      }
    }

    /* 移动端底部操作栏样式 */
    .mobile-action-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
      padding: env(safe-area-inset-bottom, 0) 0 0 0;

      .action-bar-container {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 12px 20px;

        .action-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          padding: 8px 12px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          user-select: none;
          min-width: 60px;

          &:active {
            transform: scale(0.95);
          }

          .action-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &.add-icon {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              color: white;
              box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
            }

            &.refresh-icon {
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              color: #d97706;
              border: 1px solid #fbbf24;
            }
          }

          .action-label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
          }

          &.primary-action .action-label {
            color: #1e40af;
            font-weight: 600;
          }
        }
      }
    }
  }

  /* 移动端权限对话框样式 */
  .mobile-permission-dialog {
    :deep(.el-dialog) {
      margin: 5vh auto;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
    }

    :deep(.el-dialog__body) {
      padding: 0;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    :deep(.el-dialog__footer) {
      padding: 0;
      margin: 0;
    }

    .mobile-permission-header {
      padding: 16px;
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;

      .permission-stats {
        .stats-text {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
      }

      .header-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          font-size: 12px;
          padding: 6px 12px;
          border-radius: 6px;

          .el-icon {
            font-size: 12px;
            margin-right: 4px;
          }
        }
      }
    }

    .mobile-permission-content {
      flex: 1;
      overflow-y: auto;
      padding: 8px 0;

      .permission-list {
        .permission-group {
          margin-bottom: 8px;

          .permission-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.2s ease;

            &.level-1 {
              background: white;
              cursor: pointer;

              &:hover {
                background: #f8fafc;
              }

              .item-title {
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
              }

              .item-subtitle {
                font-size: 12px;
                color: #6b7280;
                margin-top: 2px;
              }
            }

            &.level-2 {
              background: #f8fafc;
              padding-left: 32px;

              .item-title {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
              }

              .item-subtitle {
                font-size: 11px;
                color: #9ca3af;
                margin-top: 2px;
              }
            }

            &.level-3 {
              background: #f1f5f9;
              padding-left: 48px;

              .item-title {
                font-size: 13px;
                font-weight: 400;
                color: #4b5563;
              }

              .item-subtitle {
                font-size: 10px;
                color: #9ca3af;
                margin-top: 2px;
              }
            }

            .item-content {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .item-left {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;

                .item-checkbox {
                  flex-shrink: 0;

                  :deep(.el-checkbox__input) {
                    .el-checkbox__inner {
                      width: 18px;
                      height: 18px;
                    }
                  }
                }

                .item-info {
                  flex: 1;
                  min-width: 0;

                  .item-title {
                    line-height: 1.4;
                    word-break: break-all;
                  }

                  .item-subtitle {
                    line-height: 1.3;
                  }
                }
              }

              .item-right {
                flex-shrink: 0;
                margin-left: 8px;

                .expand-icon {
                  font-size: 16px;
                  color: #9ca3af;
                  transition: transform 0.2s ease;

                  &.expanded {
                    transform: rotate(90deg);
                  }
                }
              }
            }
          }

          .children-container {
            &.level-3 {
              .permission-item {
                border-left: 2px solid #e5e7eb;
              }
            }
          }
        }
      }
    }

    .mobile-permission-footer {
      padding: 16px;
      background: white;
      border-top: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .footer-stats {
        .selected-count {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
      }

      .footer-actions {
        display: flex;
        gap: 12px;

        .cancel-btn {
          padding: 8px 16px;
          font-size: 14px;
        }

        .save-btn {
          padding: 8px 20px;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
