var e=Object.defineProperty,n=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,i=(n,t,l)=>t in n?e(n,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[t]=l,u=(e,u)=>{for(var a in u||(u={}))t.call(u,a)&&i(e,a,u[a]);if(n)for(var a of n(u))l.call(u,a)&&i(e,a,u[a]);return e},a=(e,n,t)=>new Promise(((l,i)=>{var u=e=>{try{r(t.next(e))}catch(n){i(n)}},a=e=>{try{r(t.throw(e))}catch(n){i(n)}},r=e=>e.done?l(e.value):Promise.resolve(e.value).then(u,a);r((t=t.apply(e,n)).next())}));import{E as r,F as o,G as s,I as c,J as v,K as d,L as f}from"./index-C5Q-N6Yp.js";import{r as m,w,c as p,N as h,b as y,d as F,e as b}from"./vendor-9ydHGNSq.js";const E=s?window:void 0,g=s?window.document:void 0;function S(e){var n;const t=d(e);return null!=(n=null==t?void 0:t.$el)?n:t}function k(...e){let n,t,l,i;if("string"==typeof e[0]||Array.isArray(e[0])?([t,l,i]=e,n=E):[n,t,l,i]=e,!n)return c;Array.isArray(t)||(t=[t]),Array.isArray(l)||(l=[l]);const a=[],r=()=>{a.forEach((e=>e())),a.length=0},o=w((()=>[S(n),d(i)]),(([e,n])=>{if(r(),!e)return;const i=f(n)?u({},n):n;a.push(...t.flatMap((n=>l.map((t=>((e,n,t,l)=>(e.addEventListener(n,t,l),()=>e.removeEventListener(n,t,l)))(e,n,t,i))))))}),{immediate:!0,flush:"post"}),s=()=>{o(),r()};return v(s),s}function x(e){const n=function(){const e=m(!1),n=b();return n&&F((()=>{e.value=!0}),n),e}();return p((()=>(n.value,Boolean(e()))))}function A(e,n={}){const{delayEnter:t=0,delayLeave:l=0,window:i=E}=n,u=m(!1);let a;const r=e=>{const n=e?t:l;a&&(clearTimeout(a),a=void 0),n?a=setTimeout((()=>u.value=e),n):u.value=e};return i?(k(e,"mouseenter",(()=>r(!0)),{passive:!0}),k(e,"mouseleave",(()=>r(!1)),{passive:!0}),u):u}const I=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function L(e,n={}){const{document:t=g,autoExit:l=!1}=n,i=p((()=>{var n;return null!=(n=S(e))?n:null==t?void 0:t.querySelector("html")})),u=m(!1),r=p((()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find((e=>t&&e in t||i.value&&e in i.value)))),o=p((()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find((e=>t&&e in t||i.value&&e in i.value)))),s=p((()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find((e=>t&&e in t||i.value&&e in i.value)))),c=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find((e=>t&&e in t)),d=x((()=>i.value&&t&&void 0!==r.value&&void 0!==o.value&&void 0!==s.value)),f=()=>{if(s.value){if(t&&null!=t[s.value])return t[s.value];{const e=i.value;if(null!=(null==e?void 0:e[s.value]))return Boolean(e[s.value])}}return!1};function w(){return a(this,null,(function*(){if(d.value&&u.value){if(o.value)if(null!=(null==t?void 0:t[o.value]))yield t[o.value]();else{const e=i.value;null!=(null==e?void 0:e[o.value])&&(yield e[o.value]())}u.value=!1}}))}function h(){return a(this,null,(function*(){if(!d.value||u.value)return;f()&&(yield w());const e=i.value;r.value&&null!=(null==e?void 0:e[r.value])&&(yield e[r.value](),u.value=!0)}))}const y=()=>{const e=f();(!e||e&&c&&(null==t?void 0:t[c])===i.value)&&(u.value=e)};return k(t,I,y,!1),k((()=>S(i)),I,y,!1),l&&v(w),{isSupported:d,isFullscreen:u,enter:h,exit:w,toggle:function(){return a(this,null,(function*(){yield u.value?w():h()}))}}}function O(e={}){const{window:n=E}=e,t=null==n?void 0:n.navigator,l=x((()=>t&&"connection"in t)),i=m(!0),u=m(!1),a=m(void 0),r=m(void 0),o=m(void 0),s=m(void 0),c=m(void 0),v=m(void 0),d=m("unknown"),f=l.value&&t.connection;function w(){t&&(i.value=t.onLine,a.value=i.value?void 0:Date.now(),r.value=i.value?Date.now():void 0,f&&(o.value=f.downlink,s.value=f.downlinkMax,v.value=f.effectiveType,c.value=f.rtt,u.value=f.saveData,d.value=f.type))}return n&&(k(n,"offline",(()=>{i.value=!1,a.value=Date.now()})),k(n,"online",(()=>{i.value=!0,r.value=Date.now()}))),f&&k(f,"change",w,!1),w(),{isSupported:y(l),isOnline:y(i),saveData:y(u),offlineAt:y(a),onlineAt:y(r),downlink:y(o),downlinkMax:y(s),effectiveType:y(v),rtt:y(c),type:y(d)}}function q(e={}){const{controls:n=!1,interval:t="requestAnimationFrame"}=e,l=m(new Date),i=()=>l.value=new Date,a="requestAnimationFrame"===t?function(e,n={}){const{immediate:t=!0,fpsLimit:l,window:i=E}=n,u=m(!1),a=l?1e3/l:null;let r=0,o=null;function s(n){if(!u.value||!i)return;r||(r=n);const t=n-r;a&&t<a||(r=n,e({delta:t,timestamp:n})),o=i.requestAnimationFrame(s)}function c(){!u.value&&i&&(u.value=!0,r=0,o=i.requestAnimationFrame(s))}function d(){u.value=!1,null!=o&&i&&(i.cancelAnimationFrame(o),o=null)}return t&&c(),v(d),{isActive:y(u),pause:d,resume:c}}(i,{immediate:!0}):o(i,t,{immediate:!0});return n?u({now:l},a):l}function T(e={}){const{isOnline:n}=O(e);return n}function D(e={}){const{window:n=E,behavior:t="auto"}=e;if(!n)return{x:m(0),y:m(0)};const l=m(n.scrollX),i=m(n.scrollY),u=p({get:()=>l.value,set(e){scrollTo({left:e,behavior:t})}}),a=p({get:()=>i.value,set(e){scrollTo({top:e,behavior:t})}});return k(n,"scroll",(()=>{l.value=n.scrollX,i.value=n.scrollY}),{capture:!1,passive:!0}),{x:u,y:a}}function M(e={}){const{window:n=E,initialWidth:t=Number.POSITIVE_INFINITY,initialHeight:l=Number.POSITIVE_INFINITY,listenOrientation:i=!0,includeScrollbar:u=!0,type:a="inner"}=e,o=m(t),s=m(l),c=()=>{n&&("outer"===a?(o.value=n.outerWidth,s.value=n.outerHeight):u?(o.value=n.innerWidth,s.value=n.innerHeight):(o.value=n.document.documentElement.clientWidth,s.value=n.document.documentElement.clientHeight))};if(c(),r(c),k("resize",c,{passive:!0}),i){const e=function(e,n={}){const{window:t=E}=n,l=x((()=>t&&"matchMedia"in t&&"function"==typeof t.matchMedia));let i;const u=m(!1),a=e=>{u.value=e.matches},r=()=>{i&&("removeEventListener"in i?i.removeEventListener("change",a):i.removeListener(a))},o=h((()=>{l.value&&(r(),i=t.matchMedia(d(e)),"addEventListener"in i?i.addEventListener("change",a):i.addListener(a),u.value=i.matches)}));return v((()=>{o(),r(),i=void 0})),u}("(orientation: portrait)");w(e,(()=>c()))}return{width:o,height:s}}export{M as a,L as b,D as c,q as d,T as e,O as f,A as g,k as u};
