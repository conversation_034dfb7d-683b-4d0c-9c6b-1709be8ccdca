{"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "vueIndentScriptAndStyle": true, "singleQuote": true, "quoteProps": "as-needed", "bracketSpacing": true, "trailingComma": "none", "bracketSameLine": false, "jsxSingleQuote": false, "arrowParens": "always", "insertPragma": false, "requirePragma": false, "proseWrap": "never", "htmlWhitespaceSensitivity": "strict", "endOfLine": "auto", "rangeStart": 0}