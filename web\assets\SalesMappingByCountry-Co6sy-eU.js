import{_ as s}from"./index-pwV1nyK9.js";import{_ as a}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as r,C as e,S as o,x as t}from"./vendor-9ydHGNSq.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const d={class:"custom-card art-custom-card sales-mapping-country"},i={class:"custom-card-body"};const n=a({},[["render",function(a,n){const c=s;return e(),r("div",d,[n[0]||(n[0]=o("div",{class:"custom-card-header"},[o("span",{class:"title"},"全国销售分布")],-1)),o("div",i,[t(c,{data:[{value:30,name:"北京"},{value:25,name:"上海"},{value:45,name:"广州"}],color:["#4C87F3","#93F1B4","#8BD8FC"],radius:["46%","60%"]})])])}],["__scopeId","data-v-35e68609"]]);export{n as default};
