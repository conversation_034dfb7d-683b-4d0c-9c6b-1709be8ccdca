import api from '@/utils/http'

// 套餐类型接口类型定义
export interface PackageType {
  _id: string
  name: string
  description: string
  price: number
  isActive: boolean
  priceFormatted: string
  createdAt: string
  updatedAt: string
  __v?: number
  id?: string
}

// 获取套餐类型列表的参数
export interface GetPackageTypesParams {
  page?: number
  limit?: number
  keyword?: string
  isActive?: boolean
}

// 创建/更新套餐类型的数据
export interface CreatePackageTypeData {
  name: string
  description: string
  price: number
  isActive: boolean
}

export interface UpdatePackageTypeData extends CreatePackageTypeData {}

// API响应类型
export interface ApiResponse<T = any> {
  message: string
  data?: T
}

/**
 * 套餐类型API服务
 */
export class PackageTypesService {
  /**
   * 获取套餐类型列表
   */
  static async getPackageTypes(params?: GetPackageTypesParams): Promise<PackageType[]> {
    try {
      const response = await api.get<PackageType[]>({
        url: '/package-types',
        params
      })
      
      return response || []
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取激活的套餐类型列表
   */
  static async getActivePackageTypes(): Promise<PackageType[]> {
    return this.getPackageTypes({ isActive: true })
  }

  /**
   * 根据ID获取套餐类型详情
   */
  static async getPackageTypeById(id: string): Promise<PackageType> {
    try {
      const response = await api.get<PackageType>({
        url: `/package-types/${id}`
      })

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 搜索套餐类型
   */
  static async searchPackageTypes(keyword: string): Promise<PackageType[]> {
    return this.getPackageTypes({ keyword })
  }

  /**
   * 创建套餐类型
   */
  static async createPackageType(data: CreatePackageTypeData): Promise<PackageType> {
    try {
      const response = await api.post<PackageType>({
        url: '/package-types',
        data
      })

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 更新套餐类型
   */
  static async updatePackageType(id: string, data: UpdatePackageTypeData): Promise<PackageType> {
    try {
      const response = await api.put<PackageType>({
        url: `/package-types/${id}`,
        data
      })

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 删除套餐类型
   */
  static async deletePackageType(id: string): Promise<void> {
    try {
      await api.del<ApiResponse>({
        url: `/package-types/${id}`
      })
    } catch (error) {
      throw error
    }
  }
}

export default PackageTypesService
