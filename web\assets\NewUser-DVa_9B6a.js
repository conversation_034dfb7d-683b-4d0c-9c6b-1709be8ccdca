import{_ as a}from"./ArtTable-CBEgSUvC.js";import"./index-C5Q-N6Yp.js";/* empty css                    *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                       *//* empty css                        */import{a as e}from"./avatar1-CutlWZf5.js";import{a as r,b as t,c as s,m as o,d as l}from"./avatar6-C8uJx9vz.js";import{k as p,r as i,aa as n,d as c,O as m,C as d,S as u,x as v,a6 as g,D as b,bx as j,i as x,u as f,aQ as _,aJ as w,X as h,bq as y}from"./vendor-9ydHGNSq.js";import{_ as k}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-DiJiIjk5.js";const T={class:"card art-custom-card"},U={class:"card-header"},V={style:{display:"flex","align-items":"center"}},q=["src"],C={class:"user-name"},D={style:{display:"flex","align-items":"center"}},E={style:{"margin-left":"10px"}},I=k(p({__name:"NewUser",setup(p){const k=i("本月"),I=n([{username:"中小鱼",province:"北京",sex:0,age:22,percentage:60,pro:0,color:"rgb(var(--art-primary)) !important",avatar:e},{username:"何小荷",province:"深圳",sex:1,age:21,percentage:20,pro:0,color:"rgb(var(--art-secondary)) !important",avatar:r},{username:"誶誶淰",province:"上海",sex:1,age:23,percentage:60,pro:0,color:"rgb(var(--art-warning)) !important",avatar:t},{username:"发呆草",province:"长沙",sex:0,age:28,percentage:50,pro:0,color:"rgb(var(--art-info)) !important",avatar:s},{username:"甜筒",province:"浙江",sex:1,age:26,percentage:70,pro:0,color:"rgb(var(--art-error)) !important",avatar:o},{username:"冷月呆呆",province:"湖北",sex:1,age:25,percentage:90,pro:0,color:"rgb(var(--art-success)) !important",avatar:l}]);c((()=>{Q()}));const Q=()=>{setTimeout((()=>{for(let a=0;a<I.length;a++){let e=I[a];I[a].pro=e.percentage}}),100)};return(e,r)=>{const t=j,s=_,o=w,l=y,p=a;return d(),m("div",T,[u("div",U,[r[1]||(r[1]=u("div",{class:"title"},[u("h4",{class:"box-title"},"新用户"),u("p",{class:"subtitle"},[g("这个月增长"),u("span",{class:"text-success"},"+20%")])],-1)),v(s,{modelValue:f(k),"onUpdate:modelValue":r[0]||(r[0]=a=>x(k)?k.value=a:null)},{default:b((()=>[v(t,{value:"本月",label:"本月"}),v(t,{value:"上月",label:"上月"}),v(t,{value:"今年",label:"今年"})])),_:1},8,["modelValue"])]),v(p,{class:"table",data:f(I),pagination:!1,size:"large",border:!1,stripe:!1,"show-header-background":!1},{default:b((()=>[v(o,{label:"头像",prop:"avatar",width:"150px"},{default:b((a=>[u("div",V,[u("img",{class:"avatar",src:a.row.avatar},null,8,q),u("span",C,h(a.row.username),1)])])),_:1}),v(o,{label:"地区",prop:"province"}),v(o,{label:"性别",prop:"avatar"},{default:b((a=>[u("div",D,[u("span",E,h(1===a.row.sex?"男":"女"),1)])])),_:1}),v(o,{label:"进度",width:"240"},{default:b((a=>[v(l,{percentage:a.row.pro,color:a.row.color,"stroke-width":4},null,8,["percentage","color"])])),_:1})])),_:1},8,["data"])])}}}),[["__scopeId","data-v-8d144d52"]]);export{I as default};
