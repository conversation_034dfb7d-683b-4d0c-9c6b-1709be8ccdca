import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-C-OqvfhG.js";import{_ as t}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as e,C as r,S as a,x as i}from"./vendor-9ydHGNSq.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const o={class:"card art-custom-card",style:{height:"26rem"}};const p=t({},[["render",function(t,p){const n=s;return r(),e("div",o,[p[0]||(p[0]=a("div",{class:"card-header"},[a("p",{class:"title"},"销售趋势"),a("p",{class:"subtitle"},"月度销售对比")],-1)),i(n,{positiveData:[50,80,120,90,60],negativeData:[30,60,90,70,40],xAxisData:["一月","二月","三月","四月","五月"],height:"19rem"})])}]]);export{p as default};
