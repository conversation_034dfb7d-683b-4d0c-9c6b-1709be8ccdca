import{_ as a}from"./ArtTable-CBEgSUvC.js";import{D as o}from"./index-C5Q-N6Yp.js";/* empty css                    *//* empty css                        *//* empty css                    *//* empty css                  */import{k as r,c as s,O as e,C as t,S as p,x as l,D as i,aJ as d,bq as m,Q as n,u,X as c}from"./vendor-9ydHGNSq.js";import{_ as j}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-DiJiIjk5.js";const b={class:"custom-card art-custom-card top-products"},y={class:"custom-card-body"},g=j(r({__name:"TopProducts",setup(r){const j=s((()=>[{name:"智能手机",popularity:10,sales:"100"},{name:"笔记本电脑",popularity:29,sales:"100"},{name:"平板电脑",popularity:65,sales:"100"},{name:"智能手表",popularity:32,sales:"100"},{name:"无线耳机",popularity:78,sales:"100"},{name:"智能音箱",popularity:41,sales:"100"}])),g=a=>a<25?"#00E096":a<50?"#0095FF":a<75?"#884CFF":"#FE8F0E";return(r,s)=>{const x=d,_=m,w=a;return t(),e("div",b,[s[0]||(s[0]=p("div",{class:"custom-card-header"},[p("span",{class:"title"},"热门产品")],-1)),p("div",y,[l(w,{data:u(j),style:{width:"100%"},pagination:!1,size:"large",border:!1,stripe:!1,"show-header-background":!1},{default:i((()=>[l(x,{prop:"name",label:"产品名称",width:"200"}),l(x,{prop:"popularity",label:"销量"},{default:i((a=>[l(_,{percentage:a.row.popularity,color:g(a.row.popularity),"stroke-width":5,"show-text":!1},null,8,["percentage","color"])])),_:1}),l(x,{prop:"sales",label:"销量",width:"80"},{default:i((a=>[p("span",{style:n({color:g(a.row.popularity),backgroundColor:`rgba(${u(o)(g(a.row.popularity))}, 0.08)`,border:"1px solid",padding:"3px 6px",borderRadius:"4px",fontSize:"12px"})},c(a.row.sales),5)])),_:1})])),_:1},8,["data"])])])}}}),[["__scopeId","data-v-e10a1642"]]);export{g as default};
