var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,n=(t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s;import{_ as p}from"./ArtResultPage-BW5rW1mA.js";import"./index-C5Q-N6Yp.js";/* empty css                  */import{k as l,V as u,B as i,C as c,D as _,a2 as m,a3 as b,a6 as f,S as j}from"./vendor-9ydHGNSq.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";const y=l((d=((e,t)=>{for(var r in t||(t={}))a.call(t,r)&&n(e,r,t[r]);if(s)for(var r of s(t))o.call(t,r)&&n(e,r,t[r]);return e})({},{name:"ResultSuccess"}),t(d,r({__name:"index",setup:e=>(e,t)=>{const r=b,s=p,a=u("ripple");return c(),i(s,{type:"success",title:"提交成功",message:"提交结果页用于反馈一系列操作任务的处理结果，如果仅是简单操作，使用 Message 全局提示反馈即可。灰色区域可以显示一些补充的信息。",iconCode:""},{content:_((()=>t[0]||(t[0]=[j("p",null,"已提交申请，等待部门审核。",-1)]))),buttons:_((()=>[m((c(),i(r,{type:"primary"},{default:_((()=>t[1]||(t[1]=[f("返回修改")]))),_:1,__:[1]})),[[a]]),m((c(),i(r,null,{default:_((()=>t[2]||(t[2]=[f("查看")]))),_:1,__:[2]})),[[a]]),m((c(),i(r,null,{default:_((()=>t[3]||(t[3]=[f("打印")]))),_:1,__:[3]})),[[a]])])),_:1})}}))));var d;export{y as default};
