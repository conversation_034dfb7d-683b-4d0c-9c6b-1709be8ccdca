var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,n=(t,r,o)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o;import{_ as l}from"./ArtResultPage-BW5rW1mA.js";import"./index-C5Q-N6Yp.js";/* empty css                  */import{k as p,V as i,B as c,C as u,D as f,a2 as m,a3 as b,a6 as y,S as _}from"./vendor-9ydHGNSq.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";const j=p((d=((e,t)=>{for(var r in t||(t={}))a.call(t,r)&&n(e,r,t[r]);if(o)for(var r of o(t))s.call(t,r)&&n(e,r,t[r]);return e})({},{name:"ResultFail"}),t(d,r({__name:"index",setup:e=>(e,t)=>{const r=b,o=l,a=i("ripple");return u(),c(o,{type:"fail",title:"提交失败",message:"请核对并修改以下信息后，再重新提交。",iconCode:""},{content:f((()=>t[0]||(t[0]=[_("p",null,"您提交的内容有如下错误：",-1),_("p",null,[_("i",{class:"icon iconfont-sys"},""),y("您的账户已被冻结")],-1),_("p",null,[_("i",{class:"icon iconfont-sys"},""),y("您的账户还不具备申请资格")],-1)]))),buttons:f((()=>[m((u(),c(r,{type:"primary"},{default:f((()=>t[1]||(t[1]=[y("返回修改")]))),_:1,__:[1]})),[[a]]),m((u(),c(r,null,{default:f((()=>t[2]||(t[2]=[y("查看")]))),_:1,__:[2]})),[[a]])])),_:1})}}))));var d;export{j as default};
