import{_ as n}from"./ArtWangEditor.vue_vue_type_style_index_0_lang-BphJvq20.js";import{k as e,r as t,O as l,C as o,x as r}from"./vendor-9ydHGNSq.js";import"./index-C5Q-N6Yp.js";import"./emojo-Ben6gd8J.js";const a={class:"page-content"},i=e({__name:"index",setup(e){const i=t('<h1>欢迎使用富文本编辑器</h1>\n  <p>这是一个段落示例，下面展示了一些常用的富文本格式：</p>\n  \n  <h2>文本样式</h2>\n  <p><strong>这是加粗的文字</strong></p>\n  <p><em>这是斜体文字</em></p>\n  \n  <h2>列表示例</h2>\n  <ul>\n    <li>无序列表项 1</li>\n    <li>无序列表项 2</li>\n    <li>无序列表项 3</li>\n  </ul>\n  \n  \n  <ol>\n    <li>有序列表项 1</li>\n    <li>有序列表项 2</li>\n    <li>有序列表项 3</li>\n  </ol>\n  \n  <h2>引用示例</h2>\n  <blockquote style="border-left: 4px solid #ccc; margin-left: 0; padding-left: 1em;">\n    这是一段引用文字，可以用来突出显示重要内容。\n  </blockquote>\n  \n  <h2>表格示例</h2>\n  <table border="1" cellpadding="5">\n    <tr>\n      <th>表头 1</th>\n      <th>表头 2</th>\n    </tr>\n    <tr>\n      <td>单元格 1</td>\n      <td>单元格 2</td>\n    </tr>\n  </table>\n  \n  <h2>代码示例</h2>\n  <pre><code class="language-javascript">// JavaScript 代码示例\nfunction greeting(name) {\n    return `Hello, ${name}!`;\n}\n\nconsole.log(greeting(\'世界\'));</code></pre>\n\n<pre><code class="language-python"># Python 代码示例\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n\nprint(fibonacci(10))</code></pre>\n  \n  <h2>链接示例</h2>\n  <p><a href="https://www.lingchen.kim/art-design-pro/docs/">这是一个超链接</a></p>\n');return(e,t)=>{const c=n;return o(),l("div",a,[r(c,{modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=n=>i.value=n)},null,8,["modelValue"])])}}});export{i as default};
