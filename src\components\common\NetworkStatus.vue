<template>
  <Transition name="network-status">
    <div v-if="!networkStatus.isOnline" class="network-status-bar">
      <div class="network-status-content">
        <i class="el-icon-warning network-status-icon">&#xe67f;</i>
        <span v-if="networkStatus.retryCount < networkStatus.maxRetries">
          {{ $t('httpMsg.networkOffline') }} {{ $t('common.retrying') }} ({{
            networkStatus.retryCount
          }}/{{ networkStatus.maxRetries }})
        </span>
        <span v-else> {{ $t('httpMsg.networkOffline') }} {{ $t('common.retryFailed') }} </span>
        <ElButton
          size="small"
          type="danger"
          class="network-status-retry-button"
          @click="handleRetry"
        >
          {{ $t('common.retry') }}
        </ElButton>
      </div>
      <div class="network-status-progress">
        <div
          v-if="networkStatus.retryCount < networkStatus.maxRetries"
          class="network-status-progress-bar"
          :style="progressStyle"
        ></div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
  import { useNetworkStatus } from '@/composables/useNetworkStatus'
  import { ElButton, ElMessage } from 'element-plus'
  import { computed } from 'vue'
  import { $t } from '@/locales'

  const props = defineProps({
    onReconnect: {
      type: Function,
      default: () => {}
    }
  })

  // 网络状态检测钩子
  const networkStatus = useNetworkStatus({
    maxRetries: 3,
    retryInterval: 5000,
    onReconnect: () => {
      ElMessage.success($t('common.networkRecovered'))
      if (typeof props.onReconnect === 'function') {
        props.onReconnect()
      }
    }
  })

  // 计算进度条样式
  const progressStyle = computed(() => {
    // 计算进度百分比
    const percent =
      networkStatus.retryCount.value === 0
        ? 0
        : (networkStatus.retryCount.value / networkStatus.maxRetries) * 100

    return {
      width: `${percent}%`
    }
  })

  // 手动重试
  const handleRetry = async () => {
    const isOnline = await networkStatus.checkNetworkConnection()
    if (isOnline) {
      ElMessage.success($t('common.networkRecovered'))
      if (typeof props.onReconnect === 'function') {
        props.onReconnect()
      }
    } else {
      ElMessage.error($t('common.networkStillOffline'))
      networkStatus.startRetrying() // 重新启动重试
    }
  }
</script>

<style lang="scss" scoped>
  .network-status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    background-color: var(--el-color-danger);
    color: #fff;
    padding: 6px 12px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .network-status-content {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;

    .network-status-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    .network-status-retry-button {
      margin-left: 16px;
    }
  }

  .network-status-progress {
    height: 2px;
    background-color: rgba(255, 255, 255, 0.3);
    margin-top: 4px;

    .network-status-progress-bar {
      height: 100%;
      background-color: #fff;
      transition: width 0.3s ease;
    }
  }

  // 过渡动画
  .network-status-enter-active,
  .network-status-leave-active {
    transition:
      transform 0.3s ease,
      opacity 0.3s ease;
  }

  .network-status-enter-from,
  .network-status-leave-to {
    transform: translateY(-100%);
    opacity: 0;
  }
</style>
