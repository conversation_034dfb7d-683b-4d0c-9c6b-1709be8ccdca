<template>
  <div class="page-login">
    <!-- 登录表单区域 -->
    <div class="box">
      <!-- 右上角功能按钮 -->
      <div class="top-right-wrap">
        <!-- <div class="btn theme-btn" @click="toggleTheme">
          <i class="iconfont-sys">
            {{ isDark ? '&#xe6b5;' : '&#xe725;' }}
          </i>
        </div> -->
        <ElDropdown @command="changeLanguage" popper-class="langDropDownStyle">
          <div class="btn language-btn">
            <i class="iconfont-sys icon-language">&#xe611;</i>
          </div>
          <template #dropdown>
            <ElDropdownMenu>
              <div v-for="lang in languageOptions" :key="lang.value" class="lang-btn-item">
                <ElDropdownItem :command="lang.value" :class="{ 'is-selected': locale === lang.value }">
                  <span class="menu-txt">{{ lang.label }}</span>
                  <i v-if="locale === lang.value" class="iconfont-sys icon-check">&#xe621;</i>
                </ElDropdownItem>
              </div>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>

      <div class="logo">
        <div class="icon">
          <ArtLogo />
        </div>
        <span>{{ systemName }}</span>
      </div>

      <!-- 描述文字 -->
      <p class="desc">{{ $t('login.subTitle') }}</p>

      <!-- 登录表单 -->
      <div class="form">
        <ElForm ref="formRef" :model="formData" :rules="rules" label-position="top" class="form" :disabled="loading"
          :hide-required-asterisk="true">
          <ElFormItem label="用户名" prop="username">
            <ElInput v-model="formData.username" placeholder="请输入用户名" maxlength="20" />
          </ElFormItem>

          <ElFormItem label="密码" prop="password">
            <ElInput v-model="formData.password" type="password" placeholder="请输入密码" maxlength="20" show-password
              autocomplete="new-password" />
          </ElFormItem>

          <ElFormItem label="验证码" prop="captchaCode">
            <ElInput v-model="formData.captchaCode" placeholder="验证码" maxlength="4" @keyup.enter="handleSubmit">
              <template #suffix>
                <div class="captcha-image" @click="refreshCaptcha" title="点击刷新验证码">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" draggable="false" />
                  <ElIcon v-else class="loading-icon">
                    <Loading />
                  </ElIcon>
                </div>
              </template>
            </ElInput>
          </ElFormItem>

          <div class="op">
            <ElButton type="primary" :loading="loading" @click="handleSubmit">
              登录
            </ElButton>
          </div>
        </ElForm>
      </div>
    </div>

    <!-- 背景SVG -->
    <div class="bg">
      <img src="@/assets/images/login-bg.svg" alt="background" />
    </div>

    <!-- 版权信息 -->
    <a href="https://gitee.com/Moon__Sun" class="copyright"> Copyright © Sun </a>
  </div>
</template>

<script setup lang="ts">
import AppConfig from '@/config'
import { RoutesAlias } from '@/router/routesAlias'
import { ElNotification, ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { HOME_PAGE } from '@/router/routesAlias'
import { getCssVar } from '@/utils/ui'
import { languageOptions } from '@/locales'
import { LanguageEnum, SystemThemeEnum } from '@/enums/appEnum'
import { useI18n } from 'vue-i18n'
import { HttpError } from '@/utils/http/error'
import { Loading } from '@element-plus/icons-vue'

defineOptions({ name: 'Login' })

const { t } = useI18n()
import { useSettingStore } from '@/store/modules/setting'
import type { FormInstance, FormRules } from 'element-plus'

const settingStore = useSettingStore()
const { isDark, systemThemeType } = storeToRefs(settingStore)

const userStore = useUserStore()
const router = useRouter()

const captchaKey = ref('')
const captchaImage = ref('')

const systemName = AppConfig.systemInfo.name
const formRef = ref<FormInstance>()

const formData = reactive({
  username: '',
  password: '',
  captchaCode: '',
  rememberPassword: false
})

const rules = computed<FormRules>(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, message: '用户名至少需要2位字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: '用户名只能包含字母、数字和下划线，不能输入中文',
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少需要6位字符', trigger: 'blur' }
  ],
  captchaCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
}))

const loading = ref(false)
const { width } = useWindowSize()

// 获取验证码
const getCaptcha = async () => {
  try {
    const response = await UserService.getCaptcha()

    // 检查响应结构
    if (response && response.data) {
      captchaKey.value = response.data.captchaKey
      captchaImage.value = response.data.captchaImage
    } else if (response && response.captchaKey) {
      // 直接从响应中获取数据
      captchaKey.value = response.captchaKey
      captchaImage.value = response.captchaImage
    } else {
      ElMessage.error(t('login.captchaFailed'))
    }
  } catch (error) {
    ElMessage.error(t('login.captchaFailed'))
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
}

// 处理验证码输入 - 只允许数字
const handleCaptchaInput = (value: string) => {
  // 过滤非数字字符，只保留数字
  const numericValue = value.replace(/[^0-9]/g, '')
  // 限制最多4位
  const limitedValue = numericValue.slice(0, 4)
  // 更新表单数据
  formData.captchaCode = limitedValue
}

// 处理验证码按键输入 - 阻止非数字字符输入
const handleCaptchaKeypress = (event: KeyboardEvent) => {
  // 允许的特殊键：退格、删除、Tab、回车、方向键等
  const allowedKeys = [
    'Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight',
    'ArrowUp', 'ArrowDown', 'Home', 'End'
  ]

  // 如果是特殊键，允许通过
  if (allowedKeys.includes(event.key)) {
    return
  }

  // 如果不是数字键，阻止输入
  if (!/^[0-9]$/.test(event.key)) {
    event.preventDefault()
  }

  // 如果已经输入了4位数字，阻止继续输入
  if (formData.captchaCode.length >= 4) {
    event.preventDefault()
  }
}

onMounted(() => {
  getCaptcha()
})

// 登录
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 登录请求
    const { username, password, captchaCode } = formData

    const loginParams = {
      username,
      password,
      captchaKey: captchaKey.value,
      captchaCode
    }

    const response = await UserService.login(loginParams)

    // 从响应中提取数据
    let loginResult

    // 检查响应结构
    if (response && response.data) {
      // 嵌套结构
      loginResult = response.data
    } else if (response && response.accessToken) {
      // 直接结构
      loginResult = response
    } else {
      throw new Error('登录失败 - 响应格式不正确')
    }

    // 验证token
    if (!loginResult || !loginResult.accessToken) {
      throw new Error('登录失败 - 未收到有效的令牌')
    }

    // 存储token和用户信息
    userStore.setToken(loginResult.accessToken, loginResult.refreshToken)

    // 确保user对象存在
    if (loginResult.user) {
      // 设置用户信息，确保permissions字段能够被识别
      const userInfo = {
        ...loginResult.user,
        // 将perms字段复制到permissions字段，确保兼容性
        permissions: loginResult.user.perms || []
      }
      userStore.setUserInfo(userInfo)
    } else {
      userStore.setUserInfo({ permissions: [], roles: [] })
    }

    userStore.setLoginStatus(true)

    // 重置路由状态，确保重新加载路由
    const { resetRouterState } = await import('@/router/guards/beforeEach')
    resetRouterState(router)

    // 强制刷新菜单，确保重新获取菜单
    const { useMenuStore } = await import('@/store/modules/menu')
    const menuStore = useMenuStore()

    try {
      // 强制刷新菜单
      await menuStore.forceRefreshMenu()
    } catch (error) {
      console.warn('菜单刷新失败，但不影响登录:', error)
    }

    // 等待一个tick确保状态更新完成
    await nextTick()

    // 登录成功处理
    showLoginSuccessNotice()

    // 延迟跳转，确保菜单数据能够正确加载
    setTimeout(() => {
      router.push(HOME_PAGE)
    }, 150)
  } catch (error) {
    // 处理 HttpError
    if (error instanceof HttpError) {
      // 不再显示错误消息，因为已经在HTTP拦截器中显示了
      // 刷新验证码
      getCaptcha()
    } else {
      // 处理非 HttpError，这种情况下需要手动显示错误消息
      ElMessage.error(error instanceof Error ? error.message : t('login.failed'))
      // 刷新验证码
      getCaptcha()
    }
  } finally {
    loading.value = false
  }
}

// 登录成功提示
const showLoginSuccessNotice = () => {
  setTimeout(() => {
    // 获取用户名，优先使用name，如果不存在则使用username
    const userName = userStore.info.name || userStore.info.username || formData.username

    ElNotification({
      title: t('login.success.title'),
      type: 'success',
      duration: 2500,
      zIndex: 10000,
      message: `${t('login.success.message')}, ${userName}!`
    })
  }, 150)
}

// 切换语言
const { locale } = useI18n()

const changeLanguage = (lang: LanguageEnum) => {
  if (locale.value === lang) return
  locale.value = lang
  userStore.setLanguage(lang)
}

// 切换主题
import { useTheme } from '@/composables/useTheme'
import { UserService } from '@/api/usersApi'

const toggleTheme = () => {
  let { LIGHT, DARK } = SystemThemeEnum
  useTheme().switchThemeStyles(systemThemeType.value === LIGHT ? DARK : LIGHT)
}
</script>

<style lang="scss" scoped>
@use './index';

.captcha-container {
  display: flex;
  align-items: center;
  gap: 10px;

  .captcha-input {
    flex: 1;
  }

  .captcha-image {
    width: 120px;
    height: 40px;
    border-radius: calc(var(--custom-radius) / 3 + 2px);
    overflow: hidden;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, var(--art-gray-100), var(--art-gray-200));
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
      pointer-events: none;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      padding: 0 4px;
      filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    }

    .captcha-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: var(--art-gray-500);

      .el-icon {
        font-size: 24px;
        animation: spin 1.2s linear infinite;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
