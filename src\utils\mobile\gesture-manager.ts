/**
 * 移动端手势管理工具
 * 用于处理移动端滑动手势冲突问题
 */

export interface GestureOptions {
  // 滑动阈值
  threshold?: number
  // 是否阻止默认行为
  preventDefault?: boolean
  // 是否阻止事件冒泡
  stopPropagation?: boolean
  // 滑动方向限制
  direction?: 'horizontal' | 'vertical' | 'both'
  // 回调函数
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
}

export class GestureManager {
  private element: HTMLElement
  private options: Required<GestureOptions>
  private startX = 0
  private startY = 0
  private isDragging = false
  private isActive = true

  constructor(element: HTMLElement, options: GestureOptions = {}) {
    this.element = element
    this.options = {
      threshold: 50,
      preventDefault: true,
      stopPropagation: true,
      direction: 'both',
      onSwipeLeft: () => {},
      onSwipeRight: () => {},
      onSwipeUp: () => {},
      onSwipeDown: () => {},
      ...options
    }

    this.init()
  }

  private init() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false })
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false })
  }

  private handleTouchStart(e: TouchEvent) {
    if (!this.isActive) return

    if (this.options.stopPropagation) {
      e.stopPropagation()
    }

    this.startX = e.touches[0].clientX
    this.startY = e.touches[0].clientY
    this.isDragging = false
  }

  private handleTouchMove(e: TouchEvent) {
    if (!this.isActive) return

    if (!this.isDragging) {
      const deltaX = Math.abs(e.touches[0].clientX - this.startX)
      const deltaY = Math.abs(e.touches[0].clientY - this.startY)

      // 判断滑动方向
      const isHorizontal = deltaX > deltaY
      const shouldStart = 
        (this.options.direction === 'horizontal' && isHorizontal && deltaX > 10) ||
        (this.options.direction === 'vertical' && !isHorizontal && deltaY > 10) ||
        (this.options.direction === 'both' && (deltaX > 10 || deltaY > 10))

      if (shouldStart) {
        this.isDragging = true
        if (this.options.preventDefault) {
          e.preventDefault()
        }
        if (this.options.stopPropagation) {
          e.stopPropagation()
        }
      }
    }

    if (this.isDragging) {
      if (this.options.preventDefault) {
        e.preventDefault()
      }
      if (this.options.stopPropagation) {
        e.stopPropagation()
      }
    }
  }

  private handleTouchEnd(e: TouchEvent) {
    if (!this.isActive) return

    if (this.options.stopPropagation) {
      e.stopPropagation()
    }

    if (!this.isDragging) return

    if (this.options.preventDefault) {
      e.preventDefault()
    }

    const deltaX = e.changedTouches[0].clientX - this.startX
    const deltaY = e.changedTouches[0].clientY - this.startY

    // 检查是否超过阈值
    if (Math.abs(deltaX) > this.options.threshold) {
      if (deltaX > 0) {
        this.options.onSwipeRight()
      } else {
        this.options.onSwipeLeft()
      }
    }

    if (Math.abs(deltaY) > this.options.threshold) {
      if (deltaY > 0) {
        this.options.onSwipeDown()
      } else {
        this.options.onSwipeUp()
      }
    }

    this.isDragging = false
  }

  // 启用手势
  public enable() {
    this.isActive = true
  }

  // 禁用手势
  public disable() {
    this.isActive = false
  }

  // 销毁手势管理器
  public destroy() {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this))
  }
}

/**
 * 防止浏览器默认的左滑返回手势
 * @param element 要保护的元素
 */
export function preventBrowserSwipeBack(element: HTMLElement) {
  const gestureManager = new GestureManager(element, {
    direction: 'horizontal',
    preventDefault: true,
    stopPropagation: true,
    threshold: 30
  })

  return gestureManager
}

/**
 * 为滑动容器添加手势保护
 * @param selector CSS选择器
 */
export function protectSwipeContainers(selector: string = '.swipe-container, .mobile-cards-wrapper, .mobile-swiper-container') {
  const containers = document.querySelectorAll(selector)
  const managers: GestureManager[] = []

  containers.forEach(container => {
    if (container instanceof HTMLElement) {
      const manager = preventBrowserSwipeBack(container)
      managers.push(manager)
    }
  })

  return managers
}
