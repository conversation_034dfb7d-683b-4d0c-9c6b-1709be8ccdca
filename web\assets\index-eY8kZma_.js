import"./index-C5Q-N6Yp.js";/* empty css                    *//* empty css                  *//* empty css                     *//* empty css                  */import{e}from"./iconfont-DPUoc2h2.js";import{k as l,r as a,d as s,O as o,C as n,S as t,x as c,D as u,F as i,Z as d,am as r,i as m,u as p,al as v,aF as f,Q as b,W as y,X as h,E as x}from"./vendor-9ydHGNSq.js";import{_ as j}from"./_plugin-vue_export-helper-BCo6x5W8.js";const k={class:"page-content"},C={class:"form"},_={class:"colors-icon"},V={class:"list"},g={class:"icon-list"},M=["onClick"],N=["innerHTML"],D=j(l({__name:"index",setup(l){const j=a("unicode"),D=[{value:"unicode",label:"Unicode"},{value:"fontClass",label:"Font class"}],E=a([]),F=a(!1);s((()=>{E.value=e()}));const U=()=>{const e=["#2d8cf0","#19be6b","#ff9900","#f24965","#9463f7"];return e[Math.floor(Math.random()*e.length)]},H=()=>F.value?{color:U()}:{color:"var(--art-text-gray-700)"};return(e,l)=>{const a=r,s=v,U=f;return n(),o("div",k,[t("div",C,[c(s,{modelValue:p(j),"onUpdate:modelValue":l[0]||(l[0]=e=>m(j)?j.value=e:null),placeholder:"Select",style:{width:"240px"}},{default:u((()=>[(n(),o(i,null,d(D,(e=>c(a,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),t("div",_,[c(U,{modelValue:p(F),"onUpdate:modelValue":l[1]||(l[1]=e=>m(F)?F.value=e:null),label:"彩色图标"},null,8,["modelValue"])])]),t("div",V,[t("ul",g,[(n(!0),o(i,null,d(p(E),(e=>(n(),o("li",{key:e.className,onClick:l=>(e=>{if(!e)return;let l=document.createElement("input");l.setAttribute("value",("unicode"===j.value?e.unicode:e.className)||""),document.body.appendChild(l),l.select(),document.execCommand("copy"),document.body.removeChild(l),x.success("已复制")})(e)},["unicode"===p(j)?(n(),o("i",{key:0,class:"iconfont-sys",innerHTML:e.unicode,style:b(H())},null,12,N)):(n(),o("i",{key:1,class:y(`iconfont-sys ${e.className}`),style:b(H())},null,6)),t("span",null,h("unicode"===p(j)?e.unicode:e.className),1)],8,M)))),128))])])])}}}),[["__scopeId","data-v-1353bda8"]]);export{D as default};
