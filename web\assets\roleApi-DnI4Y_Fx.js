import{a as e}from"./index-DiJiIjk5.js";import{c as s}from"./vendor-9ydHGNSq.js";import{q as r}from"./index-C5Q-N6Yp.js";const t=()=>{const{width:r,height:t}=e(),a={xs:480,sm:768,md:992,lg:1200,xl:1920},l=s((()=>{const e=r.value;return e<a.xs?"xs":e<a.sm?"sm":e<a.md?"md":e<a.lg?"lg":e<a.xl?"xl":"xxl"})),o=s((()=>r.value<a.sm)),u=s((()=>r.value>=a.sm&&r.value<a.lg)),i=s((()=>r.value>=a.lg)),n=s((()=>r.value>t.value)),m=s((()=>t.value>r.value));return{width:r,height:t,currentBreakpoint:l,isMobile:o,isTablet:u,isDesktop:i,isLandscape:n,isPortrait:m,isBreakpoint:e=>s((()=>r.value>=a[e])),breakpoints:a}};class a{static getRoles(e={page:1,limit:10}){return r.get({url:"/users/roles",params:e})}static createRole(e){return r.post({url:"/users/roles",data:e,showErrorMessage:!0})}static updateRole(e,s){return r.put({url:`/users/roles/${e}`,data:s,showErrorMessage:!0})}static deleteRole(e){return r.del({url:`/users/roles/${e}`,showErrorMessage:!0})}static getRole(e){return r.get({url:`/users/roles/${e}`})}}export{a as R,t as u};
