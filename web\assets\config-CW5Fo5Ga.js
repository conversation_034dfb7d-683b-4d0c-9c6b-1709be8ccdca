var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,s=(a,t,o)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o,i=(e,a,t)=>new Promise(((o,l)=>{var n=e=>{try{i(t.next(e))}catch(a){l(a)}},s=e=>{try{i(t.throw(e))}catch(a){l(a)}},i=e=>e.done?o(e.value):Promise.resolve(e.value).then(n,s);i((t=t.apply(e,a)).next())}));import{P as r,a as c}from"./PackageTypeManager-D6ClJz-t.js";import{E as u,C as v}from"./ExchangeOptionManager-DPBEYdCn.js";import{M as p,a as d}from"./MemoryCardManager-VB7P5bqP.js";import{k as m,r as g,w as y,c as h,d as f,n as k,O as b,C as j,S as C,F as w,Z as x,W as T,Q as M,X as O,R as P,B as _}from"./vendor-9ydHGNSq.js";import{_ as D}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-C5Q-N6Yp.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css                         */import"./ArtTable-CBEgSUvC.js";/* empty css                      *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-DiJiIjk5.js";/* empty css                  */import"./useForm-CMcsaOzW.js";import"./useDebounce-C8earYBB.js";import"./index-DEP0vMzR.js";const E={class:"config-management"},X={class:"config-layout"},I={class:"config-sidebar"},$={class:"sidebar-nav"},W=["onClick"],F={class:"icon-emoji"},Y={class:"nav-content"},z={class:"nav-title"},A={class:"nav-count"},B={class:"config-main"},Q={key:0,class:"mobile-swipe-header"},S=["onClick"],Z={class:"swipe-item-content"},q={class:"icon-emoji"},L={class:"swipe-content"},R={class:"swipe-title"},U={class:"swipe-description"},V={class:"swipe-indicators"},G=["onClick"],H={key:1,class:"content-header"},J={class:"content-title"},K={class:"content-description"},N={class:"content-body"},ee=m((ae=((e,a)=>{for(var t in a||(a={}))l.call(a,t)&&s(e,t,a[t]);if(o)for(var t of o(a))n.call(a,t)&&s(e,t,a[t]);return e})({},{name:"ConfigManagement"}),a(ae,t({__name:"config",setup(e,{expose:a}){const t=g("package"),o=g(!1),l=g(),n=g(0),s=g(0),m=g(0),D=g(0),ee=g(!1),ae=()=>{o.value=window.innerWidth<=768},te=(e,a)=>{t.value=e,s.value=a,oe()};y(t,(e=>{if(o.value){const a=ie.value.findIndex((a=>a.key===e));-1!==a&&a!==s.value&&(s.value=a,k((()=>{oe()})))}}));const oe=()=>{if(!l.value)return;const e=window.innerWidth,a=-s.value*e,t=-(ie.value.length-1)*e;n.value=Math.max(Math.min(a,0),t)},le=e=>{m.value=e.touches[0].clientX,D.value=e.touches[0].clientY,ee.value=!1},ne=e=>{if(!ee.value){const a=Math.abs(e.touches[0].clientX-m.value);a>Math.abs(e.touches[0].clientY-D.value)&&a>10&&(ee.value=!0,e.preventDefault())}},se=e=>{if(!ee.value)return;const a=e.changedTouches[0].clientX-m.value;Math.abs(a)>50&&(a>0&&s.value>0?(s.value--,t.value=ie.value[s.value].key,oe()):a<0&&s.value<ie.value.length-1&&(s.value++,t.value=ie.value[s.value].key,oe())),ee.value=!1},ie=g([{key:"package",title:"套餐类型",description:"管理各种套餐类型的配置信息",icon:"📦",color:"#667eea",bgColor:"#eff6ff",count:0},{key:"exchange",title:"随心换选项",description:"管理随心换服务的配置选项",icon:"🔄",color:"#10b981",bgColor:"#ecfdf5",count:0},{key:"memory",title:"内存卡管理",description:"管理内存卡相关的配置信息",icon:"💾",color:"#f59e0b",bgColor:"#fffbeb",count:0}]),re=h((()=>ie.value.find((e=>e.key===t.value)))),ce=()=>i(this,null,(function*(){try{const[e,a,t]=yield Promise.all([c.getPackageTypes().catch((()=>[])),v.getChangeOptions().catch((()=>[])),d.getMemoryCards().catch((()=>[]))]),o=ie.value.find((e=>"package"===e.key));o&&(o.count=e.length);const l=ie.value.find((e=>"exchange"===e.key));l&&(l.count=a.length);const n=ie.value.find((e=>"memory"===e.key));n&&(n.count=t.length)}catch(e){}})),ue=e=>i(this,null,(function*(){try{let a=0;switch(e){case"package":a=(yield c.getPackageTypes()).length;break;case"exchange":a=(yield v.getChangeOptions()).length;break;case"memory":a=(yield d.getMemoryCards()).length}const t=ie.value.find((a=>a.key===e));t&&(t.count=a)}catch(a){}}));return f((()=>{ce(),ae();const e=((e,a)=>{let t;return function(...o){clearTimeout(t),t=setTimeout((()=>{clearTimeout(t),e(...o)}),a)}})((()=>{ae(),o.value&&k((()=>{setTimeout((()=>{oe()}),50)}))}),100);window.addEventListener("resize",e),o.value&&k((()=>{setTimeout((()=>{const e=ie.value.findIndex((e=>e.key===t.value));-1!==e&&(s.value=e),oe()}),150)}))})),a({refreshCategoryCount:ue,refreshCurrentCategoryCount:()=>{ue(t.value)},loadDataCounts:ce}),(e,a)=>{var s,i;return j(),b("div",E,[a[5]||(a[5]=C("div",{class:"page-header"},[C("h2",{class:"page-title"},"配置管理"),C("p",{class:"page-description"},"管理套餐类型、随心换选项、内存卡等配置信息")],-1)),C("div",X,[C("div",I,[a[4]||(a[4]=C("div",{class:"sidebar-header"},[C("h3",null,"配置分类")],-1)),C("nav",$,[(j(!0),b(w,null,x(ie.value,(e=>(j(),b("div",{key:e.key,class:T(["nav-item",{active:t.value===e.key}]),onClick:a=>t.value=e.key},[C("div",{class:"nav-icon",style:M({backgroundColor:e.bgColor,color:e.color})},[C("span",F,O(e.icon),1)],4),C("div",Y,[C("span",z,O(e.title),1),C("span",A,O(e.count),1)]),a[3]||(a[3]=C("div",{class:"nav-indicator"},null,-1))],10,W)))),128))])]),C("div",B,[o.value?(j(),b("div",Q,[C("div",{class:"swipe-container",ref_key:"swipeContainer",ref:l,onTouchstart:le,onTouchmove:ne,onTouchend:se},[C("div",{class:"swipe-wrapper",style:M({transform:`translateX(${n.value}px)`})},[(j(!0),b(w,null,x(ie.value,((e,a)=>(j(),b("div",{key:e.key,class:T(["swipe-item",{active:t.value===e.key}]),onClick:t=>te(e.key,a)},[C("div",Z,[C("div",{class:"swipe-icon",style:M({backgroundColor:e.bgColor,color:e.color})},[C("span",q,O(e.icon),1)],4),C("div",L,[C("h3",R,O(e.title),1),C("p",U,O(e.description),1)])])],10,S)))),128))],4)],544),C("div",V,[(j(!0),b(w,null,x(ie.value,((e,a)=>(j(),b("span",{key:e.key,class:T(["indicator",{active:t.value===e.key}]),onClick:t=>te(e.key,a)},null,10,G)))),128))])])):P("",!0),o.value?P("",!0):(j(),b("div",H,[C("h2",J,O(null==(s=re.value)?void 0:s.title),1),C("p",K,O(null==(i=re.value)?void 0:i.description),1)])),C("div",N,["package"===t.value?(j(),_(r,{key:`package-${o.value}`,onDataChanged:a[0]||(a[0]=()=>ue("package"))})):P("",!0),"exchange"===t.value?(j(),_(u,{key:`exchange-${o.value}`,onDataChanged:a[1]||(a[1]=()=>ue("exchange"))})):P("",!0),"memory"===t.value?(j(),_(p,{key:`memory-${o.value}`,onDataChanged:a[2]||(a[2]=()=>ue("memory"))})):P("",!0)])])])])}}}))));var ae;const te=D(ee,[["__scopeId","data-v-0d82f94b"]]);export{te as default};
