var e=Object.defineProperty,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,n=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l,o=(e,o)=>{for(var d in o||(o={}))t.call(o,d)&&n(e,d,o[d]);if(a)for(var d of a(o))l.call(o,d)&&n(e,d,o[d]);return e};import"./index-C5Q-N6Yp.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                        */import{k as d,r,c as u,O as s,C as p,x as i,D as c,S as m,W as v,X as y,F as b,Z as g,a1 as f,i as D,u as _,bG as j,ab as V,ac as w,$ as Y,aQ as k,aR as h,a6 as C,aU as M,B as x,R as U,a3 as O,ae as $}from"./vendor-9ydHGNSq.js";import{_ as q}from"./_plugin-vue_export-helper-BCo6x5W8.js";const I={class:"page-content"},P=["onClick"],S={class:"calendar-date"},B={class:"calendar-events"},E=["onClick"],R={class:"dialog-footer"},z=q(d({__name:"index",setup(e){const a=[{label:"基本",value:"bg-primary"},{label:"成功",value:"bg-success"},{label:"警告",value:"bg-warning"},{label:"危险",value:"bg-danger"}],t=r(new Date("2025-02-07")),l=r([{date:"2025-02-01",content:"产品需求评审",type:"bg-primary"},{date:"2025-02-03",endDate:"2025-02-05",content:"项目周报会议（跨日期）",type:"bg-primary"},{date:"2025-02-10",content:"瑜伽课程",type:"bg-success"},{date:"2025-02-15",content:"团队建设活动",type:"bg-primary"},{date:"2025-02-20",content:"健身训练",type:"bg-success"},{date:"2025-02-20",content:"代码评审",type:"bg-danger"},{date:"2025-02-20",content:"团队午餐",type:"bg-primary"},{date:"2025-02-20",content:"项目进度汇报",type:"bg-warning"},{date:"2025-02-28",content:"月度总结会",type:"bg-warning"}]),n=r(!1),d=r("添加事件"),q=r(-1),z=r({date:"",endDate:"",content:"",type:"bg-primary"}),A=u((()=>q.value>=0)),F=()=>{z.value={date:"",endDate:"",content:"",type:"bg-primary"},q.value=-1},G=()=>{z.value.content&&z.value.date&&(A.value?l.value[q.value]=o({},z.value):l.value.push(o({},z.value)),n.value=!1,F())},Q=()=>{A.value&&(l.value.splice(q.value,1),n.value=!1,F())};return(e,r)=>{const u=j,W=Y,X=w,Z=h,H=k,J=M,K=V,L=O,N=$;return p(),s("div",I,[i(u,{modelValue:_(t),"onUpdate:modelValue":r[0]||(r[0]=e=>D(t)?t.value=e:null)},{"date-cell":c((({data:e})=>{return[m("div",{class:v(["calendar-cell",{"is-selected":e.isSelected}]),onClick:a=>{return t=e.day,d.value="添加事件",z.value={date:t,content:"",type:"bg-primary"},q.value=-1,void(n.value=!0);var t}},[m("p",S,y((t=e.day,t.split("-")[2])),1),m("div",B,[(p(!0),s(b,null,g((a=e.day,l.value.filter((e=>{const t=new Date(e.date),l=new Date(a),n=e.endDate?new Date(e.endDate):new Date(e.date);return l>=t&&l<=n}))),(e=>(p(),s("div",{key:`${e.date}-${e.content}`,class:"calendar-event",onClick:f((a=>(e=>{d.value="编辑事件",z.value=o({},e),q.value=l.value.findIndex((a=>a.date===e.date&&a.content===e.content)),n.value=!0})(e)),["stop"])},[m("div",{class:v(["event-tag",[`${e.type||"bg-primary"}`]])},y(e.content),3)],8,E)))),128))])],10,P)];var a,t})),_:1},8,["modelValue"]),i(N,{modelValue:_(n),"onUpdate:modelValue":r[5]||(r[5]=e=>D(n)?n.value=e:null),title:_(d),width:"600px",onClosed:F},{footer:c((()=>[m("span",R,[_(A)?(p(),x(L,{key:0,type:"danger",onClick:Q},{default:c((()=>r[6]||(r[6]=[C(" 删除 ")]))),_:1,__:[6]})):U("",!0),i(L,{type:"primary",onClick:G},{default:c((()=>[C(y(_(A)?"更新":"添加"),1)])),_:1})])])),default:c((()=>[i(K,{model:_(z),"label-width":"80px"},{default:c((()=>[i(X,{label:"活动标题",required:""},{default:c((()=>[i(W,{modelValue:_(z).content,"onUpdate:modelValue":r[1]||(r[1]=e=>_(z).content=e),placeholder:"请输入活动标题"},null,8,["modelValue"])])),_:1}),i(X,{label:"事件颜色"},{default:c((()=>[i(H,{modelValue:_(z).type,"onUpdate:modelValue":r[2]||(r[2]=e=>_(z).type=e)},{default:c((()=>[(p(),s(b,null,g(a,(e=>i(Z,{key:e.value,value:e.value},{default:c((()=>[C(y(e.label),1)])),_:2},1032,["value"]))),64))])),_:1},8,["modelValue"])])),_:1}),i(X,{label:"开始日期",required:""},{default:c((()=>[i(J,{style:{width:"100%"},modelValue:_(z).date,"onUpdate:modelValue":r[3]||(r[3]=e=>_(z).date=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),i(X,{label:"结束日期"},{default:c((()=>[i(J,{style:{width:"100%"},modelValue:_(z).endDate,"onUpdate:modelValue":r[4]||(r[4]=e=>_(z).endDate=e),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","min-date":_(z).date},null,8,["modelValue","min-date"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-d442f1d9"]]);export{z as default};
