var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,s=(t,r,a)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a,c=(e,t)=>{for(var r in t||(t={}))o.call(t,r)&&s(e,r,t[r]);if(a)for(var r of a(t))i.call(t,r)&&s(e,r,t[r]);return e},l=(e,a)=>t(e,r(a));import"./index-C5Q-N6Yp.js";/* empty css                *//* empty css                        */import{k as n,O as d,C as u,x as g,b7 as p,D as m,S as h,R as v,bm as b,Y as f,u as y,a5 as C,X as w,a6 as _,b2 as x,bH as k,Q as B,W as S,r as A,d as j,w as z,B as D,bq as R,F as V,Z as M,aY as L,aX as T}from"./vendor-9ydHGNSq.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{_ as U}from"./index-ArFw1yvF.js";import{_ as I}from"./index-Dgm4PPPz.js";import{a as W,u as P}from"./useChart-CoPw7zBp.js";import{_ as q}from"./index-CoyPwaSJ.js";/* empty css                    */import{C as E}from"./vue3-count-to.esm-Dsfbf00q.js";/* empty css               *//* empty css               *//* empty css                     *//* empty css                  */import"./index-DEP0vMzR.js";const H={class:"image-wrapper"},$={class:"image-placeholder"},X={key:0,class:"read-time"},F={class:"content"},Y={key:0,class:"category"},J={class:"title"},Q={class:"stats"},Z={key:0,class:"views"},G={key:1,class:"comments"},K={class:"date"},N=O(n(l(c({},{name:"ArtImageCard"}),{__name:"index",props:{imageUrl:{},title:{},category:{},readTime:{},views:{},comments:{},date:{}},emits:["click"],setup(e,{emit:t}){const r=e,a=t,o=()=>{a("click",r)};return(e,t)=>{const a=f,i=b,s=p;return u(),d("div",{class:"image-card",onClick:o},[g(s,{"body-style":{padding:"0px"},shadow:"hover",class:"art-custom-card"},{default:m((()=>[h("div",H,[g(i,{src:r.imageUrl,fit:"cover",loading:"lazy"},{placeholder:m((()=>[h("div",$,[g(a,null,{default:m((()=>[g(y(C))])),_:1})])])),_:1},8,["src"]),r.readTime?(u(),d("div",X,w(r.readTime)+" 阅读 ",1)):v("",!0)]),h("div",F,[r.category?(u(),d("div",Y,w(r.category),1)):v("",!0),h("p",J,w(r.title),1),h("div",Q,[r.views?(u(),d("span",Z,[g(a,null,{default:m((()=>[g(y(x))])),_:1}),_(" "+w(r.views),1)])):v("",!0),r.comments?(u(),d("span",G,[g(a,null,{default:m((()=>[g(y(k))])),_:1}),_(" "+w(r.comments),1)])):v("",!0),h("span",K,w(r.date),1)])])])),_:1})])}}})),[["__scopeId","data-v-7fb70d89"]]),ee={class:"card-body"},te={class:"card-content"},re={class:"data-section"},ae={class:"title"},oe={class:"value"},ie={key:0},se={class:"chart-legend"},ce={key:0,class:"legend-item current"},le={key:1,class:"legend-item previous"},ne={class:"chart-section"},de=O(n(l(c({},{name:"ArtDonutChartCard"}),{__name:"index",props:{value:{},title:{},percentage:{},percentageLabel:{},currentValue:{},previousValue:{},height:{default:9},color:{},radius:{default:()=>["70%","90%"]},data:{default:()=>[0,0]}},setup(e){const t=e,{chartRef:r}=W({props:{height:`${t.height}rem`,loading:!1,isEmpty:t.data.every((e=>0===e))},checkEmpty:()=>t.data.every((e=>0===e)),watchSources:[()=>t.data,()=>t.color,()=>t.radius,()=>t.currentValue,()=>t.previousValue],generateOptions:()=>{const e=t.color||P().themeColor;return{series:[{type:"pie",radius:t.radius,avoidLabelOverlap:!1,label:{show:!1},data:[{value:t.data[0],name:t.currentValue,itemStyle:{color:e}},{value:t.data[1],name:t.previousValue,itemStyle:{color:"#e6e8f7"}}]}]}}});return(e,t)=>{return u(),d("div",{class:"donut-chart-card art-custom-card",style:B({height:`${e.height}rem`})},[h("div",ee,[h("div",te,[h("div",re,[h("p",ae,w(e.title),1),h("div",null,[h("p",oe,w((a=e.value,a.toLocaleString())),1),h("div",{class:S(["percentage",{"is-increase":e.percentage>0}])},[_(w(e.percentage>0?"+":"")+w(e.percentage)+"% ",1),e.percentageLabel?(u(),d("span",ie,w(e.percentageLabel),1)):v("",!0)],2)]),h("div",se,[e.currentValue?(u(),d("span",ce,w(e.currentValue),1)):v("",!0),e.previousValue?(u(),d("span",le,w(e.previousValue),1)):v("",!0)])]),h("div",ne,[h("div",{ref_key:"chartRef",ref:r,class:"chart-container"},null,512)])])])],4);var a}}})),[["__scopeId","data-v-1b97aacb"]]),ue={class:"card-body"},ge={class:"chart-header"},pe={class:"metric"},me={class:"value"},he={class:"label"},ve=O(n(l(c({},{name:"ArtBarChartCard"}),{__name:"index",props:{value:{},label:{},percentage:{},date:{},height:{default:11},color:{},chartData:{},barWidth:{default:"26%"},isMiniChart:{type:Boolean}},setup(e){var t;const r=e,{chartRef:a}=W({props:{height:`${r.height}rem`,loading:!1,isEmpty:!(null==(t=r.chartData)?void 0:t.length)||r.chartData.every((e=>0===e))},checkEmpty:()=>{var e;return!(null==(e=r.chartData)?void 0:e.length)||r.chartData.every((e=>0===e))},watchSources:[()=>r.chartData,()=>r.color,()=>r.barWidth],generateOptions:()=>{const e=r.color||P().themeColor;return{grid:{top:0,right:0,bottom:15,left:0},xAxis:{type:"category",show:!1},yAxis:{type:"value",show:!1},series:[{data:r.chartData,type:"bar",barWidth:r.barWidth,itemStyle:{color:e,borderRadius:2}}]}}});return(e,t)=>(u(),d("div",{class:"bar-chart-card art-custom-card",style:B({height:`${e.height}rem`})},[h("div",ue,[h("div",ge,[h("div",pe,[h("p",me,w(e.value),1),h("p",he,w(e.label),1)]),h("div",{class:S(["percentage",{"is-increase":e.percentage>0,"is-mini-chart":e.isMiniChart}])},w(e.percentage>0?"+":"")+w(e.percentage)+"% ",3),e.date?(u(),d("div",{key:0,class:S(["date",{"is-mini-chart":e.isMiniChart}])},w(e.date),3)):v("",!0)]),h("div",{ref_key:"chartRef",ref:a,class:S(["chart-container",{"is-mini-chart":e.isMiniChart}]),style:B({height:`calc(${e.height}rem - 5rem)`})},null,6)])],4))}})),[["__scopeId","data-v-d2c9c0f3"]]),be={class:"progress-card art-custom-card"},fe={class:"left"},ye=["innerHTML"],Ce={class:"right"},we={class:"title"},_e=O(n(l(c({},{name:"ArtProgressCard"}),{__name:"index",props:{percentage:{},title:{},color:{default:"#67C23A"},icon:{},iconColor:{},iconBgColor:{},iconBgRadius:{default:8},iconSize:{},strokeWidth:{default:5}},setup(e){const t=e,r=A(0),a=()=>{const e=Date.now(),a=r.value,o=t.percentage,i=()=>{const t=Date.now()-e,s=Math.min(t/500,1);r.value=a+(o-a)*s,s<1&&requestAnimationFrame(i)};requestAnimationFrame(i)};return j((()=>{a()})),z((()=>t.percentage),(()=>{a()})),(e,t)=>{const a=R;return u(),d("div",be,[h("div",{class:"progress-info",style:B({justifyContent:e.icon?"space-between":"flex-start"})},[h("div",fe,[e.icon?(u(),d("i",{key:0,class:"iconfont-sys",innerHTML:e.icon,style:B({color:e.iconColor,backgroundColor:e.iconBgColor,fontSize:e.iconSize+"px",borderRadius:e.iconBgRadius+"px"})},null,12,ye)):v("",!0)]),h("div",Ce,[(u(),D(y(E),{key:e.percentage,class:"percentage",style:B({textAlign:e.icon?"right":"left"}),endVal:e.percentage,duration:2e3,suffix:"%"},null,8,["style","endVal"])),h("p",we,w(e.title),1)])],4),g(a,{percentage:y(r),"stroke-width":e.strokeWidth,"show-text":!1,color:e.color},null,8,["percentage","stroke-width","color"])])}}})),[["__scopeId","data-v-21a9c442"]]),xe=["innerHTML"],ke={class:"stats-card__content"},Be={key:1,class:"stats-card__arrow"},Se=O(n(l(c({},{name:"ArtStatsCard"}),{__name:"index",props:{icon:{},title:{},count:{},description:{},iconColor:{},iconBgColor:{},iconBgRadius:{default:50},iconSize:{default:30},textColor:{},backgroundColor:{},showArrow:{type:Boolean}},setup:e=>(e,t)=>(u(),d("div",{class:"stats-card art-custom-card",style:B({backgroundColor:e.backgroundColor})},[e.icon?(u(),d("div",{key:0,class:"stats-card__icon",style:B({backgroundColor:e.iconBgColor,borderRadius:e.iconBgRadius+"px"})},[h("i",{class:"iconfont-sys",innerHTML:e.icon,style:B({color:e.iconColor,fontSize:e.iconSize+"px"})},null,12,xe)],4)):v("",!0),h("div",ke,[e.title?(u(),d("p",{key:0,class:"stats-card__title",style:B({color:e.textColor})},w(e.title),5)):v("",!0),e.count?(u(),D(y(E),{key:1,class:"stats-card__count",endVal:e.count,duration:1e3},null,8,["endVal"])):v("",!0),e.description?(u(),d("p",{key:2,class:"stats-card__description",style:B({color:e.textColor})},w(e.description),5)):v("",!0)]),e.showArrow?(u(),d("div",Be,t[0]||(t[0]=[h("i",{class:"iconfont-sys"},"",-1)]))):v("",!0)],4))})),[["__scopeId","data-v-a1f91aff"]]),Ae=""+new URL("img1-D1-edq2V.webp",import.meta.url).href,je=""+new URL("img2-B4YC9Se8.webp",import.meta.url).href,ze=""+new URL("img3-2dukXPv_.webp",import.meta.url).href,De=""+new URL("img4-puXkhAjD.webp",import.meta.url).href,Re={class:"cards"},Ve=O(n({__name:"index",setup(e){const t=[{id:1,title:"销售产品",count:1235,description:"鞋子、牛仔裤、派对服装、手表",icon:"&#xe812;",iconColor:"rgb(var(--art-primary))",iconSize:20,iconBgColor:"rgb(var(--art-info))",textColor:"rgb(var(--art-primary))",backgroundColor:"rgb(var(--art-bg-primary))",showArrow:!1},{id:2,title:"活跃用户",count:5e3,description:"日活跃用户超过5,000+",icon:"&#xe724;",iconColor:"rgb(var(--art-warning))",iconSize:20,iconBgColor:"rgb(var(--art-success))",textColor:"rgb(var(--art-warning))",backgroundColor:"rgb(var(--art-bg-warning))",showArrow:!1},{id:3,title:"总收入",count:35e3,description:"月收入超过¥350,000+",icon:"&#xe70e;",iconColor:"rgb(var(--art-secondary))",iconSize:20,iconBgColor:"rgb(var(--art-secondary))",textColor:"rgb(var(--art-secondary))",backgroundColor:"rgb(var(--art-bg-secondary))",showArrow:!1},{id:4,title:"客户评价",count:4800,description:"平均评分4.8/5",icon:"&#xe82d;",iconColor:"rgb(var(--art-error))",iconSize:20,iconBgColor:"rgb(var(--art-error))",textColor:"rgb(var(--art-error))",backgroundColor:"rgb(var(--art-bg-error))",showArrow:!1}],r=[{id:1,title:"完成进度",percentage:75,color:"rgb(var(--art-success))",icon:"&#xe812;",iconColor:"rgb(var(--art-success))",iconBgColor:"rgb(var(--art-bg-success))",iconSize:20},{id:2,title:"项目进度",percentage:65,color:"rgb(var(--art-primary))",icon:"&#xe724;",iconColor:"rgb(var(--art-primary))",iconBgColor:"rgb(var(--art-bg-primary))",iconSize:20},{id:3,title:"学习进度",percentage:45,color:"rgb(var(--art-error))",icon:"&#xe724;",iconColor:"rgb(var(--art-error))",iconBgColor:"rgb(var(--art-bg-error))",iconSize:20},{id:4,title:"任务进度",percentage:90,color:"rgb(var(--art-secondary))",icon:"&#xe724;",iconColor:"rgb(var(--art-secondary))",iconBgColor:"rgb(var(--art-bg-secondary))",iconSize:20}],a=[{id:1,imageUrl:Ae,title:"AI技术在医疗领域的创新应用与发展前景",category:"社交",readTime:"2分钟",views:9125,comments:3,date:"12月19日 周一"},{id:2,imageUrl:je,title:"大数据分析助力企业决策的实践案例",category:"技术",readTime:"3分钟",views:7234,comments:5,date:"12月20日 周二"},{id:3,imageUrl:ze,title:"区块链技术在供应链管理中的应用",category:"科技",readTime:"4分钟",views:5678,comments:8,date:"12月21日 周三"},{id:4,imageUrl:De,title:"云计算技术发展趋势与未来展望",category:"云技术",readTime:"5分钟",views:4321,comments:6,date:"12月22日 周四"}],o=[{title:"新加坡之行",status:"进行中",time:"5分钟",class:"bg-primary",icon:"&#xe6f2;"},{title:"归档数据",status:"进行中",time:"10分钟",class:"bg-secondary",icon:"&#xe806;"},{title:"客户会议",status:"待处理",time:"15分钟",class:"bg-warning",icon:"&#xe6fb;"},{title:"筛选任务团队",status:"进行中",time:"20分钟",class:"bg-danger",icon:"&#xe813;"},{title:"发送信封给小王",status:"已完成",time:"20分钟",class:"bg-success",icon:"&#xe70c;"}],i=[{time:"上午 09:30",status:"rgb(73, 190, 255)",content:"收到 John Doe 支付的 385.90 美元"},{time:"上午 10:00",status:"rgb(54, 158, 255)",content:"新销售记录",code:"ML-3467"},{time:"上午 12:00",status:"rgb(103, 232, 207)",content:"向 Michael 支付了 64.95 美元"},{time:"下午 14:30",status:"rgb(255, 193, 7)",content:"系统维护通知",code:"MT-2023"},{time:"下午 15:45",status:"rgb(255, 105, 105)",content:"紧急订单取消提醒",code:"OR-9876"},{time:"下午 17:00",status:"rgb(103, 232, 207)",content:"完成每日销售报表"}],s=()=>{};return(e,c)=>{const l=Se,n=L,p=T,v=_e,b=q,f=ve,y=de,C=I,w=U,_=N;return u(),d("div",Re,[c[0]||(c[0]=h("h1",{class:"page-title"},"统计卡片（文字）",-1)),g(p,{gutter:20},{default:m((()=>[(u(),d(V,null,M(t,(e=>g(n,{xs:24,sm:12,md:6,key:e.id},{default:m((()=>[g(l,{icon:e.icon,title:e.title,description:e.description,iconSize:e.iconSize,iconBgRadius:8,iconColor:"#fff",iconBgColor:e.iconBgColor,showArrow:e.showArrow},null,8,["icon","title","description","iconSize","iconBgColor","showArrow"])])),_:2},1024))),64))])),_:1}),c[1]||(c[1]=h("h1",{class:"page-title"},"统计卡片（数字滚动）",-1)),g(p,{gutter:20},{default:m((()=>[(u(),d(V,null,M(t,(e=>g(n,{xs:24,sm:12,md:6,key:e.id},{default:m((()=>[g(l,{icon:e.icon,count:e.count,description:e.description,iconSize:e.iconSize,iconColor:"#fff",iconBgColor:e.iconBgColor,showArrow:e.showArrow},null,8,["icon","count","description","iconSize","iconBgColor","showArrow"])])),_:2},1024))),64))])),_:1}),c[2]||(c[2]=h("h1",{class:"page-title"},"统计卡片（自定义样式）",-1)),g(p,{gutter:20},{default:m((()=>[(u(),d(V,null,M(t,(e=>g(n,{xs:24,sm:12,md:6,key:e.id},{default:m((()=>[g(l,{icon:e.icon,title:e.title,description:e.description,iconColor:e.iconColor,textColor:e.textColor,backgroundColor:e.backgroundColor,showArrow:e.showArrow},null,8,["icon","title","description","iconColor","textColor","backgroundColor","showArrow"])])),_:2},1024))),64))])),_:1}),c[3]||(c[3]=h("h1",{class:"page-title"},"进度卡片",-1)),g(p,{gutter:20},{default:m((()=>[(u(),d(V,null,M(r,(e=>g(n,{xs:24,sm:12,md:6,key:e.id},{default:m((()=>[g(v,{percentage:e.percentage,title:e.title,color:e.color},null,8,["percentage","title","color"])])),_:2},1024))),64))])),_:1}),c[4]||(c[4]=h("h1",{class:"page-title"},"进度卡片（icon）",-1)),g(p,{gutter:20},{default:m((()=>[(u(),d(V,null,M(r,(e=>g(n,{xs:24,sm:12,md:6,key:e.id},{default:m((()=>[g(v,{percentage:e.percentage,title:e.title,color:e.color,icon:e.icon,iconColor:e.iconColor,iconBgColor:e.iconBgColor,iconSize:e.iconSize,iconBgRadius:8},null,8,["percentage","title","color","icon","iconColor","iconBgColor","iconSize"])])),_:2},1024))),64))])),_:1}),c[5]||(c[5]=h("h1",{class:"page-title"},"图表卡片（小图表）",-1)),g(p,{gutter:20},{default:m((()=>[g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(b,{isMiniChart:!0,value:2545,label:"新用户",date:"过去7天",percentage:1.2,height:9.5,chartData:[120,132,101,134,90,230,210]})])),_:1}),g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(f,{isMiniChart:!0,value:15480,label:"浏览量",date:"过去 14 天",percentage:-4.15,height:9.5,barWidth:"45%",chartData:[120,100,150,140,90,120,130]})])),_:1}),g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(b,{isMiniChart:!0,value:2545,label:"粉丝数",date:"过去 30 天",percentage:1.2,height:9.5,showAreaColor:!0,chartData:[150,180,160,200,180,220,240]})])),_:1}),g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(y,{value:36358,title:"粉丝量",percentage:18,percentageLabel:"较去年",data:[50,40],height:9.5,currentValue:"2022",previousValue:"2021",radius:["50%","70%"]})])),_:1})])),_:1}),c[6]||(c[6]=h("h1",{class:"page-title"},"图表卡片（大图表）",-1)),g(p,{gutter:20},{default:m((()=>[g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(b,{value:2545,label:"新用户",percentage:1.2,height:11,chartData:[120,132,101,134,90,230,210]})])),_:1}),g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(f,{value:15480,label:"浏览量",percentage:-4.15,height:11,chartData:[120,100,150,140,90,120,130,110]})])),_:1}),g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(b,{value:2545,label:"粉丝数",percentage:1.2,height:11,showAreaColor:!0,chartData:[150,180,160,200,180,220,240]})])),_:1}),g(n,{xs:24,sm:12,md:6},{default:m((()=>[g(y,{value:36358,title:"粉丝量",percentage:-18,percentageLabel:"较2021年",data:[70,30],height:11,currentValue:"12月",previousValue:"11月"})])),_:1})])),_:1}),c[7]||(c[7]=h("h1",{class:"page-title"},"数据列表卡片",-1)),g(p,{gutter:20},{default:m((()=>[g(n,{xs:24,sm:12,lg:8},{default:m((()=>[g(C,{list:o,title:"待办事项",subtitle:"今日待处理任务"})])),_:1}),g(n,{xs:24,sm:12,lg:8},{default:m((()=>[g(C,{maxCount:4,list:o,title:"最近活动",subtitle:"近期活动列表",showMoreButton:!0,onMore:s})])),_:1}),g(n,{xs:24,sm:12,lg:8},{default:m((()=>[g(w,{list:i,title:"最近交易",subtitle:"2024年12月20日"})])),_:1})])),_:1}),c[8]||(c[8]=h("h1",{class:"page-title"},"图片卡片",-1)),g(p,{gutter:20},{default:m((()=>[(u(),d(V,null,M(a,(e=>g(n,{xs:24,sm:12,md:6,key:e.id},{default:m((()=>[g(_,{imageUrl:e.imageUrl,title:e.title,category:e.category,readTime:e.readTime,views:e.views,comments:e.comments,date:e.date,onClick:e=>{}},null,8,["imageUrl","title","category","readTime","views","comments","date","onClick"])])),_:2},1024))),64))])),_:1})])}}}),[["__scopeId","data-v-471450ea"]]);export{Ve as default};
