/**
 * 字体加载监控工具
 * 监控字体加载状态，提供加载反馈
 */

interface FontLoadEvent {
  /** 字体族名称 */
  family: string
  /** 加载状态 */
  status: 'loading' | 'loaded' | 'error'
  /** 加载时间 */
  loadTime?: number
  /** 错误信息 */
  error?: string
}

type FontLoadCallback = (event: FontLoadEvent) => void

class FontMonitor {
  private callbacks: FontLoadCallback[] = []
  private loadStartTimes = new Map<string, number>()
  private isMonitoring = false

  /**
   * 开始监控字体加载
   */
  startMonitoring(): void {
    if (this.isMonitoring || !('fonts' in document)) {
      return
    }

    this.isMonitoring = true

    // 监听字体加载事件
    document.fonts.addEventListener('loading', this.handleFontLoading.bind(this))
    document.fonts.addEventListener('loadingdone', this.handleFontLoadingDone.bind(this))
    document.fonts.addEventListener('loadingerror', this.handleFontLoadingError.bind(this))

    // 检查已加载的字体
    this.checkLoadedFonts()
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    this.isMonitoring = false

    document.fonts.removeEventListener('loading', this.handleFontLoading.bind(this))
    document.fonts.removeEventListener('loadingdone', this.handleFontLoadingDone.bind(this))
    document.fonts.removeEventListener('loadingerror', this.handleFontLoadingError.bind(this))
  }

  /**
   * 添加字体加载回调
   */
  onFontLoad(callback: FontLoadCallback): void {
    this.callbacks.push(callback)
  }

  /**
   * 移除字体加载回调
   */
  offFontLoad(callback: FontLoadCallback): void {
    const index = this.callbacks.indexOf(callback)
    if (index > -1) {
      this.callbacks.splice(index, 1)
    }
  }

  /**
   * 获取字体加载状态
   */
  getFontStatus(): {
    ready: boolean
    loading: number
    loaded: number
    failed: number
  } {
    if (!('fonts' in document)) {
      return { ready: true, loading: 0, loaded: 0, failed: 0 }
    }

    const fonts = Array.from(document.fonts)
    const loading = fonts.filter(font => font.status === 'loading').length
    const loaded = fonts.filter(font => font.status === 'loaded').length
    const failed = fonts.filter(font => font.status === 'error').length

    return {
      ready: document.fonts.ready,
      loading,
      loaded,
      failed
    }
  }

  /**
   * 等待所有字体加载完成
   */
  async waitForFonts(timeout = 5000): Promise<void> {
    if (!('fonts' in document)) {
      return
    }

    try {
      await Promise.race([
        document.fonts.ready,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Font loading timeout')), timeout)
        )
      ])
    } catch (error) {
      // 静默处理字体加载超时
    }
  }

  /**
   * 处理字体开始加载
   */
  private handleFontLoading(event: any): void {
    const fontFace = event.fontface
    if (fontFace && fontFace.family) {
      this.loadStartTimes.set(fontFace.family, Date.now())
      
      this.notifyCallbacks({
        family: fontFace.family,
        status: 'loading'
      })
    }
  }

  /**
   * 处理字体加载完成
   */
  private handleFontLoadingDone(event: any): void {
    const fontFace = event.fontface
    if (fontFace && fontFace.family) {
      const startTime = this.loadStartTimes.get(fontFace.family)
      const loadTime = startTime ? Date.now() - startTime : undefined
      
      this.loadStartTimes.delete(fontFace.family)
      
      this.notifyCallbacks({
        family: fontFace.family,
        status: 'loaded',
        loadTime
      })
    }
  }

  /**
   * 处理字体加载错误
   */
  private handleFontLoadingError(event: any): void {
    const fontFace = event.fontface
    if (fontFace && fontFace.family) {
      this.loadStartTimes.delete(fontFace.family)
      
      this.notifyCallbacks({
        family: fontFace.family,
        status: 'error',
        error: 'Font loading failed'
      })
    }
  }

  /**
   * 检查已加载的字体
   */
  private checkLoadedFonts(): void {
    if (!('fonts' in document)) {
      return
    }

    document.fonts.forEach(fontFace => {
      if (fontFace.status === 'loaded') {
        this.notifyCallbacks({
          family: fontFace.family,
          status: 'loaded'
        })
      }
    })
  }

  /**
   * 通知回调函数
   */
  private notifyCallbacks(event: FontLoadEvent): void {
    this.callbacks.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        // 字体加载回调执行失败，已禁用错误输出
      }
    })
  }
}

// 创建全局实例
export const fontMonitor = new FontMonitor()

/**
 * 初始化字体监控
 */
export const initFontMonitor = (): void => {
  fontMonitor.startMonitoring()

  // 字体监控已启用，但禁用控制台输出
}

/**
 * 字体加载性能监控
 */
export const monitorFontPerformance = (): void => {
  fontMonitor.onFontLoad((event) => {
    if (event.status === 'loaded' && event.loadTime) {
      // 记录字体加载性能
      if ('performance' in window && 'mark' in performance) {
        performance.mark(`font-${event.family}-loaded`)
      }

      // 如果加载时间过长，记录但不输出警告
      if (event.loadTime > 1000) {
        // 字体加载时间过长，已禁用警告输出
      }
    }
  })
}

export default FontMonitor
