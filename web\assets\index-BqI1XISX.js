var e=Object.defineProperty,r=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,n=(r,t,o)=>t in r?e(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o;import{k as c,O as p,C as i,S as l,Q as f}from"./vendor-9ydHGNSq.js";import{_ as y}from"./_plugin-vue_export-helper-BCo6x5W8.js";const b={class:"chart-empty-state"},m=c((u=((e,r)=>{for(var t in r||(r={}))a.call(r,t)&&n(e,t,r[t]);if(o)for(var t of o(r))s.call(r,t)&&n(e,t,r[t]);return e})({},{name:"ArtChartEmpty"}),r(u,t({__name:"index",props:{iconSize:{default:50},iconColor:{default:"var(--art-text-gray-300)"}},setup(e){const r=e;return(e,t)=>(i(),p("div",b,[l("i",{class:"iconfont-sys",style:f({fontSize:r.iconSize+"px",color:r.iconColor})},"",4)]))}}))));var u;const v=y(m,[["__scopeId","data-v-6b39fecc"]]);export{v as _};
