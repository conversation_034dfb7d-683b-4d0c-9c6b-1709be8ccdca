/**
 * 字体加载优化工具
 * 智能预加载字体，避免浏览器警告
 */

interface FontConfig {
  /** 字体文件URL */
  url: string
  /** 字体格式 */
  format: 'woff2' | 'woff' | 'ttf' | 'otf'
  /** 字体族名称 */
  family?: string
  /** 是否立即加载 */
  immediate?: boolean
}

class FontLoader {
  private loadedFonts = new Set<string>()
  private loadingPromises = new Map<string, Promise<void>>()

  /**
   * 预加载字体
   */
  async preloadFont(config: FontConfig): Promise<void> {
    const { url, format, family, immediate = false } = config

    // 如果已经加载过，直接返回
    if (this.loadedFonts.has(url)) {
      return
    }

    // 如果正在加载，返回现有的Promise
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)
    }

    // 创建加载Promise
    const loadPromise = this.loadFontFile(url, format, family, immediate)
    this.loadingPromises.set(url, loadPromise)

    try {
      await loadPromise
      this.loadedFonts.add(url)
    } catch (error) {
      // 静默处理字体加载失败
    } finally {
      this.loadingPromises.delete(url)
    }
  }

  /**
   * 批量预加载字体
   */
  async preloadFonts(configs: FontConfig[]): Promise<void> {
    const promises = configs.map(config => this.preloadFont(config))
    await Promise.allSettled(promises)
  }

  /**
   * 加载字体文件
   */
  private async loadFontFile(
    url: string, 
    format: string, 
    family?: string, 
    immediate = false
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果不是立即加载，等待页面加载完成
      if (!immediate && document.readyState !== 'complete') {
        window.addEventListener('load', () => {
          this.doLoadFont(url, format, family, resolve, reject)
        }, { once: true })
      } else {
        this.doLoadFont(url, format, family, resolve, reject)
      }
    })
  }

  /**
   * 执行字体加载
   */
  private doLoadFont(
    url: string,
    format: string,
    family: string | undefined,
    resolve: () => void,
    reject: (error: any) => void
  ): void {
    // 使用Font Face API加载字体
    if ('FontFace' in window && family) {
      const fontFace = new FontFace(family, `url(${url}) format('${format}')`)
      
      fontFace.load()
        .then(() => {
          document.fonts.add(fontFace)
          resolve()
        })
        .catch(reject)
    } else {
      // 降级方案：创建隐藏元素触发字体加载
      this.loadFontFallback(url, resolve, reject)
    }
  }

  /**
   * 降级字体加载方案
   */
  private loadFontFallback(
    url: string,
    resolve: () => void,
    reject: (error: any) => void
  ): void {
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = url
    link.onload = () => resolve()
    link.onerror = () => reject(new Error(`Failed to load font: ${url}`))
    
    document.head.appendChild(link)
  }

  /**
   * 检查字体是否已加载
   */
  isFontLoaded(url: string): boolean {
    return this.loadedFonts.has(url)
  }

  /**
   * 获取已加载的字体列表
   */
  getLoadedFonts(): string[] {
    return Array.from(this.loadedFonts)
  }

  /**
   * 清除加载记录
   */
  clear(): void {
    this.loadedFonts.clear()
    this.loadingPromises.clear()
  }
}

// 创建全局实例
export const fontLoader = new FontLoader()

/**
 * 预加载系统图标字体
 */
export const preloadSystemFonts = async (): Promise<void> => {
  const systemFonts: FontConfig[] = [
    {
      url: '/src/assets/icons/system/iconfont.woff2',
      format: 'woff2',
      family: 'iconfont',
      immediate: false // 页面加载完成后再加载
    },
    {
      url: '/src/assets/icons/system/iconfont.woff',
      format: 'woff',
      family: 'iconfont',
      immediate: false
    }
  ]

  await fontLoader.preloadFonts(systemFonts)
}

/**
 * 智能字体预加载
 * 根据页面内容决定是否需要加载字体
 */
export const smartFontPreload = async (): Promise<void> => {
  // 检查页面是否使用了图标字体
  const hasIconFont = document.querySelector('.iconfont, [class*="icon-"]')
  
  if (hasIconFont) {
    await preloadSystemFonts()
  }
}

/**
 * 延迟字体加载
 * 在页面空闲时加载字体
 */
export const lazyLoadFonts = (): void => {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      preloadSystemFonts()
    })
  } else {
    // 降级方案：延迟2秒加载
    setTimeout(() => {
      preloadSystemFonts()
    }, 2000)
  }
}

export default FontLoader
