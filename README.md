# 🎯 DianDian AdminPro Web

<div align="center">

![Vue](https://img.shields.io/badge/Vue-3.5.12-brightgreen.svg) ![TypeScript](https://img.shields.io/badge/TypeScript-5.6.3-blue.svg) ![Vite](https://img.shields.io/badge/Vite-6.1.0-646CFF.svg) ![Element Plus](https://img.shields.io/badge/Element%20Plus-2.8.0-409EFF.svg) ![License](https://img.shields.io/badge/License-MIT-green.svg)

**现代化企业级后台管理系统**

基于 Vue3 + TypeScript + Vite + Element Plus 构建的高性能管理平台提供完整的业务管理解决方案，专注于用户体验和开发效率

[在线预览](#) · [快速开始](#-快速开始) · [功能特性](#-核心功能) · [技术文档](#-技术架构)

</div>

## ✨ 项目特色

### 🚀 技术先进

- **前沿技术栈**: Vue 3.5 + TypeScript 5.6 + Vite 6.1 + Element Plus 2.8
- **现代化架构**: 组合式API + Pinia状态管理 + Vue Router 4
- **开发体验**: 完整的TypeScript类型支持 + 自动导入 + 热重载
- **工程化**: ESLint + Prettier + Husky + Lint-staged 代码质量保障

### 🎨 界面精美

- **多主题系统**: 明暗主题无缝切换 + 自定义主题色
- **响应式设计**: 完美适配桌面、平板、移动端设备
- **交互动画**: 流畅的页面过渡和微交互效果
- **现代UI**: 精心设计的组件库和布局系统

### 🔐 安全可靠

- **多层权限**: 路由级 + 菜单级 + 按钮级权限控制
- **安全防护**: 双重密码验证 + 安全锁屏 + 数据加密
- **网络监控**: 实时网络状态监控和自动重连机制
- **操作审计**: 完整的用户操作日志记录

## 🎯 核心功能

### 📊 业务管理模块

#### 🗓️ 预约管理系统

- **客户预约**: 完整的预约流程管理，支持预约状态跟踪
- **配置管理**: 套餐类型、随心换选项、内存卡规格配置
- **数据统计**: 实时预约数据统计，支持多维度分析
- **批量操作**: 高效的批量处理功能，提升操作效率

#### 👥 客户关系管理

- **客户档案**: 详细的客户信息管理和维护
- **标签分类**: 灵活的客户标签系统，便于分类管理
- **数据导入**: 支持Excel等格式的批量数据导入
- **搜索筛选**: 强大的搜索和筛选功能

#### 📈 数据分析中心

- **首页仪表盘**: 关键业务指标实时展示
- **统计报表**: 多维度数据统计和趋势分析
- **可视化图表**: 基于ECharts的丰富图表展示
- **数据导出**: 支持多种格式的数据导出

### 🛠️ 系统管理模块

#### 👤 用户权限管理

- **用户管理**: 用户账户的创建、编辑、删除和状态管理
- **角色管理**: 灵活的角色权限配置和分配
- **密码安全**: 双重验证的密码重置机制
- **权限控制**: 细粒度的功能权限控制

#### 🧭 菜单路由管理

- **动态菜单**: 基于权限的动态菜单生成
- **路由管理**: 灵活的路由配置和权限控制
- **菜单配置**: 可视化的菜单结构配置
- **权限分配**: 精确的菜单权限分配

#### ⚙️ 系统配置

- **主题设置**: 多主题切换和自定义配置
- **国际化**: 多语言支持和切换
- **系统参数**: 灵活的系统参数配置
- **安全设置**: 系统安全相关配置

### 🧩 组件工具库

#### 📋 高性能表格

- **虚拟滚动**: 支持大数据量的高性能渲染
- **列宽记忆**: 自动记忆用户的列宽设置
- **数据缓存**: 智能的数据缓存机制
- **批量操作**: 支持多选和批量处理

#### 📝 智能表单

- **动态表单**: 基于配置的动态表单生成
- **验证规则**: 完善的表单验证机制
- **联动控制**: 表单字段间的联动控制
- **自动保存**: 表单数据的自动保存功能

#### 📊 图表组件

- **ECharts集成**: 丰富的图表类型支持
- **实时数据**: 支持实时数据更新
- **交互功能**: 完善的图表交互功能
- **响应式**: 自适应不同屏幕尺寸

## 🌟 功能亮点

### 🎨 卓越体验

- 🌈 **智能主题**: 明暗主题自动切换 + 自定义主题色配置
- 🔍 **全局搜索**: 快速查找功能和内容，支持模糊匹配
- 📑 **多标签页**: 支持多页面同时操作，标签拖拽排序
- 🧭 **面包屑导航**: 清晰的页面层级导航和快速跳转
- 🔒 **安全锁屏**: 自动锁屏保护，支持密码和图案解锁

### ⚡ 性能卓越

- 🚀 **虚拟滚动**: 大数据量表格的极致性能优化
- 💾 **智能缓存**: 多层级缓存策略，显著减少API请求
- 🎭 **骨架屏**: 优雅的加载状态，提升用户感知性能
- 📱 **懒加载**: 组件和资源的按需加载机制
- 🔄 **状态持久化**: 用户偏好和操作状态的本地持久化

### 🎯 交互优化

- ✨ **微动画**: 流畅的页面过渡和组件交互动画
- 🎪 **礼花效果**: 节日庆祝动画和操作成功反馈
- 💬 **聊天窗口**: 内置实时聊天功能
- 🌊 **水印保护**: 可配置的页面水印，保护内容安全
- 📱 **触摸优化**: 移动端友好的触摸交互设计

## 🏗️ 技术架构

### 🎯 核心技术栈

| 技术         | 版本   | 说明                            |
| ------------ | ------ | ------------------------------- |
| Vue          | 3.5.12 | 渐进式JavaScript框架，组合式API |
| TypeScript   | 5.6.3  | 类型安全的JavaScript超集        |
| Vite         | 6.1.0  | 下一代前端构建工具，极速热重载  |
| Element Plus | 2.8.0  | Vue 3 企业级UI组件库            |
| Pinia        | 3.0.2  | Vue 官方推荐的状态管理库        |
| Vue Router   | 4.4.2  | Vue 官方路由管理器              |

### 🛠️ 开发工具链

| 工具        | 版本    | 功能                           |
| ----------- | ------- | ------------------------------ |
| ESLint      | 9.9.1   | JavaScript/TypeScript 代码检查 |
| Prettier    | 3.5.3   | 代码格式化工具                 |
| Stylelint   | 16.20.0 | CSS/SCSS 代码检查              |
| Husky       | 9.1.5   | Git hooks 管理                 |
| Lint-staged | 15.5.2  | 暂存文件检查                   |

### 📦 核心依赖

| 依赖         | 版本   | 用途             |
| ------------ | ------ | ---------------- |
| @vueuse/core | latest | Vue 组合式工具集 |
| ECharts      | 5.4.0  | 数据可视化图表库 |
| Axios        | 1.7.5  | HTTP 客户端      |
| Crypto-js    | 4.2.0  | 加密解密库       |
| Vue-i18n     | 9.14.0 | 国际化解决方案   |

## 📦 项目结构

```
DianDian-AdminPro-Web/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口层
│   │   ├── appointmentApi.ts    # 预约管理接口
│   │   ├── userManageApi.ts     # 用户管理接口
│   │   ├── menuApi.ts           # 菜单管理接口
│   │   ├── roleApi.ts           # 角色管理接口
│   │   └── index.ts             # 接口统一导出
│   ├── components/        # 组件库
│   │   ├── core/         # 核心组件
│   │   │   ├── layouts/  # 布局组件
│   │   │   ├── tables/   # 表格组件
│   │   │   ├── forms/    # 表单组件
│   │   │   └── charts/   # 图表组件
│   │   ├── common/       # 通用组件
│   │   └── custom/       # 自定义组件
│   ├── composables/      # 组合式函数
│   │   ├── useApi.ts     # API调用Hook
│   │   ├── useTable.ts   # 表格Hook
│   │   ├── useForm.ts    # 表单Hook
│   │   └── useAuth.ts    # 权限Hook
│   ├── directives/       # 自定义指令
│   ├── locales/          # 国际化
│   ├── router/           # 路由配置
│   ├── store/            # 状态管理(Pinia)
│   ├── styles/           # 样式文件
│   ├── utils/            # 工具函数
│   │   ├── http/         # HTTP请求工具
│   │   ├── browser/      # 浏览器相关
│   │   └── validation/   # 验证工具
│   ├── views/            # 页面组件
│   │   ├── dashboard/    # 仪表盘
│   │   ├── appointment/  # 预约管理
│   │   ├── system/       # 系统管理
│   │   └── auth/         # 认证页面
│   └── types/            # TypeScript类型定义
└── config files          # 配置文件
```

## 🚀 快速开始

### 📋 环境要求

| 环境     | 版本要求      | 说明                                          |
| -------- | ------------- | --------------------------------------------- |
| Node.js  | >= 16.0.0     | 建议使用 LTS 版本                             |
| 包管理器 | pnpm >= 7.0.0 | 推荐使用 pnpm                                 |
| 浏览器   | 现代浏览器    | Chrome 87+, Firefox 78+, Safari 14+, Edge 88+ |

### 📥 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-username/DianDian-AdminPro-Web.git
cd DianDian-AdminPro-Web

# 安装依赖 (推荐使用 pnpm)
pnpm install

# 或使用其他包管理器
npm install
# yarn install
```

### 🛠️ 开发调试

```bash
# 启动开发服务器
pnpm dev

# 启动并自动打开浏览器
pnpm dev --open

# 开发服务器将在 http://localhost:9011 启动
```

### 📦 构建部署

```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm serve

# 代码质量检查
pnpm lint

# 自动修复代码格式
pnpm fix

# 样式检查
pnpm lint:stylelint

# 提交代码 (使用 commitizen)
pnpm commit
```

## 📋 功能清单

### ✅ 核心功能模块

#### 🏠 首页仪表盘

- [x] 实时数据概览卡片 (今日预约、待处理、已完成、总客户数)
- [x] 动态数据统计图表
- [x] 快捷操作入口
- [x] 系统活动动态
- [x] 待办事项管理

#### 🗓️ 预约管理系统

- [x] 客户预约列表管理
- [x] 预约状态流转控制
- [x] 套餐类型配置管理
- [x] 随心换选项管理
- [x] 内存卡规格管理
- [x] 批量操作功能
- [x] 高级搜索和筛选
- [x] 数据导入导出
- [x] 列宽记忆功能
- [x] 虚拟滚动性能优化
- [x] 智能数据缓存机制

#### 👤 用户权限管理

- [x] 用户账户管理 (创建、编辑、删除、状态控制)
- [x] 双重安全验证的密码重置
- [x] 角色权限配置和分配
- [x] 菜单权限动态控制
- [x] 按钮级权限指令
- [x] 路由级别访问鉴权
- [x] 用户登录状态管理

#### 🧭 菜单路由系统

- [x] 动态菜单生成
- [x] 基于权限的菜单显示
- [x] 路由权限控制
- [x] 面包屑导航
- [x] 多标签页管理

#### 🎨 界面主题系统

- [x] 明暗主题无缝切换
- [x] 自定义主题色配置
- [x] 响应式布局设计
- [x] 多语言国际化支持
- [x] 全局搜索功能
- [x] 安全锁屏保护

#### 🧩 组件工具库

- [x] 高性能虚拟滚动表格
- [x] 动态表单生成器
- [x] ECharts图表组件集成
- [x] 富文本编辑器
- [x] 图片裁剪工具
- [x] 二维码生成器
- [x] Excel数据处理
- [x] 文件上传组件

### 🔄 性能与体验优化

#### ⚡ 性能优化

- [x] 组件按需懒加载
- [x] 图片资源懒加载
- [x] 大数据虚拟滚动
- [x] 多层级数据缓存
- [x] 骨架屏加载状态
- [x] 代码分割优化
- [x] 网络请求防抖节流

#### 🎯 用户体验

- [x] 流畅的微动画效果
- [x] 智能加载状态提示
- [x] 完善的错误边界处理
- [x] 实时网络状态监控
- [x] 丰富的操作反馈提示
- [x] 移动端触摸优化
- [x] 礼花庆祝动画效果

## 🔧 配置说明

### 🌍 环境配置

项目支持多环境配置，通过环境变量文件进行管理：

```bash
.env                # 公共环境变量
.env.development    # 开发环境配置
.env.production     # 生产环境配置
```

**主要配置项：**

- `VITE_API_BASE_URL`: API接口基础地址
- `VITE_APP_TITLE`: 应用标题
- `VITE_APP_VERSION`: 应用版本号

### 🎨 主题配置

支持丰富的主题自定义选项：

| 配置项   | 选项                | 说明                 |
| -------- | ------------------- | -------------------- |
| 主题模式 | 明亮/暗黑/自动      | 支持系统主题自动切换 |
| 主题色   | 12种预设色 + 自定义 | 实时预览主题效果     |
| 布局模式 | 左侧/顶部/混合菜单  | 灵活的布局选择       |
| 容器宽度 | 固定/流式宽度       | 适配不同屏幕尺寸     |

### 🔐 权限配置

多层级权限控制系统：

| 权限类型 | 控制范围     | 实现方式           |
| -------- | ------------ | ------------------ |
| 路由权限 | 页面访问控制 | 基于角色的路由守卫 |
| 菜单权限 | 菜单显示控制 | 动态菜单生成       |
| 按钮权限 | 操作功能控制 | v-auth 指令        |
| 数据权限 | 数据访问范围 | 基于用户的数据过滤 |

## 📱 浏览器支持

| 浏览器  | 版本要求 | 说明               |
| ------- | -------- | ------------------ |
| Chrome  | ≥ 87     | 推荐使用最新版本   |
| Firefox | ≥ 78     | 支持现代Web标准    |
| Safari  | ≥ 14     | macOS/iOS 设备     |
| Edge    | ≥ 88     | 基于 Chromium 内核 |

**注意：** 项目使用了现代 JavaScript 特性，不支持 IE 浏览器。

## 📈 更新日志

### v2.1.0 (2024-12-17)

**🔐 安全增强**

- ✨ 新增双重密码验证的用户密码重置功能
- 🔒 增强管理员操作的安全验证机制
- 🛡️ 完善操作审计和日志记录

**📊 数据统计**

- ✨ 首页集成真实预约管理数据统计
- 📈 实时数据展示和智能降级处理
- 🎯 多维度业务数据分析

**🎨 界面优化**

- 🎨 优化首页横幅设计和布局
- 💫 增强用户交互体验
- 📱 改进移动端响应式设计

### v2.0.0 (2024-12-01)

**🚀 核心功能**

- ✨ 全新的预约管理系统
- 👥 完整的客户关系管理
- 🔐 多层级权限控制系统
- 🧭 动态菜单和路由管理

**⚡ 性能优化**

- 🚀 虚拟滚动和数据缓存
- 🎭 骨架屏和懒加载优化
- 💾 智能缓存策略
- 📦 代码分割和按需加载

**🔧 技术升级**

- 🆙 Vue 3.5 + TypeScript 5.6 + Vite 6.1
- 🛠️ 完善的开发工具链
- 📋 TypeScript 类型安全
- 🎯 现代化项目架构

## 🤝 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

### 📝 贡献方式

- 🐛 **Bug 报告**: 发现问题请提交 Issue
- 💡 **功能建议**: 提出新功能想法和改进建议
- 📖 **文档改进**: 完善项目文档和注释
- 🔧 **代码贡献**: 提交 Pull Request 修复问题或添加功能

### 📋 提交规范

项目使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
feat: 新增功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整(不影响功能)
refactor: 代码重构
perf: 性能优化
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 🔄 开发流程

1. **Fork 项目** 到个人仓库
2. **创建分支**: `git checkout -b feature/your-feature-name`
3. **开发功能** 并确保代码质量
4. **提交代码**: `git commit -m 'feat: add your feature'`
5. **推送分支**: `git push origin feature/your-feature-name`
6. **提交 PR** 并描述你的更改

### ✅ 代码质量

- 遵循项目的 ESLint 和 Prettier 配置
- 确保 TypeScript 类型检查通过
- 添加必要的单元测试
- 更新相关文档

## 📄 开源协议

本项目基于 [MIT License](./LICENSE) 开源协议，您可以自由使用、修改和分发。

## 🙏 致谢

感谢以下优秀的开源项目：

| 项目         | 描述                     | 官网                                                  |
| ------------ | ------------------------ | ----------------------------------------------------- |
| Vue.js       | 渐进式JavaScript框架     | [vuejs.org](https://vuejs.org/)                       |
| Element Plus | Vue 3 企业级UI组件库     | [element-plus.org](https://element-plus.org/)         |
| Vite         | 下一代前端构建工具       | [vitejs.dev](https://vitejs.dev/)                     |
| TypeScript   | JavaScript的类型安全超集 | [typescriptlang.org](https://www.typescriptlang.org/) |
| Pinia        | Vue 官方状态管理库       | [pinia.vuejs.org](https://pinia.vuejs.org/)           |

## 📞 联系我们

- 📧 **邮箱**: <EMAIL>
- 💬 **讨论**: [GitHub Discussions](https://github.com/your-username/DianDian-AdminPro-Web/discussions)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-username/DianDian-AdminPro-Web/issues)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给个 Star 支持一下！**

**🚀 让我们一起构建更好的管理系统！**

Made with ❤️ by DianDian Team

</div>
