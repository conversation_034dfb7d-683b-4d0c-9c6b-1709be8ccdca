import { useWindowSize as useVueUseWindowSize } from '@vueuse/core'
import { computed } from 'vue'

/**
 * 增强的窗口尺寸监听钩子
 * 基于VueUse的useWindowSize，添加了响应式断点和设备类型判断
 * @returns 窗口尺寸和响应式信息
 */
export const useWindowSize = () => {
  const { width, height } = useVueUseWindowSize()

  // 响应式断点
  const breakpoints = {
    xs: 480,
    sm: 768,
    md: 992,
    lg: 1200,
    xl: 1920
  }

  // 当前断点
  const currentBreakpoint = computed(() => {
    const w = width.value
    if (w < breakpoints.xs) return 'xs'
    if (w < breakpoints.sm) return 'sm'
    if (w < breakpoints.md) return 'md'
    if (w < breakpoints.lg) return 'lg'
    if (w < breakpoints.xl) return 'xl'
    return 'xxl'
  })

  // 设备类型判断
  const isMobile = computed(() => width.value < breakpoints.sm)
  const isTablet = computed(() => width.value >= breakpoints.sm && width.value < breakpoints.lg)
  const isDesktop = computed(() => width.value >= breakpoints.lg)

  // 屏幕方向
  const isLandscape = computed(() => width.value > height.value)
  const isPortrait = computed(() => height.value > width.value)

  // 响应式断点检查函数
  const isBreakpoint = (bp: keyof typeof breakpoints) => {
    return computed(() => width.value >= breakpoints[bp])
  }

  return {
    width,
    height,
    currentBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
    isBreakpoint,
    breakpoints
  }
}
