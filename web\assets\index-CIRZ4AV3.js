var e=Object.defineProperty,t=Object.defineProperties,i=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,s=(t,i,n)=>i in t?e(t,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[i]=n,a=(e,t)=>{for(var i in t||(t={}))o.call(t,i)&&s(e,i,t[i]);if(n)for(var i of n(t))r.call(t,i)&&s(e,i,t[i]);return e};import{j as l,k as c,r as u,d as h,bw as d,O as f,C as p,S as g,x as v}from"./vendor-9ydHGNSq.js";import{l as y}from"./lock_screen_1-DQCBQAEv.js";import{_ as m}from"./_plugin-vue_export-helper-BCo6x5W8.js";function k(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function C(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?k(Object(i),!0).forEach((function(t){x(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):k(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function w(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,M(n.key),n)}}function T(e,t,i){return t&&w(e.prototype,t),i&&w(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function x(e,t,i){return(t=M(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function S(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&P(e,t)}function E(e){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function P(e,t){return(P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function I(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function L(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return I(e)}function A(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=E(e);if(t){var o=E(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return L(this,i)}}function O(){return O="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=E(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:i):o.value}},O.apply(this,arguments)}function D(e){return function(e){if(Array.isArray(e))return R(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return R(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function M(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var N,F={exports:{}};var H=(N||(N=1,function(e){var t=Object.prototype.hasOwnProperty,i="~";function n(){}function o(e,t,i){this.fn=e,this.context=t,this.once=i||!1}function r(e,t,n,r,s){if("function"!=typeof n)throw new TypeError("The listener must be a function");var a=new o(n,r||e,s),l=i?i+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],a]:e._events[l].push(a):(e._events[l]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function a(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(i=!1)),a.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(i?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},a.prototype.listeners=function(e){var t=i?i+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,r=n.length,s=new Array(r);o<r;o++)s[o]=n[o].fn;return s},a.prototype.listenerCount=function(e){var t=i?i+e:e,n=this._events[t];return n?n.fn?1:n.length:0},a.prototype.emit=function(e,t,n,o,r,s){var a=i?i+e:e;if(!this._events[a])return!1;var l,c,u=this._events[a],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,r),!0;case 6:return u.fn.call(u.context,t,n,o,r,s),!0}for(c=1,l=new Array(h-1);c<h;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,o);break;default:if(!l)for(d=1,l=new Array(h-1);d<h;d++)l[d-1]=arguments[d];u[c].fn.apply(u[c].context,l)}}return!0},a.prototype.on=function(e,t,i){return r(this,e,t,i,!1)},a.prototype.once=function(e,t,i){return r(this,e,t,i,!0)},a.prototype.removeListener=function(e,t,n,o){var r=i?i+e:e;if(!this._events[r])return this;if(!t)return s(this,r),this;var a=this._events[r];if(a.fn)a.fn!==t||o&&!a.once||n&&a.context!==n||s(this,r);else{for(var l=0,c=[],u=a.length;l<u;l++)(a[l].fn!==t||o&&!a[l].once||n&&a[l].context!==n)&&c.push(a[l]);c.length?this._events[r]=1===c.length?c[0]:c:s(this,r)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=i?i+e:e,this._events[t]&&s(this,t)):(this._events=new n,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=i,a.EventEmitter=a,e.exports=a}(F)),F.exports);const B=l(H);var U="undefined"!=typeof window&&window.location&&window.location.href.indexOf("xgplayerdebugger=1")>-1,j={info:"color: #525252; background-color: #90ee90;",error:"color: #525252; background-color: red;",warn:"color: #525252; background-color: yellow; "},V="%c[xgplayer]",W={config:{debug:U?3:0},logInfo:function(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];this.config.debug>=3&&(t=console).log.apply(t,[V,j.info,e].concat(n))},logWarn:function(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];this.config.debug>=1&&(t=console).warn.apply(t,[V,j.warn,e].concat(n))},logError:function(e){var t;if(!(this.config.debug<1)){for(var i=this.config.debug>=2?"trace":"error",n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];(t=console)[i].apply(t,[V,j.error,e].concat(o))}}};var G=function(){function e(t){_(this,e),this.bufferedList=t}return T(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}(),z={};function K(e){var t=b(e);return null!==e&&("object"===t||"function"===t)}function Y(e,t,i){var n,o,r,s,a,l,c=0,u=!1,h=!1,d=!0,f=!t&&0!==t&&"function"==typeof window.requestAnimationFrame;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var i=n,r=o;return n=o=void 0,c=t,s=e.apply(r,i)}function g(e,t){return f?(window.cancelAnimationFrame(a),window.requestAnimationFrame(e)):setTimeout(e,t)}function v(e){var i=e-l;return void 0===l||i>=t||i<0||h&&e-c>=r}function y(){var e=Date.now();if(v(e))return m(e);a=g(y,function(e){var i=e-c,n=t-(e-l);return h?Math.min(n,r-i):n}(e))}function m(e){return a=void 0,d&&n?p(e):(n=o=void 0,s)}function k(){for(var e=Date.now(),i=v(e),r=arguments.length,d=new Array(r),f=0;f<r;f++)d[f]=arguments[f];if(n=d,o=this,l=e,i){if(void 0===a)return function(e){return c=e,a=g(y,t),u?p(e):s}(l);if(h)return a=g(y,t),p(l)}return void 0===a&&(a=g(y,t)),s}return t=+t||0,K(i)&&(u=!!i.leading,r=(h="maxWait"in i)?Math.max(+i.maxWait||0,t):r,d="trailing"in i?!!i.trailing:d),k.cancel=function(){void 0!==a&&function(e){if(f)return window.cancelAnimationFrame(e);clearTimeout(e)}(a),c=0,n=l=o=a=void 0},k.flush=function(){return void 0===a?s:m(Date.now())},k.pending=function(){return void 0!==a},k}z.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=document.createElement(e);return o.className=n,o.innerHTML=t,Object.keys(i).forEach((function(t){var n=t,r=i[t];"video"===e||"audio"===e||"live-video"===e?r&&o.setAttribute(n,r):o.setAttribute(n,r)})),o},z.createDomFromHtml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{var n=document.createElement("div");n.innerHTML=e;var o=n.children;return n=null,o.length>0?(o=o[0],i&&z.addClass(o,i),t&&Object.keys(t).forEach((function(e){o.setAttribute(e,t[e])})),o):null}catch(r){return W.logError("util.createDomFromHtml",r),null}},z.hasClass=function(e,t){if(!e||!t)return!1;try{return Array.prototype.some.call(e.classList,(function(e){return e===t}))}catch(n){var i=e.className&&"object"===b(e.className)?e.getAttribute("class"):e.className;return i&&!!i.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}},z.addClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)}))}catch(i){z.hasClass(e,t)||(e.className&&"object"===b(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t)}},z.removeClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.remove(t)}))}catch(i){z.hasClass(e,t)&&t.split(/\s+/g).forEach((function(t){var i=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===b(e.className)?e.setAttribute("class",e.getAttribute("class").replace(i," ")):e.className=e.className.replace(i," ")}))}},z.toggleClass=function(e,t){e&&t.split(/\s+/g).forEach((function(t){z.hasClass(e,t)?z.removeClass(e,t):z.addClass(e,t)}))},z.classNames=function(){for(var e=arguments,t=[],i=function(i){"String"===z.typeOf(e[i])?t.push(e[i]):"Object"===z.typeOf(e[i])&&Object.keys(e[i]).map((function(n){e[i][n]&&t.push(n)}))},n=0;n<arguments.length;n++)i(n);return t.join(" ")},z.findDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,i=arguments.length>1?arguments[1]:void 0;try{e=t.querySelector(i)}catch(n){W.logError("util.findDom",n),0===i.indexOf("#")&&(e=t.getElementById(i.slice(1)))}return e},z.getCss=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,!1)[t]},z.padStart=function(e,t,i){for(var n=String(i),o=t|0,r=Math.ceil(o/n.length),s=[],a=String(e);r--;)s.push(n);return s.join("").substring(0,o-a.length)+a},z.format=function(e){if(window.isNaN(e))return"";e=Math.round(e);var t=z.padStart(Math.floor(e/3600),2,0),i=z.padStart(Math.floor((e-3600*t)/60),2,0),n=z.padStart(Math.floor(e-3600*t-60*i),2,0);return("00"===t?[i,n]:[t,i,n]).join(":")},z.event=function(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement},z.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]},z.deepCopy=function(e,t){if("Object"===z.typeOf(t)&&"Object"===z.typeOf(e))return Object.keys(t).forEach((function(i){"Object"!==z.typeOf(t[i])||t[i]instanceof Node?"Array"===z.typeOf(t[i])?e[i]="Array"===z.typeOf(e[i])?e[i].concat(t[i]):t[i]:e[i]=t[i]:void 0===e[i]||void 0===e[i]?e[i]=t[i]:z.deepCopy(e[i],t[i])})),e},z.deepMerge=function(e,t){return Object.keys(t).map((function(i){var n;"Array"===z.typeOf(t[i])&&"Array"===z.typeOf(e[i])?"Array"===z.typeOf(e[i])&&(n=e[i]).push.apply(n,D(t[i])):z.typeOf(e[i])!==z.typeOf(t[i])||null===e[i]||"Object"!==z.typeOf(e[i])||t[i]instanceof window.Node?null!==t[i]&&(e[i]=t[i]):z.deepMerge(e[i],t[i])})),e},z.getBgImage=function(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var i=document.createElement("a");return i.href=t.replace(/url\("|"\)/g,""),i.href},z.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},z.setInterval=function(e,t,i,n){e._interval[t]||(e._interval[t]=window.setInterval(i.bind(e),n))},z.clearInterval=function(e,t){clearInterval(e._interval[t]),e._interval[t]=null},z.setTimeout=function(e,t,i){e._timers||(e._timers=[]);var n=setTimeout((function(){t(),z.clearTimeout(e,n)}),i);return e._timers.push(n),n},z.clearTimeout=function(e,t){var i=e._timers;if("Array"===z.typeOf(i)){for(var n=0;n<i.length;n++)if(i[n]===t){i.splice(n,1),clearTimeout(t);break}}else clearTimeout(t)},z.clearAllTimers=function(e){var t=e._timers;"Array"===z.typeOf(t)&&(t.map((function(e){clearTimeout(e)})),e._timerIds=[])},z.createImgBtn=function(e,t,i,n){var o,r,s,a=z.createDom("xg-".concat(e),"",{},"xgplayer-".concat(e,"-img"));(a.style.backgroundImage='url("'.concat(t,'")'),i&&n)&&(["px","rem","em","pt","dp","vw","vh","vm","%"].every((function(e){return!(i.indexOf(e)>-1&&n.indexOf(e)>-1)||(o=parseFloat(i.slice(0,i.indexOf(e)).trim()),r=parseFloat(n.slice(0,n.indexOf(e)).trim()),s=e,!1)})),a.style.width="".concat(o).concat(s),a.style.height="".concat(r).concat(s),a.style.backgroundSize="".concat(o).concat(s," ").concat(r).concat(s),a.style.margin="start"===e?"-".concat(r/2).concat(s," auto auto -").concat(o/2).concat(s):"auto 5px auto 5px");return a},z.Hex2RGBA=function(e,t){var i=[];if(/^\#[0-9A-F]{3}$/i.test(e)){var n="#";e.replace(/[0-9A-F]/gi,(function(e){n+=e+e})),e=n}return/^#[0-9A-F]{6}$/i.test(e)?(e.replace(/[0-9A-F]{2}/gi,(function(e){i.push(parseInt(e,16))})),"rgba(".concat(i.join(","),", ").concat(t,")")):"rgba(255, 255, 255, 0.1)"},z.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement},z.checkIsFunction=function(e){return e&&"function"==typeof e},z.checkIsObject=function(e){return null!==e&&"object"===b(e)},z.hide=function(e){e.style.display="none"},z.show=function(e,t){e.style.display=t||"block"},z.isUndefined=function(e){if(null==e)return!0},z.isNotNull=function(e){return!(null==e)},z.setStyleFromCsstext=function(e,t){t&&("String"===z.typeOf(t)?t.replace(/\s+/g,"").split(";").map((function(t){if(t){var i=t.split(":");i.length>1&&(e.style[i[0]]=i[1])}})):Object.keys(t).map((function(i){e.style[i]=t[i]})))},z.filterStyleFromText=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],i=e.style.cssText;if(!i)return{};var n=i.replace(/\s+/g,"").split(";"),o={},r={};return n.map((function(e){if(e){var i=e.split(":");i.length>1&&(!function(e,t){for(var i=0,n=t.length;i<n;i++)if(e.indexOf(t[i])>-1)return!0;return!1}(i[0],t)?r[i[0]]=i[1]:o[i[0]]=i[1])}})),e.setAttribute("style",""),Object.keys(r).map((function(t){e.style[t]=r[t]})),o},z.getStyleFromCsstext=function(e){var t=e.style.cssText;if(!t)return{};var i=t.replace(/\s+/g,"").split(";"),n={};return i.map((function(e){if(e){var t=e.split(":");t.length>1&&(n[t[0]]=t[1])}})),n},z.preloadImg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){var n=new window.Image;n.onload=function(e){n=null,t&&t(e)},n.onerror=function(e){n=null,i&&i(e)},n.src=e}},z.stopPropagation=function(e){e&&e.stopPropagation()},z.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},z.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},z.checkTouchSupport=function(){return"ontouchstart"in window},z.getBuffered2=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,i=[],n=0;n<e.length;n++)i.push({start:e.start(n)<.5?0:e.start(n),end:e.end(n)});i.sort((function(e,t){var i=e.start-t.start;return i||t.end-e.end}));var o=[];if(t)for(var r=0;r<i.length;r++){var s=o.length;if(s){var a=o[s-1].end;i[r].start-a<t?i[r].end>a&&(o[s-1].end=i[r].end):o.push(i[r])}else o.push(i[r])}else o=i;return new G(o)},z.getEventPos=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e.touches&&e.touches.length>0&&(e=e.touches[0]),{x:e.x/t,y:e.y/t,clientX:e.clientX/t,clientY:e.clientY/t,offsetX:e.offsetX/t,offsetY:e.offsetY/t,pageX:e.pageX/t,pageY:e.pageY/t}},z.requestAnimationFrame=function(e){var t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(t)return t(e)},z.getHostFromUrl=function(e){if("String"!==z.typeOf(e))return"";var t=e.split("/"),i="";return t.length>3&&t[2]&&(i=t[2]),i},z.cancelAnimationFrame=function(e){var t=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;t&&t(e)},z.isMSE=function(e){return e.media&&(e=e.media),!!(e&&e instanceof HTMLMediaElement)&&(/^blob/.test(e.currentSrc)||/^blob/.test(e.src))},z.isBlob=function(e){return"string"==typeof e&&/^blob/.test(e)},z.generateSessionId=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=(new Date).getTime();try{e=parseInt(e)}catch(i){e=0}return t+=e,window.performance&&"function"==typeof window.performance.now&&(t+=parseInt(window.performance.now())),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var i=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?i:3&i|8).toString(16)}))},z.createEvent=function(e){var t;return"function"==typeof window.Event?t=new Event(e):(t=document.createEvent("Event")).initEvent(e,!0,!0),t},z.adjustTimeByDuration=function(e,t,i){return t&&e&&(e>t||i&&e<t)?t:e},z.createPositionBar=function(e,t){var i=z.createDom("xg-bar","",{"data-index":-1},e);return t.appendChild(i),i},z.getTransformStyle=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{x:0,y:0,scale:1,rotate:0},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i={scale:"".concat(e.scale||1),translate:"".concat(e.x||0,"%, ").concat(e.y||0,"%"),rotate:"".concat(e.rotate||0,"deg")};return Object.keys(i).forEach((function(e){var n=new RegExp("".concat(e,"\\([^\\(]+\\)"),"g"),o="".concat(e,"(").concat(i[e],")");n.test(t)?(n.lastIndex=-1,t=t.replace(n,o)):t+="".concat(o," ")})),t},z.convertDeg=function(e){return Math.abs(e)<=1?360*e:e%360},z.getIndexByTime=function(e,t){var i=t.length,n=-1;if(i<1)return n;if(e<=t[0].end||i<2)n=0;else if(e>t[i-1].end)n=i-1;else for(var o=1;o<i;o++)if(e>t[o-1].end&&e<=t[o].end){n=o;break}return n},z.getOffsetCurrentTime=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,n=-1;if((n=i>=0&&i<t.length?i:z.getIndexByTime(e,t))<0)return-1;var o=t.length,r=t[n],s=r.start,a=r.end,l=r.cTime,c=r.offset;return e<s?l:e>=s&&e<=a?e-c:e>a&&n>=o-1?a:-1},z.getCurrentTimeByOffset=function(e,t){var i=-1;if(!t||t.length<0)return e;for(var n=0;n<t.length;n++)if(e<=t[n].duration){i=n;break}if(-1!==i){var o=t[i].start;return i-1<0?o+e:o+(e-t[i-1].duration)}return e};var X=/(Android)\s([\d.]+)/,q=/(Version)\/([\d.]+)/,Z=["avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","avc1.42E01E","mp4v.20.8","mp4v.20.8, mp4a.40.2","mp4v.20.240, mp4a.40.2"],J={get device(){return J.os.isPc?"pc":"mobile"},get browser(){if("undefined"==typeof navigator)return"";var e=navigator.userAgent.toLowerCase(),t={ie:/rv:([\d.]+)\) like gecko/,firefox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return[].concat(Object.keys(t).filter((function(i){return t[i].test(e)})))[0]},get os(){if("undefined"==typeof navigator)return{};var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),i=/(?:SymbianOS)/.test(e)||t,n=/(?:Android)/.test(e),o=/(?:Firefox)/.test(e),r=/(?:iPad|PlayBook)/.test(e)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1,s=r||n&&!/(?:Mobile)/.test(e)||o&&/(?:Tablet)/.test(e),a=/(?:iPhone)/.test(e)&&!s;return{isTablet:s,isPhone:a,isIpad:r,isIos:a||r,isAndroid:n,isPc:!(a||n||i||s),isSymbian:i,isWindowsPhone:t,isFireFox:o}},get osVersion(){if("undefined"==typeof navigator)return 0;var e=navigator.userAgent,t="",i=(t=/(?:iPhone)|(?:iPad|PlayBook)/.test(e)?q:X)?t.exec(e):[];if(i&&i.length>=3){var n=i[2].split(".");return n.length>0?parseInt(n[0]):0}return 0},get isWeixin(){if("undefined"==typeof navigator)return!1;return!!/(micromessenger)\/([\d.]+)/.exec(navigator.userAgent.toLocaleLowerCase())},isSupportMP4:function(){var e={isSupport:!1,mime:""};if("undefined"==typeof document)return e;if(this.supportResult)return this.supportResult;var t=document.createElement("video");return"function"==typeof t.canPlayType&&Z.map((function(i){"probably"===t.canPlayType('video/mp4; codecs="'.concat(i,'"'))&&(e.isSupport=!0,e.mime+="||".concat(i))})),this.supportResult=e,t=null,e},isMSESupport:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if("undefined"==typeof MediaSource||!MediaSource)return!1;try{return MediaSource.isTypeSupported(e)}catch(t){return this._logger.error(e,t),!1}},isHevcSupported:function(){return!("undefined"==typeof MediaSource||!MediaSource.isTypeSupported)&&(MediaSource.isTypeSupported('video/mp4;codecs="hev1.1.6.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.2.4.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.3.E.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.4.10.L120.90"'))},probeConfigSupported:function(e){var t={supported:!1,smooth:!1,powerEfficient:!1};if(!e||"undefined"==typeof navigator)return Promise.resolve(t);if(navigator.mediaCapabilities&&navigator.mediaCapabilities.decodingInfo)return navigator.mediaCapabilities.decodingInfo(e);var i=e.video||{},n=e.audio||{};try{var o=MediaSource.isTypeSupported(i.contentType),r=MediaSource.isTypeSupported(n.contentType);return Promise.resolve({supported:o&&r,smooth:!1,powerEfficient:!1})}catch(s){return Promise.resolve(t)}}},$="3.0.20",Q={1:"media",2:"media",3:"media",4:"media",5:"media",6:"media"},ee={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},te=T((function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};_(this,e);var n=t&&t.i18n?t.i18n.ERROR_TYPES:null;if(t.media){var o=i.mediaError?i.mediaError:t.media.error||{},r=t.duration,s=t.currentTime,a=t.ended,l=t.src,c=t.currentSrc,u=t.media,h=u.readyState,d=u.networkState,f=i.errorCode||o.code;ee[f]&&(f=ee[f]);var p={playerVersion:$,currentTime:s,duration:r,ended:a,readyState:h,networkState:d,src:l||c,errorType:i.errorType,errorCode:f,message:i.errorMessage||o.message,mediaError:o,originError:i.originError?i.originError.stack:"",host:z.getHostFromUrl(l||c)};return i.ext&&Object.keys(i.ext).map((function(e){p[e]=i.ext[e]})),p}if(arguments.length>1){for(var g={playerVersion:$,domain:document.domain},v=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],y=0;y<arguments.length;y++)g[v[y]]=arguments[y];return g.ex=n?(n[arguments[0]]||{}).msg:"",g}})),ie="play",ne="playing",oe="ended",re="pause",se="error",ae="seeking",le="seeked",ce="timeupdate",ue="waiting",he="canplay",de="durationchange",fe="volumechange",pe="loadeddata",ge="ratechange",ve="progress",ye="loadstart",me="emptied",ke="focus",Ce="blur",be="ready",_e="urlNull",we="autoplay_started",Te="autoplay_was_prevented",xe="complete",Se="replay",Ee="destroy",Pe="urlchange",Ie="download_speed_change",Le="leaveplayer",Ae="enterplayer",Oe="loading",De="fullscreen_change",Re="cssFullscreen_change",Me="mini_state_change",Ne="definition_change",Fe="after_definition_change",He="video_resize",Be="pip_change",Ue="rotate",je="screenShot",Ve="playnext",We="shortcut",Ge="xglog",ze="user_action",Ke="reset",Ye="source_error",Xe="source_success",qe=["play","playing","ended","pause","error","seeking","seeked","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","loadeddata","loadedmetadata","ratechange","progress","loadstart","emptied","stalled","suspend","abort","lowdecode"],Ze={STATS_INFO:"stats_info",STATS_DOWNLOAD:"stats_download",STATS_RESET:"stats_reset"},Je="fps_stuck";const $e=Object.freeze(Object.defineProperty({__proto__:null,ABORT:"abort",AFTER_DEFINITION_CHANGE:Fe,AUTOPLAY_PREVENTED:Te,AUTOPLAY_STARTED:we,BEFORE_DEFINITION_CHANGE:"before_definition_change",BUFFER_CHANGE:"bufferedChange",CANPLAY:he,CANPLAY_THROUGH:"canplaythrough",COMPLETE:xe,CSS_FULLSCREEN_CHANGE:Re,DEFINITION_CHANGE:Ne,DESTROY:Ee,DOWNLOAD_SPEED_CHANGE:Ie,DURATION_CHANGE:de,EMPTIED:me,ENDED:oe,ENTER_PLAYER:Ae,ERROR:se,FPS_STUCK:Je,FULLSCREEN_CHANGE:De,LEAVE_PLAYER:Le,LOADED_DATA:pe,LOADED_METADATA:"loadedmetadata",LOADING:Oe,LOAD_START:ye,MINI_STATE_CHANGE:Me,PAUSE:re,PIP_CHANGE:Be,PLAY:ie,PLAYER_BLUR:Ce,PLAYER_FOCUS:ke,PLAYING:ne,PLAYNEXT:Ve,PROGRESS:ve,RATE_CHANGE:ge,READY:be,REPLAY:Se,RESET:Ke,RETRY:"retry",ROTATE:Ue,SCREEN_SHOT:je,SEEKED:le,SEEKING:ae,SEI_PARSED:"SEI_PARSED",SHORTCUT:We,SOURCE_ERROR:Ye,SOURCE_SUCCESS:Xe,STALLED:"stalled",STATS_EVENTS:Ze,SUSPEND:"suspend",SWITCH_SUBTITLE:"switch_subtitle",TIME_UPDATE:ce,URL_CHANGE:Pe,URL_NULL:_e,USER_ACTION:ze,VIDEO_EVENTS:qe,VIDEO_RESIZE:He,VOLUME_CHANGE:fe,WAITING:ue,XGLOG:Ge},Symbol.toStringTag,{value:"Module"}));function Qe(e,t){this&&this.emit&&("error"===e?this.errorHandler(e,t.error):this.emit(e,t))}var et=function(){S(t,B);var e=A(t);function t(i){var n;_(this,t),(n=e.call(this,i))._hasStart=!1,n._currentTime=0,n._duration=0,n._internalOp={},n._lastMuted=!1,n.vtype="MP4",n._rate=-1,n.mediaConfig=Object.assign({},{controls:!1,autoplay:i.autoplay,playsinline:i.playsinline,"x5-playsinline":i.playsinline,"webkit-playsinline":i.playsinline,"x5-video-player-fullscreen":i["x5-video-player-fullscreen"]||i.x5VideoPlayerFullscreen,"x5-video-orientation":i["x5-video-orientation"]||i.x5VideoOrientation,airplay:i.airplay,"webkit-airplay":i.airplay,tabindex:0|i.tabindex,mediaType:i.mediaType||"video","data-index":-1},i.videoConfig,i.videoAttributes);var o=i["x5-video-player-type"]||i.x5VideoPlayerType;return J.isWeixin&&J.os.isAndroid&&o&&(n.mediaConfig["x5-video-player-type"]=o,delete n.mediaConfig.playsinline,delete n.mediaConfig["webkit-playsinline"],delete n.mediaConfig["x5-playsinline"]),i.loop&&(n.mediaConfig.loop="loop"),i.autoplayMuted&&!Object.prototype.hasOwnProperty.call(n.mediaConfig,"muted")&&(n.mediaConfig.muted=!0),n.media=z.createDom(n.mediaConfig.mediaType,"",n.mediaConfig,""),i.defaultPlaybackRate&&(n.media.defaultPlaybackRate=n.media.playbackRate=i.defaultPlaybackRate),"Number"===z.typeOf(i.volume)&&(n.volume=i.volume),i.autoplayMuted&&(n.media.muted=!0,n._lastMuted=!0),i.autoplay&&(n.media.autoplay=!0),n._interval={},n.mediaEventMiddleware={},n.attachVideoEvents(),n}return T(t,[{key:"setEventsMiddleware",value:function(e){var t=this;Object.keys(e).map((function(i){t.mediaEventMiddleware[i]=e[i]}))}},{key:"removeEventsMiddleware",value:function(e){var t=this;Object.keys(e).map((function(e){delete t.mediaEventMiddleware[e]}))}},{key:"attachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers||(this._evHandlers=qe.map((function(t){var i="on".concat(t.charAt(0).toUpperCase()).concat(t.slice(1));return"function"==typeof e[i]&&e.on(t,e[i]),x({},t,function(e,t){return function(i,n){var o={player:t,eventName:e,originalEvent:i,detail:i.detail||{},timeStamp:i.timeStamp,currentTime:t.currentTime,duration:t.duration,paused:t.paused,ended:t.ended,isInternalOp:!!t._internalOp[i.type],muted:t.muted,volume:t.volume,host:z.getHostFromUrl(t.currentSrc),vtype:t.vtype};if(t.removeInnerOP(i.type),"timeupdate"===e&&(t._currentTime=t.media&&t.media.currentTime),"ratechange"===e){var r=t.media?t.media.playbackRate:0;if(r&&t._rate===r)return;t._rate=t.media&&t.media.playbackRate}if("durationchange"===e&&(t._duration=t.media.duration),"volumechange"===e&&(o.isMutedChange=t._lastMuted!==t.muted,t._lastMuted=t.muted),"error"===e&&(o.error=n||t.video.error),t.mediaEventMiddleware[e]){var s=Qe.bind(t,e,o);try{t.mediaEventMiddleware[e].call(t,o,s)}catch(a){throw Qe.call(t,e,o),a}}else Qe.call(t,e,o)}}(t,e))}))),this._evHandlers.forEach((function(e){var i=Object.keys(e)[0];t.addEventListener(i,e[i],!1)}))}},{key:"detachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers.forEach((function(e){var i=Object.keys(e)[0];t.removeEventListener(i,e[i],!1)})),this._evHandlers.forEach((function(t){var i=Object.keys(t)[0],n="on".concat(i.charAt(0).toUpperCase()).concat(i.slice(1));"function"==typeof e[n]&&e.off(i,e[n])})),this._evHandlers=null}},{key:"_attachSourceEvents",value:function(e,t){var i=this;e.removeAttribute("src"),e.load(),t.forEach((function(e,t){i.media.appendChild(z.createDom("source","",{src:"".concat(e.src),type:"".concat(e.type||""),"data-index":t+1}))}));var n=e.children;if(n){this._videoSourceCount=n.length,this._videoSourceIndex=n.length,this._vLoadeddata=function(e){i.emit(Xe,{src:e.target.currentSrc,host:z.getHostFromUrl(e.target.currentSrc)})};for(var o=null,r=0;r<this._evHandlers.length;r++)if("error"===Object.keys(this._evHandlers[r])[0]){o=this._evHandlers[r];break}!this._sourceError&&(this._sourceError=function(e){var t=parseInt(e.target.getAttribute("data-index"),10);if(i._videoSourceIndex--,0===i._videoSourceIndex||t>=i._videoSourceCount){var n={code:4,message:"sources_load_error"};o?o.error(e,n):i.errorHandler("error",n)}var r=Q[4];i.emit(Ye,new te(i,{errorType:r,errorCode:4,errorMessage:"sources_load_error",mediaError:{code:4,message:"sources_load_error"},src:e.target.src}))});for(var s=0;s<n.length;s++)n[s].addEventListener("error",this._sourceError);e.addEventListener("loadeddata",this._vLoadeddata)}}},{key:"_detachSourceEvents",value:function(e){var t=e.children;if(t&&0!==t.length&&this._sourceError){for(var i=0;i<t.length;i++)t[i].removeEventListener("error",this._sourceError);for(;t.length>0;)e.removeChild(t[0]);this._vLoadeddata&&e.removeEventListener("loadeddata",this._vLoadeddata)}}},{key:"errorHandler",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.media&&(this.media.error||t)){var i=this.media.error||t,n=i.code?Q[i.code]:"other";i.message;this.media.currentSrc||(i={code:6,message:"empty_src"}),this.emit(e,new te(this,{errorType:n,errorCode:i.code,errorMessage:i.message||"",mediaError:i}))}}},{key:"destroy",value:function(){for(var e in this.media&&(this.media.pause&&(this.media.pause(),this.media.muted=!0),this.media.removeAttribute("src"),this.media.load()),this._currentTime=0,this._duration=0,this.mediaConfig=null,this._interval)Object.prototype.hasOwnProperty.call(this._interval,e)&&(clearInterval(this._interval[e]),this._interval[e]=null);this.detachVideoEvents(),this.media=null,this.mediaEventMiddleware={},this.removeAllListeners()}},{key:"video",get:function(){return this.media},set:function(e){this.media=e}},{key:"play",value:function(){return this.media?this.media.play():null}},{key:"pause",value:function(){this.media&&this.media.pause()}},{key:"load",value:function(){this.media&&this.media.load()}},{key:"canPlayType",value:function(e){return!!this.media&&this.media.canPlayType(e)}},{key:"getBufferedRange",value:function(e){var t=[0,0];if(!this.media)return t;e||(e=this.media.buffered);var i=this.media.currentTime;if(e)for(var n=0,o=e.length;n<o&&(t[0]=e.start(n),t[1]=e.end(n),!(t[0]<=i&&i<=t[1]));n++);return t[0]-i<=0&&i-t[1]<=0?t:[0,0]}},{key:"autoplay",get:function(){return!!this.media&&this.media.autoplay},set:function(e){this.media&&(this.media.autoplay=e)}},{key:"buffered",get:function(){return this.media?this.media.buffered:null}},{key:"buffered2",get:function(){return this.media&&this.media.buffered?z.getBuffered2(this.media.buffered):null}},{key:"bufferedPoint",get:function(){var e={start:0,end:0};if(!this.media)return e;var t=this.media.buffered;if(!t||0===t.length)return e;for(var i=0;i<t.length;i++)if((t.start(i)<=this.currentTime||t.start(i)<.1)&&t.end(i)>=this.currentTime)return{start:t.start(i),end:t.end(i)};return e}},{key:"crossOrigin",get:function(){return this.media?this.media.crossOrigin:""},set:function(e){this.media&&(this.media.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.media?this.media.currentSrc:""},set:function(e){this.media&&(this.media.currentSrc=e)}},{key:"currentTime",get:function(){return this.media?void 0!==this.media.currentTime?this.media.currentTime:this._currentTime:0},set:function(e){this.media&&(this.media.currentTime=e)}},{key:"defaultMuted",get:function(){return!!this.media&&this.media.defaultMuted},set:function(e){this.media&&(this.media.defaultMuted=e)}},{key:"duration",get:function(){return this._duration}},{key:"ended",get:function(){return!!this.media&&this.media.ended}},{key:"error",get:function(){return this.media.error}},{key:"errorNote",get:function(){if(!this.media.error)return"";return["MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED"][this.media.error.code-1]}},{key:"loop",get:function(){return!!this.media&&this.media.loop},set:function(e){this.media&&(this.media.loop=e)}},{key:"muted",get:function(){return!!this.media&&this.media.muted},set:function(e){this.media&&this.media.muted!==e&&(this._lastMuted=this.media.muted,this.media.muted=e)}},{key:"networkState",get:function(){return this.media.networkState}},{key:"paused",get:function(){return!this.media||this.media.paused}},{key:"playbackRate",get:function(){return this.media?this.media.playbackRate:0},set:function(e){this.media&&e!==1/0&&(this.media.defaultPlaybackRate=e,this.media.playbackRate=e)}},{key:"played",get:function(){return this.media?this.media.played:null}},{key:"preload",get:function(){return!!this.media&&this.media.preload},set:function(e){this.media&&(this.media.preload=e)}},{key:"readyState",get:function(){return this.media.readyState}},{key:"seekable",get:function(){return!!this.media&&this.media.seekable}},{key:"seeking",get:function(){return!!this.media&&this.media.seeking}},{key:"src",get:function(){return this.media?this.media.src:""},set:function(e){this.media&&(this.emit(Pe,e),this.emit(ue),this._currentTime=0,this._duration=0,z.isMSE(this.media)?this.onWaiting():(this._detachSourceEvents(this.media),"Array"===z.typeOf(e)?this._attachSourceEvents(this.media,e):e?this.media.src=e:this.media.removeAttribute("src"),this.load()))}},{key:"volume",get:function(){return this.media?this.media.volume:0},set:function(e){e!==1/0&&this.media&&(this.media.volume=e)}},{key:"aspectRatio",get:function(){return this.media?this.media.videoWidth/this.media.videoHeight:0}},{key:"addInnerOP",value:function(e){this._internalOp[e]=!0}},{key:"removeInnerOP",value:function(e){delete this._internalOp[e]}},{key:"emit",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=O(E(t.prototype),"emit",this)).call.apply(n,[this,e,i].concat(r))}},{key:"on",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=O(E(t.prototype),"on",this)).call.apply(n,[this,e,i].concat(r))}},{key:"once",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=O(E(t.prototype),"once",this)).call.apply(n,[this,e,i].concat(r))}},{key:"off",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=O(E(t.prototype),"off",this)).call.apply(n,[this,e,i].concat(r))}},{key:"offAll",value:function(){O(E(t.prototype),"removeAllListeners",this).call(this)}}]),t}(),tt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{name:"xgplayer",version:1,db:null,ojstore:{name:"xg-m4a",keypath:"vid"}};_(this,e),this.indexedDB=window.indexedDB||window.webkitindexedDB,this.IDBKeyRange=window.IDBKeyRange||window.webkitIDBKeyRange,this.myDB=t}return T(e,[{key:"openDB",value:function(e){var t=this,i=this,n=this.myDB.version||1,o=i.indexedDB.open(i.myDB.name,n);o.onerror=function(e){},o.onsuccess=function(n){t.myDB.db=n.target.result,e.call(i)},o.onupgradeneeded=function(e){var t=e.target.result;e.target.transaction,t.objectStoreNames.contains(i.myDB.ojstore.name)||t.createObjectStore(i.myDB.ojstore.name,{keyPath:i.myDB.ojstore.keypath})}}},{key:"deletedb",value:function(){this.indexedDB.deleteDatabase(this.myDB.name)}},{key:"closeDB",value:function(){this.myDB.db.close()}},{key:"addData",value:function(e,t){for(var i,n=this.myDB.db.transaction(e,"readwrite").objectStore(e),o=0;o<t.length;o++)(i=n.add(t[o])).onerror=function(){},i.onsuccess=function(){}}},{key:"putData",value:function(e,t){for(var i,n=this.myDB.db.transaction(e,"readwrite").objectStore(e),o=0;o<t.length;o++)(i=n.put(t[o])).onerror=function(){},i.onsuccess=function(){}}},{key:"getDataByKey",value:function(e,t,i){var n=this,o=this.myDB.db.transaction(e,"readwrite").objectStore(e).get(t);o.onerror=function(){i.call(n,null)},o.onsuccess=function(e){var t=e.target.result;i.call(n,t)}}},{key:"deleteData",value:function(e,t){this.myDB.db.transaction(e,"readwrite").objectStore(e).delete(t)}},{key:"clearData",value:function(e){this.myDB.db.transaction(e,"readwrite").objectStore(e).clear()}}]),e}(),it=["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],nt=["requestFullscreen","webkitRequestFullscreen","mozRequestFullScreen","msRequestFullscreen"],ot=["exitFullscreen","webkitExitFullscreen","mozCancelFullScreen","msExitFullscreen"],rt="data-xgplayerid";function st(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];var s=t.call.apply(t,[e].concat(o));i&&"function"==typeof i&&(s&&s.then?s.then((function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];i.call.apply(i,[e].concat(n))})):i.call.apply(i,[e].concat(o)))}function at(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[e]&&(this.__hooks[e]=null),function(){var n=arguments,o=this;if(i.pre)try{var r;(r=i.pre).call.apply(r,[this].concat(Array.prototype.slice.call(arguments)))}catch(a){throw a.message="[pluginName: ".concat(this.pluginName,":").concat(e,":pre error] >> ").concat(a.message),a}if(this.__hooks&&this.__hooks[e])try{var s=gt(this,e,t);s?s.then?s.then((function(e){!1!==e&&st.apply(void 0,[o,t,i.next].concat(D(n)))})).catch((function(e){throw e})):st.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments))):void 0===s&&st.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments)))}catch(a){throw a.message="[pluginName: ".concat(this.pluginName,":").concat(e,"] >> ").concat(a.message),a}else st.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments)))}.bind(this)}function lt(e,t){var i=this.__hooks;if(!i||!Array.isArray(i[e]))return-1;for(var n=i[e],o=0;o<n.length;o++)if(n[o]===t)return o;return-1}function ct(e,t){var i=this.__hooks;if(i)return!!i.hasOwnProperty(e)&&(Array.isArray(i[e])||(i[e]=[]),-1===lt.call(this,e,t)&&i[e].push(t),!0)}function ut(e,t){var i=this.__hooks;if(i){if(Array.isArray(i[e])){var n=i[e],o=lt.call(this,e,t);-1!==o&&n.splice(o,1)}delete i[e]}}function ht(e){if(this.plugins&&this.plugins[e.toLowerCase()]){for(var t=this.plugins[e.toLowerCase()],i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t.useHooks&&t.useHooks.apply(t,n)}}function dt(e){if(this.plugins&&this.plugins[e.toLowerCase()]){var t=this.plugins[e.toLowerCase()];if(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t.removeHooks&&t.removeHooks.apply(t,n)}}}function ft(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.__hooks={},t&&t.map((function(t){e.__hooks[t]=null})),Object.defineProperty(e,"hooks",{get:function(){return e.__hooks&&Object.keys(e.__hooks).map((function(t){if(e.__hooks[t])return t}))}})}function pt(e){e.__hooks=null}function gt(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];if(e.__hooks&&Array.isArray(e.__hooks[t])){var s=e.__hooks[t],a=-1;return function e(t,i,n){for(var o=arguments.length,r=new Array(o>3?o-3:0),l=3;l<o;l++)r[l-3]=arguments[l];if(a++,0===s.length||a===s.length)return n.call.apply(n,[t,t].concat(r));var c=s[a],u=c.call.apply(c,[t,t].concat(r));return u&&u.then?u.then((function(o){return!1===o?null:e.apply(void 0,[t,i,n].concat(r))})).catch((function(e){})):!1!==u?e.apply(void 0,[t,i,n].concat(r)):void 0}.apply(void 0,[e,t,i].concat(o))}return i.call.apply(i,[e,e].concat(o))}function vt(e,t){W.logError("[".concat(e,"] event or callback cant be undefined or null when call ").concat(t))}var yt,mt,kt,Ct,bt=function(){function e(t){_(this,e),z.checkIsFunction(this.beforeCreate)&&this.beforeCreate(t),ft(this),this.__args=t,this.__events={},this.__onceEvents={},this.config=t.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(t)}return T(e,[{key:"beforeCreate",value:function(e){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(e){this.player=e.player,this.playerConfig=e.player&&e.player.config,this.pluginName=e.pluginName?e.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=e.player&&e.player.logger}},{key:"updateLang",value:function(e){e||(e=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var e=z.checkTouchSupport()?"touch":"mouse";return!this.playerConfig||"touch"!==this.playerConfig.domEventType&&"mouse"!==this.playerConfig.domEventType||(e=this.playerConfig.domEventType),e}},{key:"on",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(this.__events[e]=t,this.player.on(e,t)):Array.isArray(e)&&e.forEach((function(e){i.__events[e]=t,i.player.on(e,t)})):vt(this.pluginName,"plugin.on(event, callback)")}},{key:"once",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(this.__onceEvents[e]=t,this.player.once(e,t)):Array.isArray(e)&&e.forEach((function(n){i.__onceEvents[n]=t,i.player.once(e,t)})):vt(this.pluginName,"plugin.once(event, callback)")}},{key:"off",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(delete this.__events[e],this.player.off(e,t)):Array.isArray(e)&&e.forEach((function(n){delete i.__events[e],i.player.off(n,t)})):vt(this.pluginName,"plugin.off(event, callback)")}},{key:"offAll",value:function(){var e=this;["__events","__onceEvents"].forEach((function(t){Object.keys(e[t]).forEach((function(i){e[t][i]&&e.off(i,e[t][i]),i&&delete e[t][i]}))})),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(e){var t;if(this.player){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];(t=this.player).emit.apply(t,[e].concat(n))}}},{key:"emitUserAction",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.player){var n=C(C({},i),{},{pluginName:this.pluginName});this.player.emitUserAction(e,t,n)}}},{key:"hook",value:function(e,t){return at.call.apply(at,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,t){for(var i=arguments.length,n=new Array(i>2?i-2:0),o=2;o<i;o++)n[o-2]=arguments[o];return ct.call.apply(ct,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,t){for(var i=arguments.length,n=new Array(i>2?i-2:0),o=2;o<i;o++)n[o-2]=arguments[o];return ut.call.apply(ut,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(this.player)return i&&(t.pluginName=i),this.player.registerPlugin({plugin:e,options:t})}},{key:"getPlugin",value:function(e){return this.player?this.player.getPlugin(e):null}},{key:"__destroy",value:function(){var e=this,t=this.player,i=this.pluginName;this.offAll(),z.clearAllTimers(this),z.checkIsFunction(this.destroy)&&this.destroy(),["player","playerConfig","pluginName","logger","__args","__hooks"].map((function(t){e[t]=null})),t.unRegisterPlugin(i),pt(this)}}],[{key:"defineGetterOrSetter",value:function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&Object.defineProperty(e,i,t[i])}},{key:"defineMethod",value:function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&"function"==typeof t[i]&&Object.defineProperty(e,i,{configurable:!0,value:t[i]})}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),e}();const _t=l(function(){if(Ct)return kt;Ct=1;var e=function(){if(mt)return yt;if(mt=1,"undefined"!=typeof Element&&!Element.prototype.matches){var e=Element.prototype;e.matches=e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector}return yt=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}}();function t(e,t,n,o,r){var s=i.apply(this,arguments);return e.addEventListener(n,s,r),{destroy:function(){e.removeEventListener(n,s,r)}}}function i(t,i,n,o){return function(n){n.delegateTarget=e(n.target,i),n.delegateTarget&&o.call(t,n)}}return kt=function(e,i,n,o,r){return"function"==typeof e.addEventListener?t.apply(null,arguments):"function"==typeof n?t.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return t(e,i,n,o,r)})))}}());var wt={CONTROLS:"controls",ROOT:"root"},Tt={ROOT:"root",ROOT_LEFT:"rootLeft",ROOT_RIGHT:"rootRight",ROOT_TOP:"rootTop",CONTROLS_LEFT:"controlsLeft",CONTROLS_RIGTH:"controlsRight",CONTROLS_RIGHT:"controlsRight",CONTROLS_CENTER:"controlsCenter",CONTROLS:"controls"},xt="xg-icon-disable";function St(e){return!!e&&(e.indexOf&&/^(?:http|data:|\/)/.test(e))}function Et(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",r=null;if(e instanceof window.Element)return z.addClass(e,i),Object.keys(n).map((function(t){e.setAttribute(t,n[t])})),e;if(St(e)||St(e.url))return n.src=St(e)?e:e.url||"",r=z.createDom(e.tag||"img","",n,"xg-img ".concat(i));if("function"==typeof e)try{return(r=e())instanceof window.Element?(z.addClass(r,i),Object.keys(n).map((function(e){r.setAttribute(e,n[e])})),r):(W.logWarn("warn>>icons.".concat(t," in config of plugin named [").concat(o,"] is a function mast return an Element Object")),null)}catch(s){return W.logError("Plugin named [".concat(o,"]:createIcon"),s),null}return"string"==typeof e?z.createDomFromHtml(e,n,i):(W.logWarn("warn>>icons.".concat(t," in config of plugin named [").concat(o,"] is invalid")),null)}function Pt(e,t){var i=t.config.icons||t.playerConfig.icons;Object.keys(e).map((function(n){var o=e[n],r=o&&o.class?o.class:"",s=o&&o.attr?o.attr:{},a=null;i&&i[n]&&(r=function(e,t){return"object"===b(e)&&e.class&&"string"==typeof e.class?"".concat(t," ").concat(e.class):t}(i[n],r),s=function(e,t){return"object"===b(e)&&e.attr&&"object"===b(e.attr)&&Object.keys(e.attr).map((function(i){t[i]=e.attr[i]})),t}(i[n],s),a=Et(i[n],n,r,s,t.pluginName)),!a&&o&&(a=Et(o.icon?o.icon:o,s,r,{},t.pluginName)),t.icons[n]=a}))}var It=function(){S(t,bt);var e=A(t);function t(){var i,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return _(this,t),(i=e.call(this,n)).__delegates=[],i}return T(t,[{key:"__init",value:function(e){if(O(E(t.prototype),"__init",this).call(this,e),e.root){var i=e.root,n=null;this.icons={},this.root=null,this.parent=null,Pt(this.registerIcons()||{},this),this.langText={};var o,r,s=this.registerLanguageTexts()||{};o=s,r=this,Object.keys(o).map((function(e){Object.defineProperty(r.langText,e,{get:function(){var t=r.lang,i=r.i18n;return i[e]?i[e]:o[e]&&o[e][t]||""}})}));var a="";try{a=this.render()}catch(u){throw W.logError("Plugin:".concat(this.pluginName,":render"),u),new Error("Plugin:".concat(this.pluginName,":render:").concat(u.message))}if(a)(n=t.insert(a,i,e.index)).setAttribute("data-index",e.index);else{if(!e.tag)return;(n=z.createDom(e.tag,"",e.attr,e.name)).setAttribute("data-index",e.index),i.appendChild(n)}this.root=n,this.parent=i;var l=this.config.attr||{},c=this.config.style||{};this.setAttr(l),this.setStyle(c),this.config.index&&this.root.setAttribute("data-index",this.config.index),this.__registerChildren()}}},{key:"__registerChildren",value:function(){var e=this;if(this.root){this._children=[];var t=this.children();t&&"object"===b(t)&&Object.keys(t).length>0&&Object.keys(t).map((function(i){var n,o,r=i,s=t[r],a={root:e.root};"function"==typeof s?(n=e.config[r]||{},o=s):"object"===b(s)&&"function"==typeof s.plugin&&(n=s.options?z.deepCopy(e.config[r]||{},s.options):e.config[r]||{},o=s.plugin),a.config=n,void 0!==n.index&&(a.index=n.index),n.root&&(a.root=n.root),e.registerPlugin(o,a,r)}))}}},{key:"updateLang",value:function(e){e||(e=this.lang);var t=this.root,i=this.i18n,n=this.langText;t&&function e(t,i){for(var n=0;n<t.children.length;n++)t.children[n].children.length>0?e(t.children[n],i):i(t.children[n])}(t,(function(t){var o=t.getAttribute&&t.getAttribute("lang-key");if(o){var r=i[o.toUpperCase()]||n[o];r&&(t.innerHTML="function"==typeof r?r(e):r)}}))}},{key:"lang",get:function(){return this.player.lang}},{key:"changeLangTextKey",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=this.i18n||{},n=this.langText;e.setAttribute&&e.setAttribute("lang-key",t);var o=i[t]||n[t]||"";o&&(e.innerHTML=o)}},{key:"plugins",value:function(){return this._children}},{key:"disable",value:function(){this.config.disable=!0,z.addClass(this.find(".xgplayer-icon"),xt)}},{key:"enable",value:function(){this.config.disable=!1,z.removeClass(this.find(".xgplayer-icon"),xt)}},{key:"children",value:function(){return{}}},{key:"registerPlugin",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";i.root=i.root||this.root;var o=O(E(t.prototype),"registerPlugin",this).call(this,e,i,n);return this._children.push(o),o}},{key:"registerIcons",value:function(){return{}}},{key:"registerLanguageTexts",value:function(){return{}}},{key:"find",value:function(e){if(this.root)return this.root.querySelector(e)}},{key:"bind",value:function(e,i,n){var o=this;if(arguments.length<3&&"function"==typeof i)Array.isArray(e)?e.forEach((function(e){o.bindEL(e,i)})):this.bindEL(e,i);else{var r=t.delegate.call(this,this.root,e,i,n);this.__delegates=this.__delegates.concat(r)}}},{key:"unbind",value:function(e,t){var i=this;if(arguments.length<3&&"function"==typeof t)Array.isArray(e)?e.forEach((function(e){i.unbindEL(e,t)})):this.unbindEL(e,t);else for(var n="".concat(e,"_").concat(t),o=0;o<this.__delegates.length;o++)if(this.__delegates[o].key===n){this.__delegates[o].destroy(),this.__delegates.splice(o,1);break}}},{key:"setStyle",value:function(e,t){var i=this;if(this.root)return"String"===z.typeOf(e)?this.root.style[e]=t:void("Object"===z.typeOf(e)&&Object.keys(e).map((function(t){i.root.style[t]=e[t]})))}},{key:"setAttr",value:function(e,t){var i=this;if(this.root)return"String"===z.typeOf(e)?this.root.setAttribute(e,t):void("Object"===z.typeOf(e)&&Object.keys(e).map((function(t){i.root.setAttribute(t,e[t])})))}},{key:"setHtml",value:function(e,t){this.root&&(this.root.innerHTML=e,"function"==typeof t&&t())}},{key:"bindEL",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.addEventListener(e,t,i)}},{key:"unbindEL",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.removeEventListener(e,t,i)}},{key:"show",value:function(e){if(this.root)return this.root.style.display=void 0!==e?e:"block","none"===window.getComputedStyle(this.root,null).getPropertyValue("display")?this.root.style.display="block":void 0}},{key:"hide",value:function(){this.root&&(this.root.style.display="none")}},{key:"appendChild",value:function(e,t){if(!this.root)return null;if(arguments.length<2&&arguments[0]instanceof window.Element)return this.root.appendChild(arguments[0]);if(!(t&&t instanceof window.Element))return null;try{return"string"==typeof e?this.find(e).appendChild(t):e.appendChild(t)}catch(i){return W.logError("Plugin:appendChild",i),null}}},{key:"render",value:function(){return""}},{key:"destroy",value:function(){}},{key:"__destroy",value:function(){var e=this,i=this.player;this.__delegates.map((function(e){e.destroy()})),this.__delegates=[],this._children instanceof Array&&(this._children.map((function(e){i.unRegisterPlugin(e.pluginName)})),this._children=null),this.root&&(this.root.hasOwnProperty("remove")?this.root.remove():this.root.parentNode&&this.root.parentNode.removeChild(this.root)),O(E(t.prototype),"__destroy",this).call(this),this.icons={},["root","parent"].map((function(t){e[t]=null}))}}],[{key:"insert",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=t.children.length,o=Number(i),r=e instanceof window.Node;if(n){for(var s=0,a=null,l="";s<n;s++){a=t.children[s];var c=Number(a.getAttribute("data-index"));if(c>=o){l="beforebegin";break}c<o&&(l="afterend")}return r?"afterend"===l?t.appendChild(e):t.insertBefore(e,a):a.insertAdjacentHTML(l,e),"afterend"===l?t.children[t.children.length-1]:t.children[s]}return r?t.appendChild(e):t.insertAdjacentHTML("beforeend",e),t.children[t.children.length-1]}},{key:"defaultConfig",get:function(){return{}}},{key:"delegate",value:function(e,t,i,n){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],r=[];if(e instanceof window.Node&&"function"==typeof n)if(Array.isArray(i))i.forEach((function(i){var s=_t(e,t,i,n,o);s.key="".concat(t,"_").concat(i),r.push(s)}));else{var s=_t(e,t,i,n,o);s.key="".concat(t,"_").concat(i),r.push(s)}return r}},{key:"ROOT_TYPES",get:function(){return wt}},{key:"POSITIONS",get:function(){return Tt}}]),t}(),Lt=function(){function e(){var t=this;if(_(this,e),x(this,"__trigger",(function(e){var i=(new Date).getTime();t.timeStamp=i;for(var n=0;n<e.length;n++)t.__runHandler(e[n].target)})),this.__handlers=[],this.timeStamp=0,this.observer=null,window.ResizeObserver)try{this.observer=new window.ResizeObserver(function(e,t,i){var n=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return K(i)&&(n="leading"in i?!!i.leading:n,o="trailing"in i?!!i.trailing:o),Y(e,t,{leading:n,trailing:o,maxWait:t})}(this.__trigger,100,{trailing:!0})),this.timeStamp=(new Date).getTime()}catch(i){}}return T(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer.observe(e);for(var i=e.getAttribute(rt),n=this.__handlers,o=-1,r=0;r<n.length;r++)n[r]&&e===n[r].target&&(o=r);o>-1?this.__handlers[o].handler=t:this.__handlers.push({target:e,handler:t,playerId:i})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(i,n){e===i.target&&(t=n)}));try{var i;null===(i=this.observer)||void 0===i||i.unobserve(e)}catch(n){}t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){var e;null===(e=this.observer)||void 0===e||e.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e){for(var t=this.__handlers,i=0;i<t.length;i++)if(t[i]&&e===t[i].target){try{t[i].handler(e)}catch(n){}return!0}return!1}}]),e}(),At=null;var Ot={pluginGroup:{},init:function(e){var t,i,n=e._pluginInfoId;n||(n=(new Date).getTime(),e._pluginInfoId=n),!e.config.closeResizeObserver&&(t=e.root,i=function(){e.resize()},At||(At=new Lt),At.addObserver(t,i)),this.pluginGroup[n]={_originalOptions:e.config||{},_plugins:{}}},formatPluginInfo:function(e,t){var i=null,n=null;return e.plugin&&"function"==typeof e.plugin?(i=e.plugin,n=e.options):(i=e,n={}),t&&(n.config=t||{}),{PLUFGIN:i,options:n}},checkPluginIfExits:function(e,t){for(var i=0;i<t.length;i++)if(e.toLowerCase()===t[i].pluginName.toLowerCase())return!0;return!1},getRootByConfig:function(e,t){for(var i=Object.keys(t),n=null,o=0;o<i.length;o++)if(e.toLowerCase()===i[o].toLowerCase()){n=t[i[o]];break}return"Object"===z.typeOf(n)?{root:n.root,position:n.position}:{}},lazyRegister:function(e,t){var i=this,n=t.timeout||1500;return Promise.race([t.loader().then((function(t){var n;n=t&&t.__esModule?t.default:t,i.register(e,n,t.options)})),new Promise((function(e,t){setTimeout((function(){t(new Error("timeout"))}),n)}))])},register:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e&&t&&"function"==typeof t&&void 0!==t.prototype){var n=e._pluginInfoId;if(n&&this.pluginGroup[n]){this.pluginGroup[n]._plugins||(this.pluginGroup[n]._plugins={});var o=this.pluginGroup[n]._plugins,r=this.pluginGroup[n]._originalOptions;i.player=e;var s=i.pluginName||t.pluginName;if(!s)throw new Error("The property pluginName is necessary");if(!t.isSupported||t.isSupported(e.config.mediaType,e.config.codecType)){i.config||(i.config={});for(var a=Object.keys(r),l=0;l<a.length;l++)if(s.toLowerCase()===a[l].toLowerCase()){var c=r[a[l]];"Object"===z.typeOf(c)?i.config=Object.assign({},i.config,r[a[l]]):"Boolean"===z.typeOf(c)&&(i.config.disable=!c);break}t.defaultConfig&&Object.keys(t.defaultConfig).forEach((function(e){void 0===i.config[e]&&(i.config[e]=t.defaultConfig[e])})),i.root?"string"==typeof i.root&&(i.root=e[i.root]):i.root=e.root,i.index=i.config.index||0;try{o[s.toLowerCase()]&&this.unRegister(n,s.toLowerCase());var u=new t(i);return o[s.toLowerCase()]=u,o[s.toLowerCase()].func=t,u&&"function"==typeof u.afterCreate&&u.afterCreate(),u}catch(h){throw h}}}}},unRegister:function(e,t){e._pluginInfoId&&(e=e._pluginInfoId),t=t.toLowerCase();try{var i=this.pluginGroup[e]._plugins[t];i&&(i.pluginName&&i.__destroy(),delete this.pluginGroup[e]._plugins[t])}catch(n){}},deletePlugin:function(e,t){var i=e._pluginInfoId;i&&this.pluginGroup[i]&&this.pluginGroup[i]._plugins&&delete this.pluginGroup[i]._plugins[t]},getPlugins:function(e){var t=e._pluginInfoId;return t&&this.pluginGroup[t]?this.pluginGroup[t]._plugins:{}},findPlugin:function(e,t){var i=e._pluginInfoId;if(!i||!this.pluginGroup[i])return null;var n=t.toLowerCase();return this.pluginGroup[i]._plugins[n]},beforeInit:function(e){var t=this;function i(e){return e&&e.then?e:new Promise((function(e){e()}))}return new Promise((function(n){if(t.pluginGroup)return(e._loadingPlugins&&e._loadingPlugins.length?Promise.all(e._loadingPlugins):Promise.resolve()).then((function(){var o=e._pluginInfoId;if(t.pluginGroup[o]){var r=t.pluginGroup[o]._plugins,s=[];Object.keys(r).forEach((function(e){if(r[e]&&r[e].beforePlayerInit)try{var t=r[e].beforePlayerInit();s.push(i(t))}catch(n){throw s.push(i(null)),n}})),Promise.all([].concat(s)).then((function(){n()})).catch((function(e){n()}))}else n()}))}))},afterInit:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var i=this.pluginGroup[t]._plugins;Object.keys(i).forEach((function(e){i[e]&&i[e].afterPlayerInit&&i[e].afterPlayerInit()}))}},setLang:function(e,t){var i=t._pluginInfoId;if(i&&this.pluginGroup[i]){var n=this.pluginGroup[i]._plugins;Object.keys(n).forEach((function(t){if(n[t].updateLang)n[t].updateLang(e);else try{n[t].lang=e}catch(i){}}))}},reRender:function(e){var t=this,i=e._pluginInfoId;if(i&&this.pluginGroup[i]){var n=[],o=this.pluginGroup[i]._plugins;Object.keys(o).forEach((function(e){"controls"!==e&&o[e]&&(n.push({plugin:o[e].func,options:o[e].__args}),t.unRegister(i,e))})),n.forEach((function(i){t.register(e,i.plugin,i.options)}))}},onPluginsReady:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var i=this.pluginGroup[t]._plugins||{};Object.keys(i).forEach((function(e){i[e].onPluginsReady&&"function"==typeof i[e].onPluginsReady&&i[e].onPluginsReady()}))}},destroy:function(e){var t=e._pluginInfoId;if(this.pluginGroup[t]){var i,n,o;i=e.root,null===(o=At)||void 0===o||o.unObserver(i,n);for(var r=this.pluginGroup[t]._plugins,s=0,a=Object.keys(r);s<a.length;s++){var l=a[s];this.unRegister(t,l)}delete this.pluginGroup[t],delete e._pluginInfoId}}},Dt={DEFAULT:"xgplayer",DEFAULT_SKIN:"xgplayer-skin-default",ENTER:"xgplayer-is-enter",PAUSED:"xgplayer-pause",PLAYING:"xgplayer-playing",ENDED:"xgplayer-ended",CANPLAY:"xgplayer-canplay",LOADING:"xgplayer-isloading",ERROR:"xgplayer-is-error",REPLAY:"xgplayer-replay",NO_START:"xgplayer-nostart",ACTIVE:"xgplayer-active",INACTIVE:"xgplayer-inactive",FULLSCREEN:"xgplayer-is-fullscreen",CSS_FULLSCREEN:"xgplayer-is-cssfullscreen",ROTATE_FULLSCREEN:"xgplayer-rotate-fullscreen",PARENT_ROTATE_FULLSCREEN:"xgplayer-rotate-parent",PARENT_FULLSCREEN:"xgplayer-fullscreen-parent",INNER_FULLSCREEN:"xgplayer-fullscreen-inner",NO_CONTROLS:"no-controls",FLEX_CONTROLS:"flex-controls",CONTROLS_FOLLOW:"controls-follow",CONTROLS_AUTOHIDE:"controls-autohide",TOP_BAR_AUTOHIDE:"top-bar-autohide",NOT_ALLOW_AUTOPLAY:"not-allow-autoplay",SEEKING:"seeking",PC:"xgplayer-pc",MOBILE:"xgplayer-mobile",MINI:"xgplayer-mini"};function Rt(){return{id:"",el:null,url:"",domEventType:"default",nullUrlStart:!1,width:600,height:337.5,fluid:!1,fitVideoSize:"fixed",videoFillMode:"auto",volume:.6,autoplay:!1,autoplayMuted:!1,loop:!1,isLive:!1,zoom:1,videoInit:!0,poster:"",isMobileSimulateMode:!1,defaultPlaybackRate:1,execBeforePluginsCall:null,allowSeekAfterEnded:!0,enableContextmenu:!0,closeVideoClick:!1,closeVideoDblclick:!1,closePlayerBlur:!1,closeDelayBlur:!1,leavePlayerTime:3e3,closePlayVideoFocus:!1,closePauseVideoFocus:!1,closeFocusVideoFocus:!0,closeControlsBlur:!0,topBarAutoHide:!0,videoAttributes:{},startTime:0,seekedStatus:"play",miniprogress:!1,disableSwipeHandler:function(){},enableSwipeHandler:function(){},preProcessUrl:null,ignores:[],whitelist:[],inactive:3e3,lang:(e=(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase(),"zh-cn"===e&&(e="zh"),e),controls:!0,marginControls:!1,fullscreenTarget:null,screenShot:!1,rotate:!1,pip:!1,download:!1,mini:!1,cssFullscreen:!0,keyShortcut:!0,presets:[],plugins:[],playbackRate:1,definition:{list:[]},playsinline:!0,customDuration:0,timeOffset:0,icons:{},i18n:[],tabindex:0,thumbnail:null,videoConfig:{},isHideTips:!1,minWaitDelay:200,commonStyle:{progressColor:"",playedColor:"",cachedColor:"",sliderBtnStyle:{},volumeColor:""}};var e}var Mt=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onMouseEnter",(function(e){var t=I(i),n=t.player;t.playerConfig.closeControlsBlur&&n.focus({autoHide:!1})})),x(I(i),"onMouseLeave",(function(e){I(i).player.focus()})),i}return T(t,[{key:"beforeCreate",value:function(e){e.config.mode||"mobile"!==J.device||(e.config.mode="flex"),e.player.config.marginControls&&(e.config.autoHide=!1)}},{key:"afterCreate",value:function(){var e=this,t=this.config,i=t.disable,n=t.height,o=t.mode;if(!i){"flex"===o&&this.player.addClass(Dt.FLEX_CONTROLS);var r={height:"".concat(n,"px")};Object.keys(r).map((function(t){e.root.style[t]=r[t]})),this.left=this.find("xg-left-grid"),this.center=this.find("xg-center-grid"),this.right=this.find("xg-right-grid"),this.innerRoot=this.find("xg-inner-controls"),this.on(Me,(function(t){t?z.addClass(e.root,"mini-controls"):z.removeClass(e.root,"mini-controls")}));var s=this.playerConfig.isMobileSimulateMode;"mobile"!==J.device&&"mobile"!==s&&(this.bind("mouseenter",this.onMouseEnter),this.bind("mouseleave",this.onMouseLeave))}}},{key:"focus",value:function(){this.player.focus({autoHide:!1})}},{key:"focusAwhile",value:function(){this.player.focus({autoHide:!0})}},{key:"blur",value:function(){this.player.blur({ignorePaused:!0})}},{key:"recoverAutoHide",value:function(){this.config.autoHide&&z.addClass(this.root,Dt.CONTROLS_AUTOHIDE)}},{key:"pauseAutoHide",value:function(){z.removeClass(this.root,Dt.CONTROLS_AUTOHIDE)}},{key:"show",value:function(e){this.root.style.display="",this.player.focus()}},{key:"hide",value:function(){this.root.style.display="none"}},{key:"mode",get:function(){return this.config.mode}},{key:"registerPlugin",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;if(this.root){var o=e.defaultConfig||{};if(!i.root){switch(i.position?i.position:i.config&&i.config.position?i.config.position:o.position){case Tt.CONTROLS_LEFT:i.root=this.left;break;case Tt.CONTROLS_RIGHT:i.root=this.right;break;case Tt.CONTROLS_CENTER:i.root=this.center;break;case Tt.CONTROLS:i.root=this.root;break;default:i.root=this.left}return O(E(t.prototype),"registerPlugin",this).call(this,e,i,n)}}}},{key:"destroy",value:function(){"mobile"!==J.device&&(this.unbind("mouseenter",this.onMouseEnter),this.unbind("mouseleave",this.onMouseLeave))}},{key:"render",value:function(){var e=this.config,t=e.mode,i=e.autoHide,n=e.initShow;if(!e.disable){var o=z.classNames({"xgplayer-controls":!0},{"flex-controls":"flex"===t},{"bottom-controls":"bottom"===t},x({},Dt.CONTROLS_AUTOHIDE,i),{"xgplayer-controls-initshow":n||!i});return'<xg-controls class="'.concat(o,'" unselectable="on">\n    <xg-inner-controls class="xg-inner-controls xg-pos">\n      <xg-left-grid class="xg-left-grid">\n      </xg-left-grid>\n      <xg-center-grid class="xg-center-grid"></xg-center-grid>\n      <xg-right-grid class="xg-right-grid">\n      </xg-right-grid>\n    </xg-inner-controls>\n    </xg-controls>')}}}],[{key:"pluginName",get:function(){return"controls"}},{key:"defaultConfig",get:function(){return{disable:!1,autoHide:!0,mode:"",initShow:!1}}}]),t}(),Nt={lang:{},langKeys:[],textKeys:[]};function Ft(e,t){return Object.keys(t).forEach((function(i){var n,o=z.typeOf(t[i]),r=z.typeOf(e[i]);"Array"===o?("Array"!==r&&(e[i]=[]),(n=e[i]).push.apply(n,D(t[i]))):"Object"===o?("Object"!==r&&(e[i]={}),Ft(e[i],t[i])):e[i]=t[i]})),e}function Ht(){Object.keys(Nt.lang.en).map((function(e){Nt.textKeys[e]=e}))}function Bt(e,t){var i=e.LANG;if(t||(t=Nt),t.lang){var n=e.TEXT||{};"zh"===i&&(i="zh-cn"),t.lang[i]?Ft(t.lang[i],n):(t.langKeys.push(i),t.lang[i]=n),Ht()}}Bt({LANG:"en",TEXT:{ERROR_TYPES:{network:{code:1,msg:"video download error"},mse:{code:2,msg:"stream append error"},parse:{code:3,msg:"parsing error"},format:{code:4,msg:"wrong format"},decoder:{code:5,msg:"decoding error"},runtime:{code:6,msg:"grammatical errors"},timeout:{code:7,msg:"play timeout"},other:{code:8,msg:"other errors"}},HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"PIP",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",OPEN:"Open",MINI_DRAG:"Click and hold to drag",MINISCREEN:"Miniscreen",REFRESH_TIPS:"Please Try",REFRESH:"Refresh",FORWARD:"forward",LIVE_TIP:"Live"}});var Ut={get textKeys(){return Nt.textKeys},get langKeys(){return Nt.langKeys},get lang(){var e={};return Nt.langKeys.map((function(t){e[t]=Nt.lang[t]})),Nt.lang["zh-cn"]&&(e.zh=Nt.lang["zh-cn"]||{}),e},extend:function(e,t){var i=[];if(t||(t=Nt),t.lang){i="Array"!==z.typeOf(e)?Object.keys(e).map((function(t){return{LANG:"zh"===t?"zh-cn":t,TEXT:e[t]}})):e;var n=t.lang;i.map((function(e){"zh"===e.LANG&&(e.LANG="zh-cn"),n[e.LANG]?Ft(n[e.LANG]||{},e.TEXT||{}):Bt(e,t)})),Ht()}},use:Bt,init:function(e){var t,i={lang:{},langKeys:[],textKeys:{},pId:e};return Ft(i.lang,Nt.lang),(t=i.langKeys).push.apply(t,D(Nt.langKeys)),Ft(i.textKeys,Nt.textKeys),i}},jt=1,Vt=2,Wt=3,Gt=4,zt=5,Kt=6,Yt=7,Xt=["ERROR","INITIAL","READY","ATTACHING","ATTACHED","NOTALLOW","RUNNING","ENDED","DESTROYED"],qt={},Zt=null,Jt=function(){S(t,H.EventEmitter);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"add",value:function(e){e&&(qt[e.playerId]=e,1===Object.keys(qt).length&&this.setActive(e.playerId,!0))}},{key:"remove",value:function(e){e&&(e.isUserActive,delete qt[e.playerId])}},{key:"_iterate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var i in qt)if(Object.prototype.hasOwnProperty.call(qt,i)){var n=qt[i];if(t){if(e(n))break}else e(n)}}},{key:"forEach",value:function(e){this._iterate(e)}},{key:"find",value:function(e){var t=null;return this._iterate((function(i){var n=e(i);return n&&(t=i),n}),!0),t}},{key:"findAll",value:function(e){var t=[];return this._iterate((function(i){e(i)&&t.push(i)})),t}},{key:"setActive",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(qt[e])return t?this.forEach((function(t){e===t.playerId?(t.isUserActive=!0,t.isInstNext=!1):t.isUserActive=!1})):qt[e].isUserActive=t,e}},{key:"getActiveId",value:function(){for(var e=Object.keys(qt),t=0;t<e.length;t++){var i=qt[e[t]];if(i&&i.isUserActive)return e[t]}return null}},{key:"setNext",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(qt[e])return t?this.forEach((function(t){e===t.playerId?(t.isUserActive=!1,t.isInstNext=!0):t.isInstNext=!1})):qt[e].isInstNext=t,e}}],[{key:"getInstance",value:function(){return Zt||(Zt=new t),Zt}}]),t}();var $t=["play","pause","replay","retry"],Qt=0,ei=0,ti=null,ii=function(){S(t,et);var e=A(t);function t(i){var n;_(this,t);var o,r=z.deepMerge(Rt(),i);x(I(n=e.call(this,r)),"canPlayFunc",(function(){if(n.config){var e=n.config,t=e.autoplay,i=e.defaultPlaybackRate;W.logInfo("player","canPlayFunc, startTime",n.__startTime),n._seekToStartTime(),n.playbackRate=i,(t||n._useAutoplay)&&n.mediaPlay(),n.off(he,n.canPlayFunc),n.removeClass(Dt.ENTER)}})),x(I(n),"onFullscreenChange",(function(e,t){var i=function(){z.setTimeout(I(n),(function(){n.resize()}),100)},o=z.getFullScreenEl();n._fullActionFrom?n._fullActionFrom="":n.emit(ze,{eventType:"system",action:"switch_fullscreen",pluginName:"player",currentTime:n.currentTime,duration:n.duration,props:[{prop:"fullscreen",from:!0,to:!1}]});var r=function(e,t,i){if(e){var n=e.getAttribute(i);return!(!n||n!==t||"VIDEO"!==e.tagName&&"AUDIO"!==e.tagName)}}(o,n.playerId,rt);if(t||o&&(o===n._fullscreenEl||r))i(),!n.config.closeFocusVideoFocus&&n.media.focus(),n.fullscreen=!0,n.changeFullStyle(n.root,o,Dt.FULLSCREEN),n.emit(De,!0,n._fullScreenOffset),n.cssfullscreen&&n.exitCssFullscreen();else if(n.fullscreen){i();var s=I(n),a=s._fullScreenOffset;if(s.config.needFullscreenScroll?(window.scrollTo(a.left,a.top),z.setTimeout(I(n),(function(){n.fullscreen=!1,n._fullScreenOffset=null}),100)):(!n.config.closeFocusVideoFocus&&n.media.focus(),n.fullscreen=!1,n._fullScreenOffset=null),n.cssfullscreen)n.removeClass(Dt.FULLSCREEN);else{var l=n._fullscreenEl;l||!n.root.contains(e.target)&&e.target!==n.root||(l=e.target),n.recoverFullStyle(n.root,l,Dt.FULLSCREEN)}n._fullscreenEl=null,n.emit(De,!1)}})),x(I(n),"_onWebkitbeginfullscreen",(function(e){n._fullscreenEl=n.media,n.onFullscreenChange(e,!0)})),x(I(n),"_onWebkitendfullscreen",(function(e){n.onFullscreenChange(e,!1)})),ft(I(n),$t),n.config=r,n._pluginInfoId=z.generateSessionId(),(o=I(n)).logInfo=W.logInfo.bind(o),o.logWarn=W.logWarn.bind(o),o.logError=W.logError.bind(o);var s=n.constructor.defaultPreset;if(n.config.presets.length){var a=n.config.presets.indexOf("default");a>=0&&s&&(n.config.presets[a]=s)}else s&&n.config.presets.push(s);if(n.userTimer=null,n.waitTimer=null,n.handleSource=!0,n._state=jt,n.isAd=!1,n.isError=!1,n._hasStart=!1,n.isSeeking=!1,n.isCanplay=!1,n._useAutoplay=!1,n.__startTime=-1,n.rotateDeg=0,n.isActive=!1,n.fullscreen=!1,n.cssfullscreen=!1,n.isRotateFullscreen=!1,n._fullscreenEl=null,n.timeSegments=[],n._cssfullscreenEl=null,n.curDefinition=null,n._orgCss="",n._fullScreenOffset=null,n._videoHeight=0,n._videoWidth=0,n.videoPos={pi:1,scale:0,rotate:-1,x:0,y:0,h:-1,w:-1,vy:0,vx:0},n.sizeInfo={width:0,height:0,left:0,top:0},n._accPlayed={t:0,acc:0,loopAcc:0},n._offsetInfo={currentTime:-1,duration:0},n.innerContainer=null,n.controls=null,n.topBar=null,n.root=null,n.__i18n=Ut.init(n._pluginInfoId),J.os.isAndroid&&J.osVersion>0&&J.osVersion<6&&(n.config.autoplay=!1),n.database=new tt,n.isUserActive=!1,n._onceSeekCanplay=null,n._isPauseBeforeSeek=0,n.innerStates={isActiveLocked:!1},n.instManager=ti,!n._initDOM())return L(n);var l=n.config,c=l.definition,u=void 0===c?{}:c;if(!l.url&&u.list&&u.list.length>0){var h=u.list.find((function(e){return e.definition&&e.definition===u.defaultDefinition}));h||(u.defaultDefinition=u.list[0].definition,h=u.list[0]),n.config.url=h.url,n.curDefinition=h}return n._bindEvents(),n._registerPresets(),n._registerPlugins(),Ot.onPluginsReady(I(n)),n.getInitDefinition(),n.setState(Vt),z.setTimeout(I(n),(function(){n.emit(be)}),0),n.onReady&&n.onReady(),(n.config.videoInit||n.config.autoplay)&&(!n.hasStart||n.state<Gt)&&n.start(),n}return T(t,[{key:"_initDOM",value:function(){var e,t=this;if(this.root=this.config.id?document.getElementById(this.config.id):null,!this.root){var i=this.config.el;if(!i||1!==i.nodeType)return this.emit(se,new te("use",this.config.vid,{line:32,handle:"Constructor",msg:"container id can't be empty"})),!1;this.root=i}var n=function(e){for(var t=Object.keys(qt),i=0;i<t.length;i++){var n=qt[t[i]];if(n.root===e)return n}return null}(this.root);n&&(W.logWarn("The is an Player instance already exists in this.root, destroy it and reinitialize"),n.destroy()),this.root.setAttribute(rt,this.playerId),null===(e=ti)||void 0===e||e.add(this),Ot.init(this),this._initBaseDoms();var o=this.constructor.XgVideoProxy;if(o&&this.mediaConfig.mediaType===o.mediaType){var r=this.innerContainer||this.root;this.detachVideoEvents(this.media);var s=new o(r,this.config,this.mediaConfig);this.attachVideoEvents(s),this.media=s}if(this.media.setAttribute(rt,this.playerId),this.config.controls){var a=this.config.controls.root||null,l=Ot.register(this,Mt,{root:a});this.controls=l}var c="mobile"===this.config.isMobileSimulateMode?"mobile":J.device;if(this.addClass("".concat(Dt.DEFAULT," ").concat(Dt.INACTIVE," xgplayer-").concat(c," ").concat(this.config.controls?"":Dt.NO_CONTROLS)),this.config.autoplay?this.addClass(Dt.ENTER):this.addClass(Dt.NO_START),this.config.fluid){var u=this.config,h=u.width,d=u.height;"number"==typeof h&&"number"==typeof d||(h=600,d=337.5);var f={width:"100%",height:"0","max-width":"100%","padding-top":"".concat(100*d/h,"%")};Object.keys(f).forEach((function(e){t.root.style[e]=f[e]}))}else["width","height"].forEach((function(e){t.config[e]&&("number"!=typeof t.config[e]?t.root.style[e]=t.config[e]:t.root.style[e]="".concat(t.config[e],"px"))}));var p=this.root.getBoundingClientRect(),g=p.width,v=p.height,y=p.left,m=p.top;return this.sizeInfo.width=g,this.sizeInfo.height=v,this.sizeInfo.left=y,this.sizeInfo.top=m,!0}},{key:"_initBaseDoms",value:function(){this.topBar=null,this.leftBar=null,this.rightBar=null,this.config.marginControls&&(this.innerContainer=z.createDom("xg-video-container","",{"data-index":-1},"xg-video-container"),this.root.appendChild(this.innerContainer))}},{key:"_bindEvents",value:function(){var e=this;["focus","blur"].forEach((function(t){e.on(t,e["on"+t.charAt(0).toUpperCase()+t.slice(1)])})),it.forEach((function(t){document&&document.addEventListener(t,e.onFullscreenChange)})),J.os.isIos&&(this.media.addEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.addEventListener("webkitendfullscreen",this._onWebkitendfullscreen)),this.once(pe,this.resize),this.playFunc=function(){e.config.closeFocusVideoFocus||e.media.focus()},this.once(ie,this.playFunc)}},{key:"_unbindEvents",value:function(){var e=this;this.root.removeEventListener("mousemove",this.mousemoveFunc),it.forEach((function(t){document.removeEventListener(t,e.onFullscreenChange)})),this.playFunc&&this.off(ie,this.playFunc),this.off(he,this.canPlayFunc),this.media.removeEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.removeEventListener("webkitendfullscreen",this._onWebkitendfullscreen)}},{key:"_clearUserTimer",value:function(){this.userTimer&&(z.clearTimeout(this,this.userTimer),this.userTimer=null)}},{key:"_startInit",value:function(e){var t=this;if(this.media&&(e&&""!==e&&("Array"!==z.typeOf(e)||0!==e.length)||(e="",this.emit(_e),W.logWarn("config.url is null, please get url and run player._startInit(url)"),!this.config.nullUrlStart))){this.handleSource&&(this._detachSourceEvents(this.media),"Array"===z.typeOf(e)&&e.length>0?this._attachSourceEvents(this.media,e):this.media.src&&this.media.src===e?e||this.media.removeAttribute("src"):this.media.src=e),"Number"===z.typeOf(this.config.volume)&&(this.volume=this.config.volume);var i=this.innerContainer?this.innerContainer:this.root;this.media instanceof window.Element&&!i.contains(this.media)&&i.insertBefore(this.media,i.firstChild);var n=this.media.readyState;W.logInfo("_startInit readyState",n),this.config.autoplay&&(!z.isMSE(this.media)&&this.load(),(J.os.isIpad||J.os.isPhone)&&this.mediaPlay());var o=this.config.startTime;this.__startTime=o>0?o:-1,this.config.startTime=0,n>=2&&this.duration>0?this.canPlayFunc():this.on(he,this.canPlayFunc),(!this.hasStart||this.state<Gt)&&Ot.afterInit(this),this.hasStart=!0,this.setState(Gt),z.setTimeout(this,(function(){t.emit(xe)}),0)}}},{key:"_registerPlugins",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this._loadingPlugins=[];var i=this.config.ignores||[],n=this.config.plugins||[],o=this.config.i18n||[];t&&Ut.extend(o,this.__i18n);var r=i.join("||").toLowerCase().split("||"),s=this.plugins;n.forEach((function(i){try{var n=i.plugin?i.plugin.pluginName:i.pluginName;if(n&&r.indexOf(n.toLowerCase())>-1)return null;if(!t&&s[n.toLowerCase()])return;if(i.lazy&&i.loader){var o=Ot.lazyRegister(e,i);return void(i.forceBeforeInit&&(o.then((function(){e._loadingPlugins.splice(e._loadingPlugins.indexOf(o),1)})).catch((function(t){W.logError("_registerPlugins:loadingPlugin",t),e._loadingPlugins.splice(e._loadingPlugins.indexOf(o),1)})),e._loadingPlugins.push(o)))}return e.registerPlugin(i)}catch(a){W.logError("_registerPlugins:",a)}}))}},{key:"_registerPresets",value:function(){var e=this;this.config.presets.forEach((function(t){!function(e,t){var i,n,o=t.preset&&t.options?new t.preset(t.options,e.config):new t({},e.config),r=o.plugins,s=void 0===r?[]:r,a=o.ignores,l=void 0===a?[]:a,c=o.icons,u=void 0===c?{}:c,h=o.i18n,d=void 0===h?[]:h;e.config.plugins||(e.config.plugins=[]),e.config.ignores||(e.config.ignores=[]),(i=e.config.plugins).push.apply(i,D(s)),(n=e.config.ignores).push.apply(n,D(l)),Object.keys(u).map((function(t){e.config.icons[t]||(e.config.icons[t]=u[t])}));var f=e.config.i18n||[];d.push.apply(d,D(f)),e.config.i18n=d}(e,t)}))}},{key:"_getRootByPosition",value:function(e){var t=null;switch(e){case Tt.ROOT_RIGHT:this.rightBar||(this.rightBar=z.createPositionBar("xg-right-bar",this.root)),t=this.rightBar;break;case Tt.ROOT_LEFT:this.leftBar||(this.leftBar=z.createPositionBar("xg-left-bar",this.root)),t=this.leftBar;break;case Tt.ROOT_TOP:this.topBar||(this.topBar=z.createPositionBar("xg-top-bar",this.root),this.config.topBarAutoHide&&z.addClass(this.topBar,Dt.TOP_BAR_AUTOHIDE)),t=this.topBar;break;default:t=this.innerContainer||this.root}return t}},{key:"registerPlugin",value:function(e,t){var i=Ot.formatPluginInfo(e,t),n=i.PLUFGIN,o=i.options,r=this.config.plugins;!Ot.checkPluginIfExits(n.pluginName,r)&&r.push(n);var s=Ot.getRootByConfig(n.pluginName,this.config);s.root&&(o.root=s.root),s.position&&(o.position=s.position);var a,l=o.position?o.position:o.config&&o.config.position||n.defaultConfig&&n.defaultConfig.position;return!o.root&&"string"==typeof l&&l.indexOf("controls")>-1?null===(a=this.controls)||void 0===a?void 0:a.registerPlugin(n,o,n.pluginName):(o.root||(o.root=this._getRootByPosition(l)),Ot.register(this,n,o))}},{key:"deregister",value:function(e){"string"==typeof e?Ot.unRegister(this,e):e instanceof bt&&Ot.unRegister(this,e.pluginName)}},{key:"unRegisterPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.deregister(e),t&&this.removePluginFromConfig(e)}},{key:"removePluginFromConfig",value:function(e){var t;if("string"==typeof e?t=e:e instanceof bt&&(t=e.pluginName),t)for(var i=this.config.plugins.length-1;i>-1;i--){if(this.config.plugins[i].pluginName.toLowerCase()===t.toLowerCase()){this.config.plugins.splice(i,1);break}}}},{key:"plugins",get:function(){return Ot.getPlugins(this)}},{key:"getPlugin",value:function(e){var t=Ot.findPlugin(this,e);return t&&t.pluginName?t:null}},{key:"addClass",value:function(e){this.root&&(z.hasClass(this.root,e)||z.addClass(this.root,e))}},{key:"removeClass",value:function(e){this.root&&z.removeClass(this.root,e)}},{key:"hasClass",value:function(e){if(this.root)return z.hasClass(this.root,e)}},{key:"setAttribute",value:function(e,t){this.root&&this.root.setAttribute(e,t)}},{key:"removeAttribute",value:function(e,t){this.root&&this.root.removeAttribute(e,t)}},{key:"start",value:function(e){var t=this;if(!(this.state>Wt))return e||this.config.url||this.getInitDefinition(),this.hasStart=!0,this.setState(Wt),this._registerPlugins(!1),Ot.beforeInit(this).then((function(){if(t.config){e||(e=t.url||t.config.url);var i=t._preProcessUrl(e);return t._startInit(i.url)}})).catch((function(e){throw e.fileName="player",e.lineNumber="236",W.logError("start:beforeInit:",e),e}))}},{key:"switchURL",value:function(e,t){var i=this,n=e;"Object"===z.typeOf(e)&&(n=e.url),n=this._preProcessUrl(n).url;var o=this.currentTime;this.__startTime=o;var r=this.paused&&!this.isError;return this.src=n,new Promise((function(e,t){var o=function(e){i.off("timeupdate",s),i.off("canplay",s),t(e)},s=function(){i._seekToStartTime(),r&&i.pause(),i.off("error",o),e(!0)};i.once("error",o),n?(J.os.isAndroid?i.once("timeupdate",s):i.once("canplay",s),i.play()):i.errorHandler("error",{code:6,message:"empty_src"})}))}},{key:"videoPlay",value:function(){this.mediaPlay()}},{key:"mediaPlay",value:function(){var e=this;if(!this.hasStart&&this.state<Gt)return this.removeClass(Dt.NO_START),this.addClass(Dt.ENTER),this.start(),void(this._useAutoplay=!0);this.state<Kt&&(this.removeClass(Dt.NO_START),!this.isCanplay&&this.addClass(Dt.ENTER));var i=O(E(t.prototype),"play",this).call(this);return void 0!==i&&i&&i.then?i.then((function(){e.removeClass(Dt.NOT_ALLOW_AUTOPLAY),e.addClass(Dt.PLAYING),e.state<Kt&&(W.logInfo(">>>>playPromise.then"),e.setState(Kt),e.emit(we))})).catch((function(t){if(W.logWarn(">>>>playPromise.catch",t.name),e.media&&e.media.error)return e.onError(),void e.removeClass(Dt.ENTER);"NotAllowedError"===t.name&&(e._errorTimer=z.setTimeout(e,(function(){e._errorTimer=null,e.emit(Te),e.addClass(Dt.NOT_ALLOW_AUTOPLAY),e.removeClass(Dt.ENTER),e.pause(),e.setState(zt)}),0))})):(W.logWarn("video.play not return promise"),this.state<Kt&&(this.setState(Kt),this.removeClass(Dt.NOT_ALLOW_AUTOPLAY),this.removeClass(Dt.NO_START),this.removeClass(Dt.ENTER),this.addClass(Dt.PLAYING),this.emit(we))),i}},{key:"mediaPause",value:function(){O(E(t.prototype),"pause",this).call(this)}},{key:"videoPause",value:function(){O(E(t.prototype),"pause",this).call(this)}},{key:"play",value:function(){var e=this;return this.removeClass(Dt.PAUSED),gt(this,"play",(function(){return e.mediaPlay()}))}},{key:"pause",value:function(){var e=this;gt(this,"pause",(function(){O(E(t.prototype),"pause",e).call(e)}))}},{key:"seek",value:function(e,t){var i=this;if(this.media&&!Number.isNaN(Number(e))&&this.hasStart){var n=this.config,o=n.isSeekedPlay,r=n.seekedStatus,s=t||(o?"play":r);e=e<0?0:e>this.duration?parseInt(this.duration,10):e,!this._isPauseBeforeSeek&&(this._isPauseBeforeSeek=this.paused?2:1),this._onceSeekCanplay&&this.off(le,this._onceSeekCanplay),this._onceSeekCanplay=function(){switch(i.removeClass(Dt.ENTER),i.isSeeking=!1,s){case"play":i.play();break;case"pause":i.pause();break;default:i._isPauseBeforeSeek>1||i.paused?i.pause():i.play()}i._isPauseBeforeSeek=0,i._onceSeekCanplay=null},this.once(le,this._onceSeekCanplay),this.state<Kt?(this.removeClass(Dt.NO_START),this.currentTime=e,this.play()):this.currentTime=e}}},{key:"getInitDefinition",value:function(){var e=this,t=this.config,i=t.definition;!t.url&&i&&i.list&&i.list.length>0&&i.defaultDefinition&&i.list.map((function(t){t.definition===i.defaultDefinition&&(e.config.url=t.url,e.curDefinition=t)}))}},{key:"changeDefinition",value:function(e,t){var i=this,n=this.config.definition;if(Array.isArray(null==n?void 0:n.list)&&n.list.forEach((function(t){(null==e?void 0:e.definition)===t.definition&&(i.curDefinition=t)})),null!=e&&e.bitrate&&"number"!=typeof e.bitrate&&(e.bitrate=parseInt(e.bitrate,10)||0),this.emit(Ne,{from:t,to:e}),this.hasStart){var o=this.switchURL(e.url,C({seamless:!1!==n.seamless&&"undefined"!=typeof MediaSource&&"function"==typeof MediaSource.isTypeSupported},e));o&&o.then?o.then((function(){i.emit(Fe,{from:t,to:e})})):this.emit(Fe,{from:t,to:e})}else this.config.url=e.url}},{key:"reload",value:function(){this.load(),this.reloadFunc=function(){this.play()},this.once(pe,this.reloadFunc)}},{key:"resetState",value:function(){var e=this,t=[Dt.NOT_ALLOW_AUTOPLAY,Dt.PLAYING,Dt.NO_START,Dt.PAUSED,Dt.REPLAY,Dt.ENTER,Dt.ENDED,Dt.ERROR,Dt.LOADING];this.hasStart=!1,this.isError=!1,this._useAutoplay=!1,this.mediaPause(),this._accPlayed.acc=0,this._accPlayed.t=0,this._accPlayed.loopAcc=0,t.forEach((function(t){e.removeClass(t)})),this.addClass(Dt.NO_START),this.emit(Ke)}},{key:"reset",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1?arguments[1]:void 0;if(this.resetState(),this.plugins&&(t.map((function(t){e.deregister(t)})),i)){var n=Rt();Object.keys(this.config).keys((function(t){"undefined"===e.config[t]||"plugins"!==t&&"presets"!==t&&"el"!==t&&"id"!==t||(e.config[t]=n[t])}))}}},{key:"destroy",value:function(){var e,i=this,n=this.innerContainer,o=this.root,r=this.media;if(o&&r){if(this.hasStart=!1,this._useAutoplay=!1,o.removeAttribute(rt),this.updateAcc("destroy"),this._unbindEvents(),this._detachSourceEvents(this.media),z.clearAllTimers(this),this.emit(Ee),null===(e=ti)||void 0===e||e.remove(this),Ot.destroy(this),pt(this),O(E(t.prototype),"destroy",this).call(this),this.fullscreen&&this._fullscreenEl===this.root&&this.exitFullscreen(),n)for(var s=n.children,a=0;a<s.length;a++)n.removeChild(s[a]);!n&&r instanceof window.Node&&o.contains(r)&&o.removeChild(r),["topBar","leftBar","rightBar","innerContainer"].map((function(e){i[e]&&o.removeChild(i[e]),i[e]=null}));var l=o.className.split(" ");l.length>0?o.className=l.filter((function(e){return e.indexOf("xgplayer")<0})).join(" "):o.className="",this.removeAttribute("data-xgfill"),["isSeeking","isCanplay","isActive","cssfullscreen","fullscreen"].forEach((function(e){i[e]=!1}))}}},{key:"replay",value:function(){var e=this;this.removeClass(Dt.ENDED),this.currentTime=0,this.isSeeking=!1,gt(this,"replay",(function(){e.once(le,(function(){var t=e.mediaPlay();t&&t.catch&&t.catch((function(e){}))})),e.emit(Se),e.onPlay()}))}},{key:"retry",value:function(){var e=this;this.removeClass(Dt.ERROR),this.addClass(Dt.LOADING),gt(this,"retry",(function(){var t=e.currentTime,i=e.config.url,n=z.isMSE(e.media)?{url:i}:e._preProcessUrl(i);e.src=n.url,!e.config.isLive&&(e.currentTime=t),e.once(he,(function(){e.mediaPlay()}))}))}},{key:"changeFullStyle",value:function(e,t,i,n){e&&(n||(n=Dt.PARENT_FULLSCREEN),this._orgCss||(this._orgCss=z.filterStyleFromText(e)),z.addClass(e,i),t&&t!==e&&!this._orgPCss&&(this._orgPCss=z.filterStyleFromText(t),z.addClass(t,n),t.setAttribute(rt,this.playerId)))}},{key:"recoverFullStyle",value:function(e,t,i,n){n||(n=Dt.PARENT_FULLSCREEN),this._orgCss&&(z.setStyleFromCsstext(e,this._orgCss),this._orgCss=""),z.removeClass(e,i),t&&t!==e&&this._orgPCss&&(z.setStyleFromCsstext(t,this._orgPCss),this._orgPCss="",z.removeClass(t,n),t.removeAttribute(rt))}},{key:"getFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget,t=this.root,i=this.media;if("video"!==e&&"media"!==e||(e=this[e]),e||(e=t),this._fullScreenOffset={top:z.scrollTop(),left:z.scrollLeft()},this._fullscreenEl=e,this._fullActionFrom="get",z.getFullScreenEl()===this._fullscreenEl)return this.onFullscreenChange(),Promise.resolve();try{for(var n=0;n<nt.length;n++){var o=nt[n];if(e[o]){var r="webkitRequestFullscreen"===o?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):e[o]();return r&&r.then?r:Promise.resolve()}}return i.fullscreenEnabled||i.webkitSupportsFullscreen?(i.webkitEnterFullscreen(),Promise.resolve()):Promise.reject(new Error("call getFullscreen fail"))}catch(s){return Promise.reject(new Error("call getFullscreen fail"))}}},{key:"exitFullscreen",value:function(e){if(this.isRotateFullscreen&&this.exitRotateFullscreen(),this._fullscreenEl||z.getFullScreenEl()){this.root;var t=this.media;this._fullActionFrom="exit";try{for(var i=0;i<ot.length;i++){var n=ot[i];if(document[n]){var o=document[n]();return o&&o.then?o:Promise.resolve()}}return t&&t.webkitSupportsFullscreen?(t.webkitExitFullScreen(),Promise.resolve()):Promise.reject(new Error("call exitFullscreen fail"))}catch(r){return Promise.reject(new Error("call exitFullscreen fail"))}}}},{key:"getCssFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.isRotateFullscreen?this.exitRotateFullscreen():this.fullscreen&&this.exitFullscreen();var t=e?"".concat(Dt.INNER_FULLSCREEN," ").concat(Dt.CSS_FULLSCREEN):Dt.CSS_FULLSCREEN;this.changeFullStyle(this.root,e,t);var i=this.config.fullscreen,n=void 0===i?{}:i;(!0===n.useCssFullscreen||"function"==typeof n.useCssFullscreen&&n.useCssFullscreen())&&(this.fullscreen=!0,this.emit(De,!0)),this._cssfullscreenEl=e,this.cssfullscreen=!0,this.emit(Re,!0)}},{key:"exitCssFullscreen",value:function(){var e=this._cssfullscreenEl?"".concat(Dt.INNER_FULLSCREEN," ").concat(Dt.CSS_FULLSCREEN):Dt.CSS_FULLSCREEN;if(this.fullscreen){var t=this.config.fullscreen,i=void 0===t?{}:t;!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen()?(this.recoverFullStyle(this.root,this._cssfullscreenEl,e),this.fullscreen=!1,this.emit(De,!1)):this.removeClass(e)}else this.recoverFullStyle(this.root,this._cssfullscreenEl,e);this._cssfullscreenEl=null,this.cssfullscreen=!1,this.emit(Re,!1)}},{key:"getRotateFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.cssfullscreen&&this.exitCssFullscreen(e);var t=e?"".concat(Dt.INNER_FULLSCREEN," ").concat(Dt.ROTATE_FULLSCREEN):Dt.ROTATE_FULLSCREEN;this._fullscreenEl=e||this.root,this.changeFullStyle(this.root,e,t,Dt.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!0,this.fullscreen=!0,this.setRotateDeg(90),this._rootStyle=this.root.getAttribute("style"),this.root.style.width="".concat(window.innerHeight,"px"),this.emit(De,!0)}},{key:"exitRotateFullscreen",value:function(e){var t=this._fullscreenEl!==this.root?"".concat(Dt.INNER_FULLSCREEN," ").concat(Dt.ROTATE_FULLSCREEN):Dt.ROTATE_FULLSCREEN;this.recoverFullStyle(this.root,this._fullscreenEl,t,Dt.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!1,this.fullscreen=!1,this.setRotateDeg(0),this.emit(De,!1),this._rootStyle&&(this.root.style.style=this._rootStyle,this._rootStyle=!1)}},{key:"setRotateDeg",value:function(e){90===window.orientation||-90===window.orientation?this.rotateDeg=0:this.rotateDeg=e}},{key:"focus",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!this.config.closeDelayBlur,delay:this.config.inactive};this.isActive?this.onFocus(e):this.emit(ke,C({paused:this.paused,ended:this.ended},e))}},{key:"blur",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{ignorePaused:!1};this.isActive?(this._clearUserTimer(),this.emit(Ce,C({paused:this.paused,ended:this.ended},e))):this.onBlur(e)}},{key:"onFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!0,delay:3e3},i=this.innerStates;if(this.isActive=!0,this.removeClass(Dt.INACTIVE),this._clearUserTimer(),void 0!==t.isLock&&(i.isActiveLocked=t.isLock),!1===t.autoHide||!0===t.isLock||i.isActiveLocked)this._clearUserTimer();else{var n=t&&t.delay?t.delay:this.config.inactive;this.userTimer=z.setTimeout(this,(function(){e.userTimer=null,e.blur()}),n)}}},{key:"onBlur",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).ignorePaused,t=void 0!==e&&e;if(!this.innerStates.isActiveLocked){var i=this.config.closePauseVideoFocus;this.isActive=!1,(t||i||!this.paused&&!this.ended)&&this.addClass(Dt.INACTIVE)}}},{key:"onEmptied",value:function(){this.updateAcc("emptied")}},{key:"onCanplay",value:function(){this.removeClass(Dt.ENTER),this.removeClass(Dt.ERROR),this.removeClass(Dt.LOADING),this.isCanplay=!0,this.waitTimer&&z.clearTimeout(this,this.waitTimer)}},{key:"onLoadeddata",value:function(){var e=this;this.isError=!1,this.isSeeking=!1,this.__startTime>0&&(this.duration>0?this._seekToStartTime():this.once(de,(function(){e._seekToStartTime()})))}},{key:"onLoadstart",value:function(){this.removeClass(Dt.ERROR),this.isCanplay=!1}},{key:"onPlay",value:function(){this.state===Yt&&this.setState(Kt),this.removeClass(Dt.PAUSED),this.ended&&this.removeClass(Dt.ENDED),!this.config.closePlayVideoFocus&&this.focus()}},{key:"onPause",value:function(){this.addClass(Dt.PAUSED),this.updateAcc("pause"),this.config.closePauseVideoFocus||(this._clearUserTimer(),this.focus())}},{key:"onEnded",value:function(){this.updateAcc("ended"),this.addClass(Dt.ENDED),this.setState(Yt)}},{key:"onError",value:function(){this.isError=!0,this.updateAcc("error"),this.removeClass(Dt.NOT_ALLOW_AUTOPLAY),this.removeClass(Dt.NO_START),this.removeClass(Dt.ENTER),this.removeClass(Dt.LOADING),this.addClass(Dt.ERROR)}},{key:"onSeeking",value:function(){this.isSeeking||this.updateAcc("seeking"),this.isSeeking=!0,this.addClass(Dt.SEEKING)}},{key:"onSeeked",value:function(){this.isSeeking=!1,this.waitTimer&&z.clearTimeout(this,this.waitTimer),this.removeClass(Dt.LOADING),this.removeClass(Dt.SEEKING)}},{key:"onWaiting",value:function(){var e=this;this.waitTimer&&z.clearTimeout(this,this.waitTimer),this.updateAcc("waiting"),this.waitTimer=z.setTimeout(this,(function(){e.addClass(Dt.LOADING),e.emit(Oe),z.clearTimeout(e,e.waitTimer),e.waitTimer=null}),this.config.minWaitDelay)}},{key:"onPlaying",value:function(){var e=this;this.isError=!1,[Dt.NO_START,Dt.PAUSED,Dt.ENDED,Dt.ERROR,Dt.REPLAY,Dt.LOADING].forEach((function(t){e.removeClass(t)})),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onTimeupdate",value:function(){!this._videoHeight&&this.media.videoHeight&&this.resize(),(this.waitTimer||this.hasClass(Dt.LOADING))&&this.media.readyState>2&&(this.removeClass(Dt.LOADING),z.clearTimeout(this,this.waitTimer),this.waitTimer=null),!this.paused&&this.state===zt&&this.duration&&(this.setState(Kt),this.emit(we)),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onVolumechange",value:function(){"Number"===z.typeOf(this.config.volume)&&(this.config.volume=this.volume)}},{key:"onRatechange",value:function(){this.config.defaultPlaybackRate=this.playbackRate}},{key:"emitUserAction",value:function(e,t,i){if(this.media&&t&&e){var n="String"===z.typeOf(e)?e:e.type||"";i.props&&"Array"!==z.typeOf(i.props)&&(i.props=[i.props]),this.emit(ze,C({eventType:n,action:t,currentTime:this.currentTime,duration:this.duration,ended:this.ended,event:e},i))}}},{key:"updateAcc",value:function(e){if(this._accPlayed.t){var t=(new Date).getTime()-this._accPlayed.t;this._accPlayed.acc+=t,this._accPlayed.t=0,("ended"===e||this.ended)&&(this._accPlayed.loopAcc=this._accPlayed.acc)}}},{key:"checkBuffer",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{startDiff:0,endDiff:0})||{},i=t.startDiff,n=void 0===i?0:i,o=t.endDiff,r=void 0===o?0:o,s=this.media.buffered;if(!s||0===s.length||!this.duration)return!0;for(var a=e||this.media.currentTime||.2,l=s.length,c=0;c<l;c++)if(s.start(c)+n<=a&&s.end(c)-r>a)return!0;return!1}},{key:"resizePosition",value:function(){var e=this.videoPos,t=e.vy,i=e.vx,n=e.h,o=e.w,r=this.videoPos.rotate;if(!(r<0&&n<0&&o<0)){var s=this.videoPos._pi;if(!s&&this.media.videoHeight&&(s=this.media.videoWidth/this.media.videoHeight*100),s){this.videoPos.pi=s;var a={rotate:r=r<0?0:r},l=0,c=0,u=1,h=Math.abs(r/90),d=this.root,f=this.innerContainer,p=d.offsetWidth,g=f?f.offsetHeight:d.offsetHeight,v=g,y=p;if(h%2==0)u=n>0?100/n:o>0?100/o:1,a.scale=u,l=t>0?(100-n)/2-t:0,a.y=2===h?0-l:l,c=i>0?(100-o)/2-i:0,a.x=2===h?0-c:c,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(v,"px");else if(h%2==1){v=p;var m=g-p;c=-m/2/(y=g)*100,a.x=3===h?c+t/2:c-t/2,l=m/2/v*100,a.y=3===h?l+i/2:l-i/2,a.scale=u,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(v,"px")}var k=z.getTransformStyle(a,this.media.style.transform||this.media.style.webkitTransform);this.media.style.transform=k,this.media.style.webkitTransform=k}}}},{key:"position",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{h:0,y:0,x:0,w:0};if(this.media&&e&&e.h){var t=this.videoPos;t.h=100*e.h||0,t.w=100*e.w||0,t.vx=100*e.x||0,t.vy=100*e.y||0,this.resizePosition()}}},{key:"setConfig",value:function(e){var t=this;e&&Object.keys(e).map((function(i){if("plugins"!==i){t.config[i]=e[i];var n=t.plugins[i.toLowerCase()];n&&"Function"===z.typeOf(n.setConfig)&&n.setConfig(e[i])}}))}},{key:"playNext",value:function(e){var t=this;this.resetState(),this.setConfig(e),this._currentTime=0,this._duration=0,gt(this,"playnext",(function(){t.start(),t.emit(Ve,e)}))}},{key:"resize",value:function(){var e=this;if(this.media){var t=this.root.getBoundingClientRect();this.sizeInfo.width=t.width,this.sizeInfo.height=t.height,this.sizeInfo.left=t.left,this.sizeInfo.top=t.top;var i=this.media,n=i.videoWidth,o=i.videoHeight,r=this.config,s=r.fitVideoSize,a=r.videoFillMode;if("fill"!==a&&"cover"!==a&&"contain"!==a||this.setAttribute("data-xgfill",a),o&&n){this._videoHeight=o,this._videoWidth=n;var l=this.controls&&this.innerContainer?this.controls.root.getBoundingClientRect().height:0,c=t.width,u=t.height-l,h=parseInt(n/o*1e3,10),d=parseInt(c/u*1e3,10),f=c,p=u,g={};"auto"===s&&d>h||"fixWidth"===s?(p=c/h*1e3,this.config.fluid?g.paddingTop="".concat(100*p/f,"%"):g.height="".concat(p+l,"px")):("auto"===s&&d<h||"fixHeight"===s)&&(f=h*u/1e3,g.width="".concat(f,"px")),this.fullscreen||this.cssfullscreen||Object.keys(g).forEach((function(t){e.root.style[t]=g[t]})),("fillHeight"===a&&d<h||"fillWidth"===a&&d>h)&&this.setAttribute("data-xgfill","cover");var v={videoScale:h,vWidth:f,vHeight:p,cWidth:f,cHeight:p+l};this.resizePosition(),this.emit(He,v)}}}},{key:"updateObjectPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.media.updateObjectPosition?this.media.updateObjectPosition(e,t):this.media.style.objectPosition="".concat(100*e,"% ").concat(100*t,"%")}},{key:"setState",value:function(e){W.logInfo("setState","state from:".concat(Xt[this.state]," to:").concat(Xt[e])),this._state=e}},{key:"_preProcessUrl",value:function(e,t){var i=this.config,n=i.preProcessUrl,o=i.preProcessUrlOptions,r=Object.assign({},o,t);return z.isBlob(e)||"function"!=typeof n?{url:e}:n(e,r)}},{key:"_seekToStartTime",value:function(){this.__startTime>0&&this.duration>0&&(this.currentTime=this.__startTime>this.duration?this.duration:this.__startTime,this.__startTime=-1)}},{key:"state",get:function(){return this._state}},{key:"isFullscreen",get:function(){return this.fullscreen}},{key:"isCssfullScreen",get:function(){return this.cssfullscreen}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){"boolean"==typeof e&&(this._hasStart=e,!1===e&&this.setState(Vt),this.emit("hasstart"))}},{key:"isPlaying",get:function(){return this._state===Kt||this._state===Yt},set:function(e){e?this.setState(Kt):this._state>=Kt&&this.setState(Gt)}},{key:"definitionList",get:function(){return this.config&&this.config.definition&&this.config.definition.list||[]},set:function(e){var t=this,i=this.config.definition,n=null,o=null;i.list=e,this.emit("resourceReady",e),e.forEach((function(e){var r;(null===(r=t.curDefinition)||void 0===r?void 0:r.definition)===e.definition&&(n=e),i.defaultDefinition===e.definition&&(o=e)})),!o&&e.length>0&&(o=e[0]),n?this.changeDefinition(n):o&&this.changeDefinition(o)}},{key:"videoFrameInfo",get:function(){var e={total:0,dropped:0,corrupted:0,droppedRate:0,droppedDuration:0};if(!this.media||!this.media.getVideoPlaybackQuality)return e;var t=this.media.getVideoPlaybackQuality();return e.dropped=t.droppedVideoFrames||0,e.total=t.totalVideoFrames||0,e.corrupted=t.corruptedVideoFrames||0,e.total>0&&(e.droppedRate=e.dropped/e.total*100,e.droppedDuration=parseInt(this.cumulateTime/e.total*e.dropped,0)),e}},{key:"lang",get:function(){return this.config.lang},set:function(e){0===Ut.langKeys.filter((function(t){return t===e})).length&&"zh"!==e||(this.config.lang=e,Ot.setLang(e,this))}},{key:"i18n",get:function(){var e=this.config.lang;return"zh"===e&&(e="zh-cn"),this.__i18n.lang[e]||this.__i18n.lang.en}},{key:"i18nKeys",get:function(){return this.__i18n.textKeys||{}}},{key:"version",get:function(){return $}},{key:"playerId",get:function(){return this._pluginInfoId}},{key:"url",get:function(){return this.__url||this.config.url},set:function(e){this.__url=e}},{key:"poster",get:function(){return this.plugins.poster?this.plugins.poster.config.poster:this.config.poster},set:function(e){this.plugins.poster&&this.plugins.poster.update(e)}},{key:"readyState",get:function(){return O(E(t.prototype),"readyState",this)}},{key:"error",get:function(){var e=O(E(t.prototype),"error",this);return this.i18n[e]||e}},{key:"networkState",get:function(){return O(E(t.prototype),"networkState",this)}},{key:"fullscreenChanging",get:function(){return!(null===this._fullScreenOffset)}},{key:"cumulateTime",get:function(){var e=this._accPlayed,t=e.acc,i=e.t;return i?(new Date).getTime()-i+t:t}},{key:"zoom",get:function(){return this.config.zoom},set:function(e){this.config.zoom=e}},{key:"videoRotateDeg",get:function(){return this.videoPos.rotate},set:function(e){(e=z.convertDeg(e))%90==0&&e!==this.videoPos.rotate&&(this.videoPos.rotate=e,this.resizePosition())}},{key:"avgSpeed",get:function(){return ei},set:function(e){ei=e}},{key:"realTimeSpeed",get:function(){return Qt},set:function(e){Qt=e}},{key:"offsetCurrentTime",get:function(){return this._offsetInfo.currentTime||0},set:function(e){this._offsetInfo.currentTime=e}},{key:"offsetDuration",get:function(){return this._offsetInfo.duration||0},set:function(e){this._offsetInfo.duration=e||0}},{key:"hook",value:function(e,t){return at.call.apply(at,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,t){return ct.call.apply(ct,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,t){return ut.call.apply(ut,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"usePluginHooks",value:function(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];return ht.call.apply(ht,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removePluginHooks",value:function(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];return dt.call.apply(dt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"setUserActive",value:function(e,t){var i;"boolean"==typeof t&&t!==this.muted&&(this.addInnerOP("volumechange"),z.typeOf(t)===Boolean&&(this.muted=t)),null===(i=ti)||void 0===i||i.setActive(this.playerId,e)}}],[{key:"debugger",get:function(){return W.config.debug},set:function(e){W.config.debug=e}},{key:"instManager",get:function(){return ti},set:function(e){ti=e}},{key:"getCurrentUserActivePlayerId",value:function(){var e;return null===(e=ti)||void 0===e?void 0:e.getActiveId()}},{key:"setCurrentUserActive",value:function(e,t){var i;null===(i=ti)||void 0===i||i.setActive(e,t)}},{key:"isHevcSupported",value:function(){return J.isHevcSupported()}},{key:"probeConfigSupported",value:function(e){return J.probeConfigSupported(e)}},{key:"install",value:function(e,i){t.plugins||(t.plugins={}),t.plugins[e]||(t.plugins[e]=i)}},{key:"use",value:function(e,i){t.plugins||(t.plugins={}),t.plugins[e]=i}}]),t}();function ni(){return(new Date).getTime()}x(ii,"defaultPreset",null),x(ii,"XgVideoProxy",null),ii.instManager=Jt.getInstance();var oi="loadstart",ri="loadeddata",si="firstFrame",ai="waitingStart",li="waitingEnd",ci="seekStart",ui="seekEnd",hi=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"_onTimeupdate",(function(){i._state.isTimeUpdate=!0,i._state.autoplayStart&&(W.logInfo("[xgLogger]".concat(i.player.playerId," _onTimeupdate")),i._sendFF("onTimeupdate"))})),x(I(i),"_onAutoplayStart",(function(){W.logInfo("[xgLogger]".concat(i.player.playerId," _onAutoplayStart")),i._state.autoplayStart=!0,i.vt&&i._sendFF("onAutoplayStart")})),x(I(i),"_onReset",(function(){i._state={autoplayStart:!1,isFFLoading:!1,isTimeUpdate:!1,isFFSend:!1,isLs:!1},i.vt=0,i.pt=0,i.fvt=0,i.newPointTime=ni(),i.loadedCostTime=0,i.startCostTime=0,i._isSeeking=!1,i.seekingStart=0,i.waitingStart=0,i.fixedWaitingStart=0,i._isWaiting=!1,i._waitTimer&&z.clearTimeout(I(i),i._waitTimer),i._waittTimer&&z.clearTimeout(I(i),i._waittTimer),i._waitTimer=null,i._waittTimer=null,i._waitType=0})),x(I(i),"_onSeeking",(function(){i.seekingStart||(i.suspendWaitingStatus("seek"),i.seekingStart=ni(),i.emitLog(ci,{start:ni()}))})),x(I(i),"_onSeeked",(function(){i.suspendSeekingStatus("seeked")})),x(I(i),"_onWaitingLoadStart",(function(){i._isWaiting||i.vt||(i._isWaiting=!0,i.waitingStart=ni(),i.fixedWaitingStart=ni(),i._waitType=1,i.emitLog(ai,{fixedStart:i.fixedWaitingStart,start:i.waitingStart,type:1,endType:"loadstart"}))})),x(I(i),"_onWaiting",(function(){!i._isWaiting&&i.vt&&(i._isWaiting=!0,i.vt?i.seekingStart?i._waitType=2:i._waitType=0:i._waitType=1,i.fixedWaitingStart=ni(),i._waitTimer=z.setTimeout(I(i),(function(){i._isWaiting&&(i.waitingStart=ni(),z.clearTimeout(I(i),i._waitTimer),i._waitTimer=null,i._startWaitTimeout(),i.emitLog(ai,{fixedStart:i.fixedWaitingStart,start:i.waitingStart,type:i._waitType,endType:2===i._waitType?"seek":"playing"}))}),200))})),x(I(i),"_onError",(function(){i.suspendSeekingStatus("error"),i.suspendWaitingStatus("error")})),x(I(i),"_onPlaying",(function(){i._isWaiting&&i.suspendWaitingStatus("playing")})),i}return T(t,[{key:"afterCreate",value:function(){var e=this;this._onReset(),this._waitType="firstFrame",this._initOnceEvents(),this.newPointTime=ni(),this.loadedCostTime=0,this.startCostTime=0,this.on(ye,(function(){var t=e._state,i=t.autoplayStart,n=t.isFFSend;e.startCostTime=ni()-e.newPointTime,W.logInfo("[xgLogger]".concat(e.player.playerId," LOAD_START"),"autoplayStart:".concat(i," isFFSend:").concat(n," startCostTime:").concat(e.startCostTime," newPointTime").concat(e.newPointTime)),n||(!t.isLs&&e.emitLog(oi,{}),t.isLs=!0,t.isTimeUpdate=!1,t.isFFLoading=!0,e.pt=ni(),e.vt=0,e.fvt=0,e._initOnceEvents(),e._onWaitingLoadStart())})),this.on(pe,(function(){e.vt=ni(),e.fvt=e.vt-e.pt,e.loadedCostTime=e.vt-e.newPointTime;var t=e._state,i=t.isTimeUpdate,n=t.isFFSend,o=t.autoplayStart;W.logInfo("[xgLogger]".concat(e.player.playerId," LOADED_DATA"),"fvt:".concat(e.fvt," isTimeUpdate:").concat(e._state.isTimeUpdate," loadedCostTime:").concat(e.loadedCostTime)),(i||o)&&e._sendFF("loadedData"),n||e.emitLog(ri,{}),e.suspendWaitingStatus("loadeddata")})),this.on(ae,this._onSeeking),this.on(le,this._onSeeked),this.on(Ee,(function(){e.endState("destroy")})),this.on(Pe,(function(){e.endState("urlChange"),W.logInfo("[xgLogger]".concat(e.player.playerId," URL_CHANGE")),e._state.isFFSend&&e._onReset()})),this.on([ne,he],this._onPlaying),this.on(ue,this._onWaiting),this.on(se,this._onError),this.on(Ke,(function(){W.logInfo("[xgLogger]".concat(e.player.playerId," RESET")),e.endState("reset"),e._initOnceEvents(),e._onReset()}))}},{key:"_initOnceEvents",value:function(){this.off(we,this._onAutoplayStart),this.off(ce,this._onTimeupdate),this.once(we,this._onAutoplayStart),this.once(ce,this._onTimeupdate)}},{key:"_sendFF",value:function(e){this.s=ni();var t=this._state,i=t.isFFLoading,n=t.isFFSend;W.logInfo("[xgLogger]".concat(this.player.playerId," _sendFF"),"".concat(e," fvt:").concat(this.fvt," isFFLoading:").concat(i," !isFFSend:").concat(!n)),this.vt>0&&i&&!n&&(W.logInfo("[xgLogger]".concat(this.player.playerId," emitLog_firstFrame"),e),this._state.isFFLoading=!1,this._state.isFFSend=!0,this.emitLog(si,{fvt:this.fvt,costTime:this.fvt,vt:this.vt,startCostTime:this.startCostTime,loadedCostTime:this.loadedCostTime}))}},{key:"_startWaitTimeout",value:function(){var e=this;this._waittTimer&&z.clearTimeout(this,this._waittTimer),this._waittTimer=z.setTimeout(this,(function(){e.suspendWaitingStatus("timeout"),z.clearTimeout(e,e._waittTimer),e._waittTimer=null}),this.config.waitTimeout)}},{key:"endState",value:function(e){this.suspendWaitingStatus(e),this.suspendSeekingStatus(e)}},{key:"suspendSeekingStatus",value:function(e){if(this.seekingStart){var t=ni(),i=t-this.seekingStart;this.seekingStart=0,this.emitLog(ui,{end:t,costTime:i,endType:e})}}},{key:"suspendWaitingStatus",value:function(e){if(this._waitTimer&&(z.clearTimeout(this,this._waitTimer),this._waitTimer=null),this._waittTimer&&(z.clearTimeout(this,this._waittTimer),this._waittTimer=null),this._isWaiting=!1,this.waitingStart){var t=ni(),i=t-this.waitingStart,n=t-this.fixedWaitingStart,o=this.config.waitTimeout;this._isWaiting=!1,this.waitingStart=0,this.fixedWaitingStart=0,this.emitLog(li,{fixedCostTime:n>o?o:n,costTime:i>o?o:i,type:"loadeddata"===e?1:this._waitType,endType:2===this._waitType?"seek":e})}}},{key:"emitLog",value:function(e,t){var i=this.player;this.emit(Ge,C({t:ni(),host:z.getHostFromUrl(i.currentSrc),vtype:i.vtype,eventType:e,currentTime:this.player.currentTime,readyState:i.video.readyState,networkState:i.video.networkState},t))}}],[{key:"pluginName",get:function(){return"xgLogger"}},{key:"defaultConfig",get:function(){return{waitTimeout:1e4}}}]),t}();function di(){return(new DOMParser).parseFromString('<svg class="xgplayer-replay-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 78 78" width="78" height="78">\n  <path fill="#fff" transform="translate(20, 20)" d="M8.22708362,13.8757234 L11.2677371,12.6472196 C11.7798067,12.4403301 12.3626381,12.6877273 12.5695276,13.1997969 L12.9441342,14.1269807 C13.1510237,14.6390502 12.9036264,15.2218816 12.3915569,15.4287712 L6.8284538,17.6764107 L5.90126995,18.0510173 C5.38920044,18.2579068 4.80636901,18.0105096 4.5994795,17.49844 L1.97723335,11.0081531 C1.77034384,10.4960836 2.0177411,9.91325213 2.52981061,9.70636262 L3.45699446,9.33175602 C3.96906396,9.12486652 4.5518954,9.37226378 4.75878491,9.88433329 L5.67885163,12.1615783 C7.99551726,6.6766934 13.3983951,3 19.5,3 C27.7842712,3 34.5,9.71572875 34.5,18 C34.5,26.2842712 27.7842712,33 19.5,33 C15.4573596,33 11.6658607,31.3912946 8.87004692,28.5831991 C8.28554571,27.9961303 8.28762719,27.0463851 8.87469603,26.4618839 C9.46176488,25.8773827 10.4115101,25.8794641 10.9960113,26.466533 C13.2344327,28.7147875 16.263503,30 19.5,30 C26.127417,30 31.5,24.627417 31.5,18 C31.5,11.372583 26.127417,6 19.5,6 C14.4183772,6 9.94214483,9.18783811 8.22708362,13.8757234 Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var fi=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"registerIcons",value:function(){return{replay:di}}},{key:"afterCreate",value:function(){var e=this;It.insert(this.icons.replay,this.root,0),this.__handleReplay=this.hook("replayClick",(function(){e.player.replay()}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(".xgplayer-replay",["click","touchend"],this.__handleReplay),this.on(oe,(function(){if(e.playerConfig.loop||z.addClass(e.player.root,"replay"),!e.config.disable){e.show();var t=e.root.querySelector("path");if(t){var i=window.getComputedStyle(t).getPropertyValue("transform");if("string"==typeof i&&i.indexOf("none")>-1)return null;t.setAttribute("transform",i)}}})),this.on(ie,(function(){e.hide()}))}},{key:"handleReplay",value:function(e){e.preventDefault(),e.stopPropagation(),this.player.replay(),z.removeClass(this.player.root,"replay")}},{key:"show",value:function(e){this.config.disable||(this.root.style.display="flex")}},{key:"enable",value:function(){this.config.disable=!1}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){this.unbind(".xgplayer-replay",["click","touchend"],this.__handleReplay)}},{key:"render",value:function(){return'<xg-replay class="xgplayer-replay">\n      <xg-replay-txt class="xgplayer-replay-txt" lang-key="'.concat(this.i18nKeys.REPLAY,'">').concat(this.i18n.REPLAY,"</xg-replay-txt>\n    </xg-replay>")}}],[{key:"pluginName",get:function(){return"replay"}},{key:"defaultConfig",get:function(){return{disable:!1}}}]),t}(),pi=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"isEndedShow",get:function(){return this.config.isEndedShow},set:function(e){this.config.isEndedShow=e}},{key:"hide",value:function(){z.addClass(this.root,"hide")}},{key:"show",value:function(e){z.removeClass(this.root,"hide")}},{key:"beforeCreate",value:function(e){"string"==typeof e.player.config.poster&&(e.config.poster=e.player.config.poster)}},{key:"afterCreate",value:function(){var e=this;this.on(oe,(function(){e.isEndedShow&&z.removeClass(e.root,"hide")})),this.config.hideCanplay?(this.once(ce,(function(){e.onTimeUpdate()})),this.on(Pe,(function(){z.removeClass(e.root,"hide"),z.addClass(e.root,"xg-showplay"),e.once(ce,(function(){e.onTimeUpdate()}))}))):this.on(ie,(function(){z.addClass(e.root,"hide")}))}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.config[i]=e[i]}));var i=this.config.poster;this.update(i)}},{key:"onTimeUpdate",value:function(){var e=this;this.player.currentTime?z.removeClass(this.root,"xg-showplay"):this.once(ce,(function(){e.onTimeUpdate()}))}},{key:"update",value:function(e){e&&(this.config.poster=e,this.root.style.backgroundImage="url(".concat(e,")"))}},{key:"getBgSize",value:function(e){var t="";switch(e){case"cover":t="cover";break;case"contain":t="contain";break;case"fixHeight":t="auto 100%";break;default:t=""}return t?"background-size: ".concat(t,";"):""}},{key:"render",value:function(){var e=this.config,t=e.poster,i=e.hideCanplay,n=e.fillMode,o=e.notHidden,r=this.getBgSize(n),s=t?"background-image:url(".concat(t,");").concat(r):r;return'<xg-poster class="xgplayer-poster '.concat(o?"xg-not-hidden":i?"xg-showplay":"",'" style="').concat(s,'">\n    </xg-poster>')}}],[{key:"pluginName",get:function(){return"poster"}},{key:"defaultConfig",get:function(){return{isEndedShow:!0,hideCanplay:!1,notHidden:!1,poster:"",fillMode:"fixWidth"}}}]),t}();function gi(){return(new DOMParser).parseFromString('<svg class="play" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n',"image/svg+xml").firstChild}function vi(){return(new DOMParser).parseFromString('<svg class="pause" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n',"image/svg+xml").firstChild}var yi={};function mi(e){e?window.clearTimeout(e):Object.keys(yi).map((function(e){window.clearTimeout(yi[e].id),delete yi[e]}))}var ki=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"onPlayerReset",(function(){n.autoPlayStart=!1;var e="auto"===n.config.mode?"auto-hide":"hide";n.setAttr("data-state","play"),z.removeClass(n.root,e),n.show()})),x(I(n),"onAutoplayStart",(function(){if(!n.autoPlayStart){var e="auto"===n.config.mode?"auto-hide":"hide";z.addClass(n.root,e),n.autoPlayStart=!0,n.toggleTo("play")}})),n.autoPlayStart=!1,n}return T(t,[{key:"afterCreate",value:function(){var e=this.playerConfig;this.initIcons(),this.listenEvents(),this.bindClickEvents(),e.autoplay||this.show()}},{key:"listenEvents",value:function(){var e=this,t=this.player,i=this.playerConfig;this.once(be,(function(){i&&(i.lang&&"en"===i.lang?z.addClass(t.root,"lang-is-en"):"jp"===i.lang&&z.addClass(t.root,"lang-is-jp"))})),this.on(we,this.onAutoplayStart),this.on(Te,(function(){var t="auto"===e.config.mode?"auto-hide":"hide";e.setAttr("data-state","play"),z.removeClass(e.root,t),e.show()})),this.on(ie,(function(){e.toggleTo("play")})),this.on(re,(function(){e.toggleTo("pause")})),this.on(Ke,(function(){e.onPlayerReset()}))}},{key:"bindClickEvents",value:function(){var e=this;this.clickHandler=this.hook("startClick",this.switchPausePlay,{pre:function(t){t.cancelable&&t.preventDefault(),t.stopPropagation();var i=e.player.paused;e.emitUserAction(t,"switch_play_pause",{props:"paused",from:i,to:!i})}}),this.bind(["click","touchend"],this.clickHandler)}},{key:"registerIcons",value:function(){return{startPlay:{icon:gi,class:"xg-icon-play"},startPause:{icon:vi,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild("xg-start-inner",e.startPlay),this.appendChild("xg-start-inner",e.startPause)}},{key:"hide",value:function(){z.addClass(this.root,"hide")}},{key:"show",value:function(e){z.removeClass(this.root,"hide")}},{key:"focusHide",value:function(){z.addClass(this.root,"focus-hide")}},{key:"recover",value:function(){z.removeClass(this.root,"focus-hide")}},{key:"switchStatus",value:function(e){e?this.setAttr("data-state",this.player.paused?"pause":"play"):this.setAttr("data-state",this.player.paused?"play":"pause")}},{key:"animate",value:function(e){var t=this;this._animateId=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{start:null,end:null};return yi[e]&&window.clearTimeout(yi[e].id),yi[e]={},i.start&&i.start(),yi[e].id=window.setTimeout((function(){i.end&&i.end(),window.clearTimeout(yi[e].id),delete yi[e]}),t),yi[e].id}("pauseplay",400,{start:function(){z.addClass(t.root,"interact"),t.show(),t.switchStatus(!0)},end:function(){z.removeClass(t.root,"interact"),!e&&t.hide(),t._animateId=null}})}},{key:"endAnimate",value:function(){z.removeClass(this.root,"interact"),mi(this._animateId),this._animateId=null}},{key:"switchPausePlay",value:function(e){var t=this.player;(e.cancelable&&e.preventDefault(),e.stopPropagation(),t.state<Vt)||(this.player.paused||t.state!==Kt?t.play():t.pause())}},{key:"onPlayPause",value:function(e){this.toggleTo(e)}},{key:"toggleTo",value:function(e){var t=this.config,i=this.player;if(i&&!(i.state<Kt)&&this.autoPlayStart){if("show"===t.mode)return this.switchStatus(),void this.show();if("auto"!==t.mode){if(t.isShowPause&&i.paused&&!i.ended||t.isShowEnd&&i.ended)return this.switchStatus(),this.show(),void this.endAnimate();if(t.disableAnimate)return this.switchStatus(),void this.hide();if("play"===e)this.autoPlayStart?this.animate():this.hide();else{if(!this.autoPlayStart||i.ended)return;this.animate()}}else this.switchStatus()}}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.clickHandler),mi(this._animateId)}},{key:"render",value:function(){var e=this.playerConfig.autoplay?"auto"===this.config.mode?"auto-hide":"hide":"";return'\n    <xg-start class="xgplayer-start '.concat(e,'">\n    <xg-start-inner></xg-start-inner>\n    </xg-start>')}}],[{key:"pluginName",get:function(){return"start"}},{key:"defaultConfig",get:function(){return{isShowPause:!1,isShowEnd:!1,disableAnimate:!1,mode:"hide"}}}]),t}(),Ci=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"render",value:function(){var e=this.config.innerHtml,t=z.createDom("xg-enter","",{},"xgplayer-enter");if(e&&e instanceof window.HTMLElement)t.appendChild(e);else if(e&&"string"==typeof e)t.innerHTML=e;else{for(var i="",n=1;n<=12;n++)i+='<div class="xgplayer-enter-bar'.concat(n,'"></div>');t.innerHTML='<div class="xgplayer-enter-spinner">'.concat(i,"</div>")}return t}}],[{key:"pluginName",get:function(){return"enter"}},{key:"defaultConfig",get:function(){return{innerHtml:"",logo:""}}}]),t}();function bi(e,t,i){try{return' <div class="xg-tips '.concat(i?"hide":" ",'" lang-key="').concat(e.i18nKeys[t],'">\n    ').concat(e.i18n[t],"\n    </div>")}catch(n){return'<div class="xg-tips hide"></div>'}}var _i=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"afterCreate",value:function(){this.getMini=this.getMini.bind(this),this.exitMini=this.exitMini.bind(this),this.bind("click",this.getMini)}},{key:"getMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"exitMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.getMini)}},{key:"render",value:function(){var e="MINISCREEN";return'\n      <xg-icon class="xgplayer-miniicon">\n      <div class="xgplayer-icon btn-text"><span class="icon-text" lang-key="'.concat(this.i18nKeys[e],'">').concat(this.i18n[e],"</span></div>\n      </xg-icon>")}}],[{key:"pluginName",get:function(){return"miniscreenIcon"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:10}}}]),t}();function wi(e){var t=parseFloat(e);return-1===e.indexOf("%")&&!Number.isNaN(t)&&t}var Ti=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],xi=Ti.length;function Si(e){if("string"==typeof e&&(e=document.querySelector(e)),e&&"object"===b(e)&&e.nodeType){var t=function(e){return window.getComputedStyle(e)}(e);if("none"===t.display)return function(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<xi;t++)e[Ti[t]]=0;return e}();var i={};i.width=e.offsetWidth,i.height=e.offsetHeight;for(var n=i.isBorderBox="border-box"===t.boxSizing,o=0;o<xi;o++){var r=Ti[o],s=t[r],a=parseFloat(s);i[r]=Number.isNaN(a)?0:a}var l=i.paddingLeft+i.paddingRight,c=i.paddingTop+i.paddingBottom,u=i.marginLeft+i.marginRight,h=i.marginTop+i.marginBottom,d=i.borderLeftWidth+i.borderRightWidth,f=i.borderTopWidth+i.borderBottomWidth,p=n,g=wi(t.width);!1!==g&&(i.width=g+(p?0:l+d));var v=wi(t.height);return!1!==v&&(i.height=v+(p?0:c+f)),i.innerWidth=i.width-(l+d),i.innerHeight=i.height-(c+f),i.outerWidth=i.width+u,i.outerHeight=i.height+h,i}}function Ei(e,t){for(var i=0;i<e.length;i++){var n=e[i];if(n.identifier===t)return n}}var Pi="dragStart",Ii="dragMove",Li="dragEnded",Ai={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]},Oi=function(){S(t,B);var e=A(t);function t(i){var n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return _(this,t),(n=e.call(this)).isEnabled=!0,n.isDragging=!1,n.isDown=!1,n.position={},n.downPoint={},n.dragPoint={x:0,y:0},n.startPos={x:0,y:0},n._root=i instanceof Element?i:document.querySelector(i),n._handlerDom=o.handle instanceof Element?o.handle:document.querySelector(o.handle),n._root&&n._handlerDom?(n._bindStartEvent(),n):L(n)}return T(t,[{key:"_bindStartEvent",value:function(){var e=this;"ontouchstart"in window?this._startKey="touchstart":this._startKey="mousedown",this["on".concat(this._startKey)]=this["on".concat(this._startKey)].bind(this),this._handlerDom.addEventListener(this._startKey,this["on".concat(this._startKey)]),Ai[this._startKey].map((function(t){e["on".concat(t)]=e["on".concat(t)].bind(e)}))}},{key:"_unbindStartEvent",value:function(){this._handlerDom.removeEventListener(this._startKey,this["on".concat(this._startKey)])}},{key:"_bindPostStartEvents",value:function(e){var t=this;if(e){var i=Ai[this._startKey];i.map((function(e){window.addEventListener(e,t["on".concat(e)])})),this._boundPointerEvents=i}}},{key:"_unbindPostStartEvents",value:function(){var e=this;this._boundPointerEvents&&(this._boundPointerEvents.map((function(t){window.removeEventListener(t,e["on".concat(t)])})),delete this._boundPointerEvents)}},{key:"enable",value:function(){this.isEnabled=!0}},{key:"disable",value:function(){this.isEnabled=!1,this.isDragging&&this.onUp()}},{key:"onDocUp",value:function(e){this.onUp()}},{key:"animate",value:function(){var e=this;this.isDragging&&(this.positionDrag(),window.requestAnimationFrame((function(){e.animate()})))}},{key:"positionDrag",value:function(){var e="translate3d(".concat(this.dragPoint.x,"px, ").concat(this.dragPoint.y,"px, 0)");this._root.style.transform=e,this._root.style.webKitTransform=e}},{key:"setLeftTop",value:function(){this._root.style.left=this.position.x+"px",this._root.style.top=this.position.y+"px"}},{key:"onmousedown",value:function(e){this.dragStart(e,e)}},{key:"onmousemove",value:function(e){this.dragMove(e,e)}},{key:"onmouseup",value:function(e){this.dragEnd(e,e)}},{key:"ontouchstart",value:function(e){var t=e.changedTouches[0];this.dragStart(e,t),this.touchIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,e.preventDefault()}},{key:"ontouchmove",value:function(e){var t=Ei(e.changedTouches,this.touchIdentifier);t&&this.dragMove(e,t)}},{key:"ontouchend",value:function(e){var t=Ei(e.changedTouches,this.touchIdentifier);t&&this.dragEnd(e,t),e.preventDefault()}},{key:"ontouchcancel",value:function(e){var t=Ei(e.changedTouches,this.touchIdentifier);t&&this.dragCancel(e,t)}},{key:"dragStart",value:function(e,t){if(this._root&&!this.isDown&&this.isEnabled){this.downPoint=t,this.dragPoint.x=0,this.dragPoint.y=0,this._getPosition();var i=Si(this._root);this.startPos.x=this.position.x,this.startPos.y=this.position.y,this.startPos.maxY=window.innerHeight-i.height,this.startPos.maxX=window.innerWidth-i.width,this.setLeftTop(),this.isDown=!0,this._bindPostStartEvents(e)}}},{key:"dragRealStart",value:function(e,t){this.isDragging=!0,this.animate(),this.emit(Pi,this.startPos)}},{key:"dragEnd",value:function(e,t){this._root&&(this._unbindPostStartEvents(),this.isDragging&&(this._root.style.transform="",this.setLeftTop(),this.emit(Li)),this.presetInfo())}},{key:"_dragPointerMove",value:function(e,t){var i={x:t.pageX-this.downPoint.pageX,y:t.pageY-this.downPoint.pageY};return!this.isDragging&&this.hasDragStarted(i)&&this.dragRealStart(e,t),i}},{key:"dragMove",value:function(e,t){if(e=e||window.event,this.isDown){var i=this.startPos,n=i.x,o=i.y,r=this._dragPointerMove(e,t),s=r.x,a=r.y;s=this.checkContain("x",s,n),a=this.checkContain("y",a,o),this.position.x=n+s,this.position.y=o+a,this.dragPoint.x=s,this.dragPoint.y=a,this.emit(Ii,this.position)}}},{key:"dragCancel",value:function(e,t){this.dragEnd(e,t)}},{key:"presetInfo",value:function(){this.isDragging=!1,this.startPos={x:0,y:0},this.dragPoint={x:0,y:0},this.isDown=!1}},{key:"destroy",value:function(){this._unbindStartEvent(),this._unbindPostStartEvents(),this.isDragging&&this.dragEnd(),this.removeAllListeners(),this._handlerDom=null}},{key:"hasDragStarted",value:function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3}},{key:"checkContain",value:function(e,t,i){return t+i<0?0-i:"x"===e&&t+i>this.startPos.maxX?this.startPos.maxX-i:"y"===e&&t+i>this.startPos.maxY?this.startPos.maxY-i:t}},{key:"_getPosition",value:function(){var e=window.getComputedStyle(this._root),t=this._getPositionCoord(e.left,"width"),i=this._getPositionCoord(e.top,"height");this.position.x=Number.isNaN(t)?0:t,this.position.y=Number.isNaN(i)?0:i,this._addTransformPosition(e)}},{key:"_addTransformPosition",value:function(e){var t=e.transform;if(0===t.indexOf("matrix")){var i=t.split(","),n=0===t.indexOf("matrix3d")?12:4,o=parseInt(i[n],10),r=parseInt(i[n+1],10);this.position.x+=o,this.position.y+=r}}},{key:"_getPositionCoord",value:function(e,t){if(-1!==e.indexOf("%")){var i=Si(this._root.parentNode);return i?parseFloat(e)/100*i[t]:0}return parseInt(e,10)}}]),t}(),Di=function(){S(t,It);var e=A(t);function t(i){var n;_(this,t),x(I(n=e.call(this,i)),"onCancelClick",(function(e){n.exitMini(),n.isClose=!0})),x(I(n),"onCenterClick",(function(e){var t=I(n).player;t.paused?t.play():t.pause()})),x(I(n),"onScroll",(function(e){if(!(!window.scrollY&&0!==window.scrollY||Math.abs(window.scrollY-n.pos.scrollY)<50)){var t=parseInt(z.getCss(n.player.root,"height"));t+=n.config.scrollTop,n.pos.scrollY=window.scrollY,window.scrollY>t+5?!n.isMini&&!n.isClose&&n.getMini():window.scrollY<=t&&(n.isMini&&n.exitMini(),n.isClose=!1)}})),n.isMini=!1,n.isClose=!1;var o=I(n).config;return n.pos={left:o.left<0?window.innerWidth-o.width-20:o.left,top:o.top<0?window.innerHeight-o.height-20:o.top,height:n.config.height,width:n.config.width,scrollY:window.scrollY||0},n.lastStyle=null,n}return T(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.mini&&(e.config.isShowIcon=e.player.config.mini)}},{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.on(re,(function(){e.setAttr("data-state","pause")})),this.on(ie,(function(){e.setAttr("data-state","play")}))}},{key:"onPluginsReady",value:function(){var e=this,t=this.player;if(!this.config.disable){if(this.config.isShowIcon){var i={config:{onClick:function(){e.getMini()}}};t.controls.registerPlugin(_i,i,_i.pluginName)}var n=z.checkTouchSupport()?"touchend":"click";this.bind(".mini-cancel-btn",n,this.onCancelClick),this.bind(".play-icon",n,this.onCenterClick),this.config.disableDrag||(this._draggabilly=new Oi(this.player.root,{handle:this.root})),this.config.isScrollSwitch&&window.addEventListener("scroll",this.onScroll)}}},{key:"registerIcons",value:function(){return{play:{icon:gi,class:"xg-icon-play"},pause:{icon:vi,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".play-icon",e.play),this.appendChild(".play-icon",e.pause)}},{key:"getMini",value:function(){var e=this;if(!this.isMini){var t=this.player,i=this.playerConfig,n=this.config.target||this.player.root;this.lastStyle={},z.addClass(t.root,"xgplayer-mini"),["width","height","top","left"].map((function(t){e.lastStyle[t]=n.style[t],n.style[t]="".concat(e.pos[t],"px")})),i.fluid&&(n.style["padding-top"]=""),this.emit(Me,!0),t.isMini=this.isMini=!0}}},{key:"exitMini",value:function(){var e=this;if(!this.isMini)return!1;var t=this.player,i=this.playerConfig,n=this.config.target||this.player.root;z.removeClass(t.root,"xgplayer-mini"),this.lastStyle&&Object.keys(this.lastStyle).map((function(t){n.style[t]=e.lastStyle[t]})),this.lastStyle=null,i.fluid&&(t.root.style.width="100%",t.root.style.height="0",t.root.style["padding-top"]="".concat(100*i.height/i.width,"%")),this.emit(Me,!1),this.isMini=t.isMini=!1}},{key:"destroy",value:function(){window.removeEventListener("scroll",this.onScroll);var e=z.checkTouchSupport()?"touchend":"click";this.unbind(".mini-cancel-btn",e,this.onCancelClick),this.unbind(".play-icon",e,this.onCenterClick),this._draggabilly&&this._draggabilly.destroy(),this._draggabilly=null,this.exitMini()}},{key:"render",value:function(){if(!this.config.disable)return'\n      <xg-mini-layer class="xg-mini-layer">\n      <xg-mini-header class="xgplayer-mini-header">\n      '.concat(bi(this,"MINI_DRAG",this.playerConfig.isHideTips),'\n      </xg-mini-header>\n      <div class="mini-cancel-btn">\n        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">\n          <path fill="#fff" fill-rule="evenodd" d="M3.99 3.49a1 1 0 0 1 1.414 0L10 8.085l4.596-4.595a1 1 0 1 1 1.414 1.414L11.414 9.5l4.596 4.596a1 1 0 0 1 .084 1.32l-.084.094a1 1 0 0 1-1.414 0L10 10.914 5.404 15.51a1 1 0 0 1-1.414-1.414L8.585 9.5 3.99 4.904a1 1 0 0 1-.084-1.32z"></path>\n        </svg>\n      </div>\n      <div class="play-icon">\n      </div>\n      </xg-mini-layer>')}}],[{key:"pluginName",get:function(){return"miniscreen"}},{key:"defaultConfig",get:function(){return{index:10,disable:!1,width:320,height:180,left:-1,top:-1,isShowIcon:!1,isScrollSwitch:!1,scrollTop:0,disableDrag:!1}}}]),t}(),Ri={mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousemove:"onMouseMove"},Mi=["videoClick","videoDbClick"],Ni=function(){S(t,bt);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onMouseMove",(function(e){var t=I(i),n=t.player,o=t.playerConfig;n.isActive||(n.focus({autoHide:!o.closeDelayBlur}),!o.closeFocusVideoFocus&&n.media.focus())})),x(I(i),"onMouseEnter",(function(e){var t=I(i),n=t.playerConfig,o=t.player;!n.closeFocusVideoFocus&&o.media.focus(),n.closeDelayBlur?o.focus({autoHide:!1}):o.focus(),i.emit(Ae)})),x(I(i),"onMouseLeave",(function(e){var t=i.playerConfig,n=t.closePlayerBlur,o=t.leavePlayerTime,r=t.closeDelayBlur;n||r||(o?i.player.focus({autoHide:!0,delay:o}):i.player.blur({ignorePaused:!0})),i.emit(Le)})),x(I(i),"onVideoClick",(function(e){var t=I(i),n=t.player,o=t.playerConfig;e.target&&o.closeVideoClick||e.target!==n.root&&e.target!==n.media&&e.target!==n.innerContainer&&e.target!==n.media.__canvas||(e.preventDefault(),o.closeVideoStopPropagation||e.stopPropagation(),i._clickCount++,i.clickTimer&&(clearTimeout(i.clickTimer),i.clickTimer=null),i.clickTimer=setTimeout((function(){i._clickCount&&(i._clickCount--,gt(I(i),Mi[0],(function(e,t){i.switchPlayPause(t.e)}),{e:e,paused:n.paused}),clearTimeout(i.clickTimer),i.clickTimer=null)}),300))})),x(I(i),"onVideoDblClick",(function(e){var t=I(i),n=t.player,o=t.playerConfig;o.closeVideoDblclick||!e.target||e.target!==n.media&&e.target!==n.media.__canvas||(!o.closeVideoClick&&i._clickCount<2?i._clickCount=0:(i._clickCount=0,i.clickTimer&&(clearTimeout(i.clickTimer),i.clickTimer=null),e.preventDefault(),e.stopPropagation(),gt(I(i),Mi[1],(function(e,t){i.emitUserAction(t.e,"switch_fullscreen",{props:"fullscreen",from:n.fullscreen,to:!n.fullscreen}),n.fullscreen?n.exitFullscreen():n.getFullscreen()}),{e:e,fullscreen:n.fullscreen})))})),i}return T(t,[{key:"afterCreate",value:function(){var e=this;this._clickCount=0,Mi.map((function(t){e.__hooks[t]=null})),"mobile"===this.playerConfig.isMobileSimulateMode||"mobile"===J.device&&!J.os.isIpad||this.initEvents()}},{key:"initEvents",value:function(){var e=this,t=this.player,i=t.media,n=t.root,o=this.playerConfig.enableContextmenu;n&&n.addEventListener("click",this.onVideoClick,!1),n&&n.addEventListener("dblclick",this.onVideoDblClick,!1),Object.keys(Ri).map((function(t){n.addEventListener(t,e[Ri[t]],!1)})),!o&&i&&i.addEventListener("contextmenu",this.onContextmenu,!1)}},{key:"switchPlayPause",value:function(e){var t=this.player;this.emitUserAction(e,"switch_play_pause",{props:"paused",from:t.paused,to:!t.paused}),t.ended?t.duration!==1/0&&t.duration>0&&t.replay():t.paused?t.play():t.pause()}},{key:"onContextmenu",value:function(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.stopPropagation?e.stopPropagation():(e.returnValue=!1,e.cancelBubble=!0)}},{key:"destroy",value:function(){var e=this,t=this.player,i=t.video,n=t.root;this.clickTimer&&clearTimeout(this.clickTimer),n.removeEventListener("click",this.onVideoClick,!1),n.removeEventListener("dblclick",this.onVideoDblClick,!1),i.removeEventListener("contextmenu",this.onContextmenu,!1),Object.keys(Ri).map((function(t){n.removeEventListener(t,e[Ri[t]],!1)}))}}],[{key:"pluginName",get:function(){return"pc"}},{key:"defaultConfig",get:function(){return{}}}]),t}(),Fi="press",Hi="pressend",Bi="doubleclick",Ui="click",ji="touchmove",Vi="touchstart",Wi="touchend",Gi={start:"touchstart",end:"touchend",move:"touchmove",cancel:"touchcancel"},zi={start:"mousedown",end:"mouseup",move:"mousemove",cancel:"mouseleave"};function Ki(e){return e&&e.length>0?e[e.length-1]:null}var Yi=function(){function e(t){var i=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{eventType:"touch"};_(this,e),x(this,"onTouchStart",(function(e){var t=i._pos,n=i.root,o=Ki(e.touches);t.x=o?parseInt(o.pageX,10):e.pageX,t.y=o?parseInt(o.pageX,10):e.pageX,t.start=!0,i.__setPress(e),n.addEventListener(i.events.end,i.onTouchEnd),n.addEventListener(i.events.cancel,i.onTouchCancel),n.addEventListener(i.events.move,i.onTouchMove),i.trigger(Vi,e)})),x(this,"onTouchCancel",(function(e){i.onTouchEnd(e)})),x(this,"onTouchEnd",(function(e){var t=i._pos,n=i.root;i.__clearPress(),n.removeEventListener(i.events.cancel,i.onTouchCancel),n.removeEventListener(i.events.end,i.onTouchEnd),n.removeEventListener(i.events.move,i.onTouchMove),e.moving=t.moving,e.press=t.press,t.press&&i.trigger(Hi,e),i.trigger(Wi,e),!t.press&&!t.moving&&i.__setDb(e),t.press=!1,t.start=!1,t.moving=!1})),x(this,"onTouchMove",(function(e){var t=i._pos,n=i.config,o=Ki(e.touches),r=o?parseInt(o.pageX,10):e.pageX,s=o?parseInt(o.pageY,10):e.pageX,a=r-t.x,l=s-t.y;Math.abs(l)<n.miniStep&&Math.abs(a)<n.miniStep||(i.__clearPress(),t.press&&i.trigger(Hi,e),t.press=!1,t.moving=!0,i.trigger(ji,e))})),this._pos={moving:!1,start:!1,x:0,y:0},this.config={pressDelay:600,dbClickDelay:200,disablePress:!1,disableDbClick:!1,miniStep:2,needPreventDefault:!0},Object.keys(n).map((function(e){i.config[e]=n[e]})),this.root=t,this.events="mouse"===n.eventType?zi:Gi,this.pressIntrvalId=null,this.dbIntrvalId=null,this.__handlers={},this._initEvent()}return T(e,[{key:"_initEvent",value:function(){this.root.addEventListener(this.events.start,this.onTouchStart)}},{key:"__setPress",value:function(e){var t=this,i=this.config;this.pressIntrvalId&&this.__clearPress(),this.pressIntrvalId=setTimeout((function(){t.trigger(Fi,e),t._pos.press=!0,t.__clearPress()}),i.pressDelay)}},{key:"__clearPress",value:function(){window.clearTimeout(this.pressIntrvalId),this.pressIntrvalId=null}},{key:"__setDb",value:function(e){var t=this,i=this.config;if(this.dbIntrvalId)return this.__clearDb(),void this.trigger(Bi,e);this.dbIntrvalId=setTimeout((function(){t.__clearDb(),t._pos.start||t._pos.press||t._pos.moving||t.trigger(Ui,e)}),i.dbClickDelay)}},{key:"__clearDb",value:function(){clearTimeout(this.dbIntrvalId),this.dbIntrvalId=null}},{key:"on",value:function(e,t){this.__handlers[e]||(this.__handlers[e]=[]),this.__handlers[e].push(t)}},{key:"off",value:function(e,t){if(this.__handlers[e]){for(var i=this.__handlers[e],n=-1,o=0;o<i.length;o++)if(i[o]===t){n=o;break}n>=0&&this.__handlers[e].splice(n,1)}}},{key:"trigger",value:function(e,t){this.__handlers[e]&&this.__handlers[e].map((function(e){try{e(t)}catch(i){}}))}},{key:"destroy",value:function(){var e=this,t={touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"};Object.keys(t).forEach((function(i){e.root.removeEventListener(i,e[t[i]])}))}}]),e}();function Xi(){return(new DOMParser).parseFromString('<svg width="20" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg"\n  xmlns:xlink="http://www.w3.org/1999/xlink">\n  <path opacity="0.54"\n    d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z"\n    fill="white" />\n  <path transform="translate(5 0)" d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z" fill="white"/>\n</svg>',"image/svg+xml").firstChild}var qi="auto",Zi="seeking",Ji="playbackrate",$i=["videoClick","videoDbClick"],Qi=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"onTouchStart",(function(e){var t=I(n),i=t.player,o=t.config,r=t.pos,s=t.playerConfig,a=n.getTouche(e);if(a&&!o.disableGesture&&n.duration>0&&!i.ended){r.isStart=!0,n.timer&&clearTimeout(n.timer),z.checkIsFunction(s.disableSwipeHandler)&&s.disableSwipeHandler(),n.find(".xg-dur").innerHTML=z.format(n.duration);var l=n.root.getBoundingClientRect();90===i.rotateDeg?(r.top=l.left,r.left=l.top,r.width=l.height,r.height=l.width):(r.top=l.top,r.left=l.left,r.width=l.width,r.height=l.height);var c=parseInt(a.pageX-r.left,10),u=parseInt(a.pageY-r.top,10);r.x=90===i.rotateDeg?u:c,r.y=90===i.rotateDeg?c:u,r.scopeL=o.scopeL*r.width,r.scopeR=(1-o.scopeR)*r.width,r.scopeM1=r.width*(1-o.scopeM)/2,r.scopeM2=r.width-r.scopeM1}})),x(I(n),"onTouchMove",(function(e){var t=n.getTouche(e),i=I(n),o=i.pos,r=i.config,s=i.player;if(t&&!r.disableGesture&&n.duration&&o.isStart){var a=r.miniMoveStep,l=r.hideControlsActive,c=parseInt(t.pageX-o.left,10),u=parseInt(t.pageY-o.top,10),h=90===s.rotateDeg?u:c,d=90===s.rotateDeg?c:u;if(Math.abs(h-o.x)>a||Math.abs(d-o.y)>a){var f=h-o.x,p=d-o.y,g=o.scope;if(-1===g&&(0===(g=n.checkScope(h,d,f,p,o))&&(l?s.blur():s.focus({autoHide:!1}),!o.time&&(o.time=parseInt(1e3*s.currentTime,10)+1e3*n.timeOffset)),o.scope=g),-1===g||g>0&&!r.gestureY||0===g&&!r.gestureX)return;n.executeMove(f,p,g,o.width,o.height),o.x=h,o.y=d}}})),x(I(n),"onTouchEnd",(function(e){var t=I(n),i=t.player,o=t.pos,r=t.playerConfig;if(setTimeout((function(){i.getPlugin("progress")&&i.getPlugin("progress").resetSeekState()}),10),o.isStart){o.scope>-1&&e.cancelable&&e.preventDefault();var s=n.config,a=s.disableGesture,l=s.gestureX;!a&&l?n.endLastMove(o.scope):o.time=0,o.scope=-1,n.resetPos(),z.checkIsFunction(r.enableSwipeHandler)&&r.enableSwipeHandler(),n.changeAction(qi)}})),x(I(n),"onRootTouchMove",(function(e){!n.config.disableGesture&&n.config.gestureX&&n.checkIsRootTarget(e)&&(e.stopPropagation(),n.pos.isStart?n.onTouchMove(e):n.onTouchStart(e))})),x(I(n),"onRootTouchEnd",(function(e){n.pos.scope>-1&&n.onTouchEnd(e)})),n.pos={isStart:!1,x:0,y:0,time:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1},n.timer=null,n}return T(t,[{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"registerIcons",value:function(){return{seekTipIcon:{icon:Xi,class:"xg-seek-pre"}}}},{key:"afterCreate",value:function(){var e=this;$i.map((function(t){e.__hooks[t]=null}));var t=this.playerConfig,i=this.config,n=this.player;!0===t.closeVideoDblclick&&(i.closedbClick=!0),this.resetPos(),z.isUndefined(t.disableGesture)||(i.disableGesture=!!t.disableGesture),this.appendChild(".xg-seek-icon",this.icons.seekTipIcon),this.xgMask=z.createDom("xg-mask","",{},"xgmask"),n.root.appendChild(this.xgMask),this.initCustomStyle(),this.registerThumbnail();var o="mouse"===this.domEventType?"mouse":"touch";this.touch=new Yi(this.root,{eventType:o,needPreventDefault:!this.config.disableGesture}),this.root.addEventListener("contextmenu",(function(e){e.preventDefault()})),n.root.addEventListener("touchmove",this.onRootTouchMove,!0),n.root.addEventListener("touchend",this.onRootTouchEnd,!0),n.root.addEventListener("touchcancel",this.onRootTouchEnd,!0);var r=this.player.controls;r&&r.center&&(r.center.addEventListener("touchmove",this.onRootTouchMove,!0),r.center.addEventListener("touchend",this.onRootTouchEnd,!0),r.center.addEventListener("touchcancel",this.onRootTouchEnd,!0)),this.on(de,(function(){var t=e.player,i=e.config;t.duration>0&&1e3*t.duration<i.moveDuration&&(i.moveDuration=1e3*t.duration)})),this.on([he,oe],(function(){var t=e.pos,i=t.time;!t.isStart&&i>0&&(e.pos.time=0)}));var s={touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",press:"onPress",pressend:"onPressEnd",click:"onClick",doubleclick:"onDbClick"};if(Object.keys(s).map((function(t){e.touch.on(t,(function(i){e[s[t]](i)}))})),!i.disableActive){var a=n.plugins.progress;a&&(a.addCallBack("dragmove",(function(t){e.activeSeekNote(t.currentTime,t.forward)})),["dragend","click"].forEach((function(t){a.addCallBack(t,(function(){e.changeAction(qi)}))})))}}},{key:"registerThumbnail",value:function(){var e=this.player.plugins.thumbnail;if(e&&e.usable){this.thumbnail=e.createThumbnail(null,"mobile-thumbnail");var t=this.find(".time-preview");t.insertBefore(this.thumbnail,t.children[0])}}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle,t=e.playedColor,i=e.progressColor,n=e.timePreviewStyle,o=e.curTimeColor,r=e.durationColor;if(t&&(this.find(".xg-curbar").style.backgroundColor=t),i&&(this.find(".xg-bar").style.backgroundColor=i),n){var s=this.find(".time-preview");Object.keys(n).forEach((function(e){s.style[e]=n[e]}))}var a=o||t,l=r;a&&(this.find(".xg-cur").style.color=a),l&&(this.find(".xg-dur").style.color=l),this.config.disableTimeProgress&&z.addClass(this.find(".xg-timebar"),"hide")}},{key:"resetPos",value:function(){var e=this;this.pos?(this.pos.isStart=!1,this.pos.scope=-1,["x","y","width","height","scopeL","scopeR","scopeM1","scopeM2"].map((function(t){e.pos[t]=0}))):this.pos={isStart:!1,x:0,y:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1,time:0}}},{key:"changeAction",value:function(e){var t=this.player;this.root.setAttribute("data-xg-action",e);var i=t.plugins.start;i&&i.recover()}},{key:"getTouche",value:function(e){this.player.rotateDeg;var t=e.touches&&e.touches.length>0?e.touches[e.touches.length-1]:e;return{pageX:t.pageX,pageY:t.pageY}}},{key:"checkScope",value:function(e,t,i,n,o){var r=o.width,s=-1;if(e<0||e>r)return s;var a=0===n?Math.abs(i):Math.abs(i/n);return Math.abs(i)>0&&a>=1.73&&e>o.scopeM1&&e<o.scopeM2?s=0:(0===Math.abs(i)||a<=.57)&&(s=e<o.scopeL?1:e>o.scopeR?2:3),s}},{key:"executeMove",value:function(e,t,i,n,o){switch(i){case 0:this.updateTime(e/n*this.config.scopeM);break;case 1:this.updateBrightness(t/o);break;case 2:J.os.isIos||this.updateVolume(t/o)}}},{key:"endLastMove",value:function(e){var t=this,i=this.pos,n=this.player,o=this.config,r=(i.time-this.timeOffset)/1e3;if(0===e)n.seek(Number(r).toFixed(1)),o.hideControlsEnd?n.blur():n.focus(),this.timer=setTimeout((function(){t.pos.time=0}),500);this.changeAction(qi)}},{key:"checkIsRootTarget",value:function(e){var t=this.player.plugins||{};return(!t.progress||!t.progress.root.contains(e.target))&&(t.start&&t.start.root.contains(e.target)||t.controls&&t.controls.root.contains(e.target))}},{key:"sendUseAction",value:function(e){var t=this.player.paused;this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t,to:!t})}},{key:"clickHandler",value:function(e){var t=this.player,i=this.config,n=this.playerConfig;t.state<Kt?n.closeVideoClick||(this.sendUseAction(z.createEvent("click")),t.play()):!i.closedbClick||n.closeVideoClick?t.isActive?t.blur():t.focus():n.closeVideoClick||((t.isActive||i.focusVideoClick)&&(this.sendUseAction(z.createEvent("click")),this.switchPlayPause()),t.focus())}},{key:"dbClickHandler",value:function(e){var t=this.config,i=this.player;!t.closedbClick&&i.state>=Kt&&(this.sendUseAction(z.createEvent("dblclick")),this.switchPlayPause())}},{key:"onClick",value:function(e){var t=this,i=this.player;gt(this,$i[0],(function(e,i){t.clickHandler(i.e)}),{e:e,paused:i.paused})}},{key:"onDbClick",value:function(e){var t=this,i=this.player;gt(this,$i[1],(function(e,i){t.dbClickHandler(i.e)}),{e:e,paused:i.paused})}},{key:"onPress",value:function(e){var t=this.pos,i=this.config,n=this.player;i.disablePress||(t.rate=this.player.playbackRate,this.emitUserAction("press","change_rate",{prop:"playbackRate",from:n.playbackRate,to:i.pressRate}),n.playbackRate=i.pressRate,this.changeAction(Ji))}},{key:"onPressEnd",value:function(e){var t=this.pos,i=this.config,n=this.player;i.disablePress||(this.emitUserAction("pressend","change_rate",{prop:"playbackRate",from:n.playbackRate,to:t.rate}),n.playbackRate=t.rate,t.rate=1,this.changeAction(qi))}},{key:"updateTime",value:function(e){var t=this.player,i=this.config,n=this.player.duration;e=Number(e.toFixed(4));var o=parseInt(e*i.moveDuration,10)+this.timeOffset;o=(o+=this.pos.time)<0?0:o>1e3*n?1e3*n-200:o,t.getPlugin("time")&&t.getPlugin("time").updateTime(o/1e3),t.getPlugin("progress")&&t.getPlugin("progress").updatePercent(o/1e3/this.duration,!0),this.activeSeekNote(o/1e3,e>0),i.isTouchingSeek&&t.seek(Number((o-this.timeOffset)/1e3).toFixed(1)),this.pos.time=o}},{key:"updateVolume",value:function(e){this.player.rotateDeg&&(e=-e);var t=this.player,i=this.pos;if(e=parseInt(100*e,10),i.volume+=e,!(Math.abs(i.volume)<10)){var n=parseInt(10*t.volume,10)-parseInt(i.volume/10,10);n=n>10?10:n<1?0:n,t.volume=n/10,i.volume=0}}},{key:"updateBrightness",value:function(e){var t=this.pos,i=this.config,n=this.xgMask;if(i.darkness){this.player.rotateDeg&&(e=-e);var o=t.light+.8*e;o=o>i.maxDarkness?i.maxDarkness:o<0?0:o,n&&(n.style.backgroundColor="rgba(0,0,0,".concat(o,")")),t.light=o}}},{key:"activeSeekNote",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.player,n=this.config,o=!(this.duration!==1/0&&this.duration>0);if(e&&"number"==typeof e&&!o&&!n.disableActive){e<0?e=0:e>i.duration&&(e=i.duration-.2),this.changeAction(Zi);var r=i.plugins.start;r&&r.focusHide(),this.find(".xg-dur").innerHTML=z.format(this.duration),this.find(".xg-cur").innerHTML=z.format(e),this.find(".xg-curbar").style.width="".concat(e/this.duration*100,"%"),t?z.removeClass(this.find(".xg-seek-show"),"xg-back"):z.addClass(this.find(".xg-seek-show"),"xg-back"),this.updateThumbnails(e)}}},{key:"updateThumbnails",value:function(e){var t=this.player.plugins.thumbnail;t&&t.usable&&this.thumbnail&&t.update(this.thumbnail,e,160,90)}},{key:"switchPlayPause",value:function(){var e=this.player;if(e.state<Gt)return!1;e.ended||(e.paused?e.play():e.pause())}},{key:"disableGesture",value:function(){this.config.disableGesture=!0}},{key:"enableGesture",value:function(){this.config.disableGesture=!1}},{key:"destroy",value:function(){var e=this.player;this.timer&&clearTimeout(this.timer),this.thumbnail=null,e.root.removeChild(this.xgMask),this.xgMask=null,this.touch&&this.touch.destroy(),this.touch=null,e.root.removeEventListener("touchmove",this.onRootTouchMove,!0),e.root.removeEventListener("touchend",this.onRootTouchEnd,!0),e.root.removeEventListener("touchcancel",this.onRootTouchEnd,!0);var t=this.player.controls;t&&t.center&&(t.center.removeEventListener("touchmove",this.onRootTouchMove,!0),t.center.removeEventListener("touchend",this.onRootTouchEnd,!0),t.center.removeEventListener("touchcancel",this.onRootTouchEnd,!0))}},{key:"render",value:function(){var e="normal"!==this.config.gradient?"gradient ".concat(this.config.gradient):"gradient";return'\n     <xg-trigger class="trigger">\n     <div class="'.concat(e,'"></div>\n        <div class="time-preview">\n            <div class="xg-seek-show ').concat(this.config.disableSeekIcon?" hide-seek-icon":"",'">\n              <i class="xg-seek-icon"></i>\n              <span class="xg-cur">00:00</span>\n              <span class="xg-separator">/</span>\n              <span class="xg-dur">00:00</span>\n            </div>\n              <div class="xg-bar xg-timebar">\n                <div class="xg-curbar"></div>\n              </div>\n        </div>\n        <div class="xg-playbackrate xg-top-note">\n            <span><i>').concat(this.config.pressRate,"X</i>").concat(this.i18n.FORWARD,"</span>\n        </div>\n     </xg-trigger>\n    ")}}],[{key:"pluginName",get:function(){return"mobile"}},{key:"defaultConfig",get:function(){return{index:0,disableGesture:!1,gestureX:!0,gestureY:!0,gradient:"normal",isTouchingSeek:!1,miniMoveStep:5,miniYPer:5,scopeL:.25,scopeR:.25,scopeM:.9,pressRate:2,darkness:!0,maxDarkness:.8,disableActive:!1,disableTimeProgress:!1,hideControlsActive:!1,hideControlsEnd:!1,moveDuration:36e4,closedbClick:!1,disablePress:!0,disableSeekIcon:!1,focusVideoClick:!1}}}]),t}();function en(e){var t=e.tagName;return!("INPUT"!==t&&"TEXTAREA"!==t&&!e.isContentEditable)}var tn=function(){S(t,bt);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onBodyKeyDown",(function(e){if(i.player){var t=e||window.event,n=t.keyCode,o=I(i),r=o._keyState,s=o.player,a=i.config,l=a.disable,c=a.disableBodyTrigger,u=a.isIgnoreUserActive;l||c||!s.isUserActive&&!u||en(t.target)||!i.checkIsVisible()||t.metaKey||t.altKey||t.ctrlKey?r.isBodyKeyDown=!1:(e.repeat||r.isKeyDown||((t.target===document.body||i.config.isGlobalTrigger&&!en(t.target))&&i.checkCode(n,!0)&&(r.isBodyKeyDown=!0),document.addEventListener("keyup",i.onBodyKeyUp)),r.isBodyKeyDown&&i.handleKeyDown(t))}})),x(I(i),"onBodyKeyUp",(function(e){i.player&&(document.removeEventListener("keyup",i.onBodyKeyUp),i.handleKeyUp(e))})),x(I(i),"onKeydown",(function(e){if(i.player){var t=e||window.event,n=I(i)._keyState;if(!t.repeat){if(i.config.disable||i.config.disableRootTrigger||t.metaKey||t.altKey||t.ctrlKey)return;!t||37!==t.keyCode&&!i.checkCode(t.keyCode)||t.target!==i.player.root&&t.target!==i.player.video&&t.target!==i.player.controls.el||(n.isKeyDown=!0),i.player.root.addEventListener("keyup",i.onKeyup)}n.isKeyDown&&i.handleKeyDown(t)}})),x(I(i),"onKeyup",(function(e){i.player&&(i.player.root.removeEventListener("keyup",i.onKeyup),i.handleKeyUp(e))})),i}return T(t,[{key:"mergekeyCodeMap",value:function(){var e=this,t=this.config.keyCodeMap;t&&Object.keys(t).map((function(i){e.keyCodeMap[i]?["keyCode","action","disable","pressAction","disablePress","isBodyTarget"].map((function(n){t[i][n]&&(e.keyCodeMap[i][n]=t[i][n])})):e.keyCodeMap[i]=t[i]}))}},{key:"afterCreate",value:function(){this.config.disable=!this.playerConfig.keyShortcut;var e="function"==typeof this.config.seekStep?this.config.seekStep(this.player):this.config.seekStep;e&&"number"==typeof e&&(this.seekStep=e),this.keyCodeMap={space:{keyCode:32,action:"playPause",disable:!1,disablePress:!1,noBodyTarget:!1},up:{keyCode:38,action:"upVolume",disable:!1,disablePress:!1,noBodyTarget:!0},down:{keyCode:40,action:"downVolume",disable:!1,disablePress:!1,noBodyTarget:!0},left:{keyCode:37,action:"seekBack",disablePress:!1,disable:!1},right:{keyCode:39,action:"seek",pressAction:"changePlaybackRate",disablePress:!1,disable:!1},esc:{keyCode:27,action:"exitFullscreen",disablePress:!0,disable:!1}},this.mergekeyCodeMap(),this._keyState={isKeyDown:!1,isBodyKeyDown:!1,isPress:!1,tt:0,playbackRate:0},this.player.root.addEventListener("keydown",this.onKeydown),document.addEventListener("keydown",this.onBodyKeyDown)}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.config[i]=e[i]}))}},{key:"checkIsVisible",value:function(){if(!this.config.checkVisible)return!0;var e=this.player.root.getBoundingClientRect(),t=e.height,i=e.top,n=e.bottom,o=window.innerHeight;return!(i<0&&i<0-.9*t||n>0&&n-o>.9*t)}},{key:"checkCode",value:function(e,t){var i=this,n=!1;return Object.keys(this.keyCodeMap).map((function(o){i.keyCodeMap[o]&&e===i.keyCodeMap[o].keyCode&&!i.keyCodeMap[o].disable&&(n=!t||t&&!i.keyCodeMap[o].noBodyTarget)})),n}},{key:"downVolume",value:function(e){var t=this.player;if(!(t.volume<=0)){var i=parseFloat((t.volume-.1).toFixed(1)),n={volume:{from:t.volume,to:i}};this.emitUserAction(e,"change_volume",{props:n}),t.volume=i>=0?i:0}}},{key:"upVolume",value:function(e){var t=this.player;if(!(t.volume>=1)){var i=parseFloat((t.volume+.1).toFixed(1)),n={volume:{from:t.volume,to:i}};this.emitUserAction(e,"change_volume",{props:n}),t.volume=i<=1?i:1}}},{key:"seek",value:function(e){var t=this.player,i=t.currentTime,n=t.offsetCurrentTime,o=t.duration,r=t.offsetDuration,s=t.timeSegments,a=n>-1?n:i,l=r||o,c=e.repeat&&this.seekStep>=4?parseInt(this.seekStep/2,10):this.seekStep;a+c<=l?a+=c:a=l;var u=z.getCurrentTimeByOffset(a,s),h={currentTime:{from:i,to:u}};this.emitUserAction(e,"seek",{props:h}),this.player.currentTime=u}},{key:"seekBack",value:function(e){var t=this.player,i=t.currentTime,n=t.offsetCurrentTime,o=t.timeSegments,r=(n>-1?n:i)-(e.repeat?parseInt(this.seekStep/2,10):this.seekStep);r<0&&(r=0);var s={currentTime:{from:i,to:r=z.getCurrentTimeByOffset(r,o)}};this.emitUserAction(e,"seek",{props:s}),this.player.currentTime=r}},{key:"changePlaybackRate",value:function(e){var t=this._keyState,i=this.config,n=this.player;0===t.playbackRate&&(t.playbackRate=n.playbackRate,n.playbackRate=i.playbackRate)}},{key:"playPause",value:function(e){var t=this.player;t&&(this.emitUserAction(e,"switch_play_pause"),t.paused?t.play():t.pause())}},{key:"exitFullscreen",value:function(e){var t=this.player,i=t.fullscreen,n=t.cssfullscreen;i&&(this.emitUserAction("keyup","switch_fullscreen",{prop:"fullscreen",from:i,to:!i}),t.exitFullscreen()),n&&(this.emitUserAction("keyup","switch_css_fullscreen",{prop:"cssfullscreen",from:n,to:!n}),t.exitCssFullscreen())}},{key:"handleKeyDown",value:function(e){var t=this._keyState;if(e.repeat){t.isPress=!0;var i=Date.now();if(i-t.tt<200)return;t.tt=i}this.handleKeyCode(e.keyCode,e,t.isPress)}},{key:"handleKeyUp",value:function(e){var t=this._keyState;t.playbackRate>0&&(this.player.playbackRate=t.playbackRate,t.playbackRate=0),t.isKeyDown=!1,t.isPress=!1,t.tt=0}},{key:"handleKeyCode",value:function(e,t,i){for(var n,o=Object.keys(this.keyCodeMap),r=0;r<o.length;r++){var s=this.keyCodeMap[o[r]],a=s.action,l=s.keyCode,c=s.disable,u=s.pressAction,h=s.disablePress;if(l===e){if(!(c||i&&h)){var d=i&&u||a;"function"==typeof d?a(t,this.player,i):"string"==typeof d&&"function"==typeof this[d]&&this[d](t,this.player,i),this.emit(We,C({key:o[r],target:t.target,isPress:i},this.keyCodeMap[o[r]]))}(n=t).preventDefault(),n.returnValue=!1,t.stopPropagation();break}}}},{key:"destroy",value:function(){this.player.root.removeEventListener("keydown",this.onKeydown),document.removeEventListener("keydown",this.onBodyKeyDown),this.player.root.removeEventListener("keyup",this.onKeyup),document.removeEventListener("keyup",this.onBodyKeyUp)}},{key:"disable",value:function(){this.config.disable=!0}},{key:"enable",value:function(){this.config.disable=!1}}],[{key:"pluginName",get:function(){return"keyboard"}},{key:"defaultConfig",get:function(){return{seekStep:10,checkVisible:!1,disableBodyTrigger:!1,disableRootTrigger:!1,isGlobalTrigger:!0,keyCodeMap:{},disable:!1,playbackRate:2,isIgnoreUserActive:!0}}}]),t}();function nn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="-5 -5 110 110">\n  <path d="M100,50A50,50,0,1,1,50,0" stroke-width="5" stroke="#ddd" stroke-dasharray="236" fill="none"></path>\n</svg>\n',"image/svg+xml").firstChild}var on=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"registerIcons",value:function(){return{loadingIcon:nn}}},{key:"afterCreate",value:function(){this.appendChild("xg-loading-inner",this.icons.loadingIcon)}},{key:"render",value:function(){return'\n    <xg-loading class="xgplayer-loading">\n      <xg-loading-inner></xg-loading-inner>\n    </xg-loading>'}}],[{key:"pluginName",get:function(){return"loading"}},{key:"defaultConfig",get:function(){return{position:Tt.ROOT}}}]),t}(),rn=[{tag:"xg-cache",className:"xgplayer-progress-cache",styleKey:"cachedColor"},{tag:"xg-played",className:"xgplayer-progress-played",styleKey:"playedColor"}],sn=function(){function e(t){_(this,e),this.fragments=t.fragments||[],0===this.fragments.length&&this.fragments.push({percent:1}),this._callBack=t.actionCallback,this.fragConfig={fragFocusClass:t.fragFocusClass||"inner-focus-point",fragAutoFocus:!!t.fragAutoFocus,fragClass:t.fragClass||""},this.style=t.style||{playedColor:"",cachedColor:"",progressColor:""},this.duration=0,this.cachedIndex=0,this.playedIndex=0,this.focusIndex=-1}return T(e,[{key:"updateDuration",value:function(e){var t=this;this.duration=e;var i=0,n=this.fragments;this.fragments=n.map((function(e){return e.start=parseInt(i,10),e.end=parseInt(i+e.percent*t.duration,10),e.duration=parseInt(e.percent*t.duration,10),i+=e.percent*t.duration,e}))}},{key:"updateProgress",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"played",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{newIndex:0,curIndex:0,millisecond:0},i=this.progressList,n=this.fragments;if(!(i.length<1)){var o=t.newIndex,r=t.curIndex,s=t.millisecond;o!==r&&i.map((function(t,i){i<o?t[e].style.width="100%":i>o&&(t[e].style.width=0)}));var a=n[o],l=0===s?0:(s-a.start)/a.duration;i[o][e].style.width=l<0?0:"".concat(100*l,"%")}}},{key:"updateFocus",value:function(e){if(this.fragConfig.fragAutoFocus&&!(this.fragments.length<2))if(e){var t=this.findIndex(1e3*e.currentTime,this.focusIndex);if(t>=0&&t!==this.focusIndex){this.focusIndex>-1&&this.unHightLight(this.focusIndex),this.setHightLight(t);var i={index:t,preIndex:this.focusIndex,fragment:this.fragments[this.focusIndex]};this.focusIndex=t,this._callBack&&this._callBack(i)}}else if(this.focusIndex>-1){this.unHightLight(this.focusIndex);var n={index:-1,preIndex:this.focusIndex,fragment:null};this._callBack&&this._callBack(n),this.focusIndex=-1}}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;if(!this.duration||parseInt(1e3*t,10)!==this.duration){if(!t&&0!==t)return;this.updateDuration(parseInt(1e3*t,10))}var i=this.playedIndex,n=this.cachedIndex;if("Undefined"!==z.typeOf(e.played)){var o=this.findIndex(1e3*e.played,i);if(o<0)return;this.updateProgress("played",{newIndex:o,curIndex:i,millisecond:parseInt(1e3*e.played,10)}),this.playedIndex=o}if("Undefined"!==z.typeOf(e.cached)){var r=this.findIndex(1e3*e.cached,n);if(r<0)return;this.updateProgress("cached",{newIndex:r,curIndex:n,millisecond:parseInt(1e3*e.cached,10)}),this.cachedIndex=r}}},{key:"findIndex",value:function(e,t){var i=this.fragments;if(!i||0===i.length)return-1;if(1===i.length)return 0;if(t>-1&&t<i.length&&e>i[t].start&&e<i[t].end)return t;if(e>i[i.length-1].start)return i.length-1;for(var n=0;n<i.length;n++)if(e>i[n].start&&e<=i[n].end){t=n;break}return t}},{key:"findHightLight",value:function(){for(var e=this.root.children,t=0;t<e.length;t++)if(z.hasClass(e[t],this.fragConfig.fragFocusClass))return{dom:e[t],pos:e[t].getBoundingClientRect()}}},{key:"findFragment",value:function(e){var t=this.root.children;return e<0||e>=t.length?null:{dom:t[e],pos:t[e].getBoundingClientRect()}}},{key:"unHightLight",value:function(){for(var e=this.root.children,t=0;t<e.length;t++)z.removeClass(e[t],this.fragConfig.fragFocusClass)}},{key:"setHightLight",value:function(e){var t=this.root.children;if(e<t.length)return z.addClass(t[e],this.fragConfig.fragFocusClass),{dom:t[e],pos:t[e].getBoundingClientRect()}}},{key:"destroy",value:function(){this.progressList=null,this.fragments=null,this.root.innerHTML=""}},{key:"reset",value:function(e){var t=this;if(Object.keys(this.fragConfig).forEach((function(i){void 0!==e[i]&&(t.fragConfig[i]=e[i])})),e.fragments){if(this.fragments=0===e.fragments.length?[{percent:1}]:e.fragments,this.updateDuration(this.duration),this.playedIndex=0,this.cachedIndex=0,this.root)for(var i=this.root.children;i.length>0;)this.root.removeChild(i[0]);this.render()}}},{key:"render",value:function(){var e=this,t=this.style.progressColor;if(this.root||(this.root=z.createDom("xg-inners","",{},"progress-list")),this.fragments){var i=this.fragConfig,n=i.fragClass,o=i.fragFocusClass;this.progressList=this.fragments.map((function(i){var r=z.createDom("xg-inner","",{style:t?"background:".concat(t,"; flex: ").concat(i.percent):"flex: ".concat(i.percent)},"".concat(i.isFocus?o:""," xgplayer-progress-inner ").concat(n));return e.root.appendChild(r),rn.forEach((function(t){r.appendChild(z.createDom(t.tag,"",{style:t.styleKey?"background: ".concat(e.style[t.styleKey],"; width:0;"):"width:0;"},t.className))})),{cached:r.children[0],played:r.children[1]}}))}return this.root}}]),e}(),an={POINT:"inner-focus-point",HIGHLIGHT:"inner-focus-highlight"},ln=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"onMoveOnly",(function(e,t){var i=I(n),o=i.pos,r=i.config,s=i.player,a=t;if(e){z.event(e);var l=z.getEventPos(e,s.zoom),c=90===s.rotateDeg?l.clientY:l.clientX;if(o.moving&&Math.abs(o.x-c)<r.miniMoveStep)return;o.moving=!0,o.x=c,a=n.computeTime(e,c)}n.triggerCallbacks("dragmove",a,e),n._updateInnerFocus(a)})),x(I(n),"onBodyClick",(function(e){n.pos.isLocked&&(n.pos.isLocked=!1,e.preventDefault(),e.stopPropagation())})),x(I(n),"_mouseDownHandler",(function(e,t){n._state.time=t.currentTime,n.updateWidth(t.currentTime,t.seekTime,t.percent,0),n._updateInnerFocus(t)})),x(I(n),"_mouseUpHandler",(function(e,t){I(n).pos.moving&&n.updateWidth(t.currentTime,t.seekTime,t.percent,2)})),x(I(n),"_mouseMoveHandler",(function(e,t){var i=I(n),o=i._state,r=i.pos,s=i.config,a=i.player;o.time<t.currentTime?t.forward=!0:t.forward=!1,o.time=t.currentTime,r.isDown&&!r.moving&&(r.moving=!0,s.isPauseMoving&&a.pause(),n.triggerCallbacks("dragstart",t,e),n.emitUserAction("drag","dragstart",t)),n.updateWidth(t.currentTime,t.seekTime,t.percent,1),n.triggerCallbacks("dragmove",t,e),n._updateInnerFocus(t)})),x(I(n),"onMouseDown",(function(e){var t=I(n),i=t._state,o=t.player,r=t.pos,s=t.config,a=t.playerConfig,l=z.getEventPos(e,o.zoom),c=90===o.rotateDeg?l.clientY:l.clientX;if(!(o.isMini||s.closeMoveSeek||!a.allowSeekAfterEnded&&o.ended)){if(o.duration||o.isPlaying){e.stopPropagation(),n.focus(),z.checkIsFunction(a.disableSwipeHandler)&&a.disableSwipeHandler(),z.checkIsFunction(s.onMoveStart)&&s.onMoveStart(),z.event(e),r.x=c,r.isDown=!0,r.moving=!1,i.prePlayTime=o.currentTime,o.focus({autoHide:!1}),n.isProgressMoving=!0,z.addClass(n.progressBtn,"active");var u=n.computeTime(e,c);return u.prePlayTime=i.prePlayTime,n._mouseDownHandlerHook(e,u),"touchstart"===e.type?(n.root.addEventListener("touchmove",n.onMouseMove),n.root.addEventListener("touchend",n.onMouseUp),n.root.addEventListener("touchcancel",n.onMouseUp)):(n.unbind("mousemove",n.onMoveOnly),document.addEventListener("mousemove",n.onMouseMove,!1),document.addEventListener("mouseup",n.onMouseUp,!1)),!0}o.play()}})),x(I(n),"onMouseUp",(function(e){var t=I(n),i=t.player,o=t.config,r=t.pos,s=t.playerConfig,a=t._state;e.stopPropagation(),e.preventDefault(),z.checkIsFunction(s.enableSwipeHandler)&&s.enableSwipeHandler(),z.checkIsFunction(o.onMoveEnd)&&o.onMoveEnd(),z.event(e),z.removeClass(n.progressBtn,"active");var l=n.computeTime(e,r.x);l.prePlayTime=a.prePlayTime,r.moving?(n.triggerCallbacks("dragend",l,e),n.emitUserAction("drag","dragend",l)):(n.triggerCallbacks("click",l,e),n.emitUserAction("click","click",l)),n._mouseUpHandlerHook(e,l),r.moving=!1,r.isDown=!1,r.x=0,r.y=0,r.isLocked=!0,a.prePlayTime=0,a.time=0;var c=e.type;"touchend"===c||"touchcancel"===c?(n.root.removeEventListener("touchmove",n.onMouseMove),n.root.removeEventListener("touchend",n.onMouseUp),n.root.removeEventListener("touchcancel",n.onMouseUp),n.blur()):(document.removeEventListener("mousemove",n.onMouseMove,!1),document.removeEventListener("mouseup",n.onMouseUp,!1),r.isEnter?"mobile"!==s.isMobileSimulateMode&&n.bind("mousemove",n.onMoveOnly):n.onMouseLeave(e)),z.setTimeout(I(n),(function(){n.resetSeekState()}),1),i.focus()})),x(I(n),"onMouseMove",(function(e){var t=I(n),i=t._state,o=t.pos,r=t.player,s=t.config;z.checkTouchSupport()&&e.preventDefault(),z.event(e);var a=z.getEventPos(e,r.zoom),l=90===r.rotateDeg?a.clientY:a.clientX,c=Math.abs(o.x-l);if(!(o.moving&&c<s.miniMoveStep||!o.moving&&c<s.miniStartStep)){o.x=l;var u=n.computeTime(e,l);u.prePlayTime=i.prePlayTime,n._mouseMoveHandlerHook(e,u)}})),x(I(n),"onMouseOut",(function(e){n.triggerCallbacks("mouseout",null,e)})),x(I(n),"onMouseOver",(function(e){n.triggerCallbacks("mouseover",null,e)})),x(I(n),"onMouseEnter",(function(e){var t=I(n),i=t.player,o=t.pos;if(!(o.isDown||o.isEnter||i.isMini||!i.config.allowSeekAfterEnded&&i.ended)){o.isEnter=!0,n.bind("mousemove",n.onMoveOnly),n.bind("mouseleave",n.onMouseLeave),z.event(e);var r=z.getEventPos(e,i.zoom),s=90===i.rotateDeg?r.clientY:r.clientX,a=n.computeTime(e,s);n.triggerCallbacks("mouseenter",a,e),n.focus()}})),x(I(n),"onMouseLeave",(function(e){n.triggerCallbacks("mouseleave",null,e),n.unlock(),n._updateInnerFocus(null)})),x(I(n),"onVideoResize",(function(){var e=n.pos,t=e.x,i=e.isDown;if(e.isEnter&&!i){var o=n.computeTime(null,t);n.onMoveOnly(null,o)}})),n.useable=!1,n.isProgressMoving=!1,n.__dragCallBacks=[],n._state={now:-1,direc:0,time:0,prePlayTime:-1},n._disableBlur=!1,"boolean"==typeof n.config.isDragingSeek&&(n.config.isDraggingSeek=n.config.isDragingSeek),n}return T(t,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i+this.timeOffset}},{key:"changeState",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.useable=e}},{key:"show",value:function(e){this.root&&(this.root.style.display="flex")}},{key:"_initInner",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t&&0!==t.length||(t=[{percent:1}]);var n=C(C({fragments:t},i),{},{actionCallback:function(t){e.emitUserAction("fragment_focus","fragment_focus",t)}});this.innerList?this.innerList.reset(n):(this.innerList=new sn(n),this.outer.insertBefore(this.innerList.render(),this.outer.children[0]),["findHightLight","unHightLight","setHightLight","findFragment"].map((function(t){e[t]=e.innerList[t].bind(e.innerList)})))}},{key:"_updateInnerFocus",value:function(e){this.innerList&&this.innerList.updateFocus(e)}},{key:"afterCreate",value:function(){if(!this.config.disable&&!this.playerConfig.isLive){this.pos={x:0,y:0,moving:!1,isDown:!1,isEnter:!1,isLocked:!1},this.outer=this.find("xg-outer");var e=this.config,t=e.fragFocusClass,i=e.fragAutoFocus,n=e.fragClass;this._initInner(this.config.fragments,{fragFocusClass:t,fragAutoFocus:i,fragClass:n,style:this.playerConfig.commonStyle||{}}),"mobile"===J.device&&(this.config.isDraggingSeek=!1,this.isMobile=!0),this.progressBtn=this.find(".xgplayer-progress-btn"),this.listenEvents(),this.bindDomEvents(),this.initCustomStyle()}}},{key:"listenEvents",value:function(){var e=this;this.on(de,(function(){e.onMouseLeave()})),this.on(ce,(function(){e.onTimeupdate()})),this.on(le,(function(){e.onTimeupdate(),e.onCacheUpdate()})),this.on(ve,(function(){e.onCacheUpdate()})),this.on(oe,(function(){e.onCacheUpdate(!0),e.onTimeupdate(!0),e._state.now=0})),this.on(me,(function(){e.onReset()})),this.on(He,(function(){e.onVideoResize()}))}},{key:"setConfig",value:function(e){var t=this,i=null;Object.keys(e).forEach((function(n){t.config[n]=e[n],"fragments"===n&&(i=e[n])})),i&&this._initInner(i,e)}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle.sliderBtnStyle,t=this.progressBtn;e&&("string"==typeof e?t.style.boxShadow=e:"object"===b(e)&&Object.keys(e).map((function(i){t.style[i]=e[i]})))}},{key:"triggerCallbacks",value:function(e,t,i){this.__dragCallBacks.length>0&&this.__dragCallBacks.map((function(n){if(n&&n.handler&&n.type===e)try{n.handler(t,i)}catch(o){}}))}},{key:"addCallBack",value:function(e,t){t&&"function"==typeof t&&this.__dragCallBacks.push({type:e,handler:t})}},{key:"removeCallBack",value:function(e,t){var i=this.__dragCallBacks,n=-1;i.map((function(i,o){i&&i.type===e&&i.handler===t&&(n=o)})),n>-1&&i.splice(n,1)}},{key:"unlock",value:function(){var e=this.player,t=this.pos;t.isEnter=!1,t.isLocked=!1,e.isMini||(this.unbind("mousemove",this.onMoveOnly),t.isDown?this.unbind("mouseleave",this.onMouseLeave):this.blur())}},{key:"bindDomEvents",value:function(){var e=this.player.config;this._mouseDownHandlerHook=this.hook("dragstart",this._mouseDownHandler),this._mouseUpHandlerHook=this.hook("dragend",this._mouseUpHandler),this._mouseMoveHandlerHook=this.hook("drag",this._mouseMoveHandler),"touch"!==this.domEventType&&"compatible"!==this.domEventType||this.root.addEventListener("touchstart",this.onMouseDown),"mouse"!==this.domEventType&&"compatible"!==this.domEventType||(this.bind("mousedown",this.onMouseDown),"mobile"!==e.isMobileSimulateMode&&this.bind("mouseenter",this.onMouseEnter),this.bind("mouseover",this.onMouseOver),this.bind("mouseout",this.onMouseOut),this.player.root.addEventListener("click",this.onBodyClick,!0))}},{key:"focus",value:function(){this.player.controls.pauseAutoHide(),z.addClass(this.root,"active")}},{key:"blur",value:function(){this._disableBlur||(this.player.controls.recoverAutoHide(),z.removeClass(this.root,"active"))}},{key:"disableBlur",value:function(){this._disableBlur=!0}},{key:"enableBlur",value:function(){this._disableBlur=!1}},{key:"updateWidth",value:function(e,t,i,n){var o=this.config,r=this.player;if(!o.isCloseClickSeek||0!==n){var s=t=t>=r.duration?r.duration-o.endedDiff:Number(t).toFixed(1);this.updatePercent(i),this.updateTime(e),(1!==n||o.isDraggingSeek&&"audio"!==r.config.mediaType)&&(this._state.now=s,this._state.direc=s>r.currentTime?0:1,r.seek(s))}}},{key:"computeTime",value:function(e,t){var i,n,o=this.player,r=this.root.getBoundingClientRect(),s=r.width,a=r.height,l=r.top,c=r.left,u=t;90===o.rotateDeg?(i=a,n=l):(i=s,n=c);var h=u-n,d=(h=h>i?i:h<0?0:h)/i;d=d<0?0:d>1?1:d;var f=parseInt(d*this.offsetDuration*1e3,10)/1e3;return{percent:d,currentTime:f,seekTime:z.getCurrentTimeByOffset(f,o.timeSegments),offset:h,width:i,left:n,e:e}}},{key:"updateTime",value:function(e){var t=this.player,i=this.duration;e>i?e=i:e<0&&(e=0);var n=t.plugins.time;n&&n.updateTime(e)}},{key:"resetSeekState",value:function(){this.isProgressMoving=!1;var e=this.player.plugins.time;e&&e.resetActive()}},{key:"updatePercent",value:function(e,t){if(this.isProgressMoving=!0,!this.config.disable){e=e>1?1:e<0?0:e,this.progressBtn.style.left="".concat(100*e,"%"),this.innerList.update({played:e*this.offsetDuration},this.offsetDuration);var i=this.player.plugins.miniprogress;i&&i.update({played:e*this.offsetDuration},this.offsetDuration)}}},{key:"onTimeupdate",value:function(e){var t=this.player,i=this._state,n=this.offsetDuration;if(!(t.isSeeking&&t.media.seeking||this.isProgressMoving)&&t.hasStart){if(i.now>-1){var o=parseInt(1e3*i.now,10)-parseInt(1e3*t.currentTime,10);if(0===i.direc&&o>300||1===i.direc&&o>-300)return void(i.now=-1);i.now=-1}var r=this.currentTime;r=z.adjustTimeByDuration(r,n,e),this.innerList.update({played:r},n),this.progressBtn.style.left="".concat(r/n*100,"%")}}},{key:"onCacheUpdate",value:function(e){var t=this.player,i=this.duration;if(t){var n=t.bufferedPoint.end;n=z.adjustTimeByDuration(n,i,e),this.innerList.update({cached:n},i)}}},{key:"onReset",value:function(){this.innerList.update({played:0,cached:0},0),this.progressBtn.style.left="0%"}},{key:"destroy",value:function(){var e=this.player;this.thumbnailPlugin=null,this.innerList.destroy(),this.innerList=null;var t=this.domEventType;"touch"!==t&&"compatible"!==t||(this.root.removeEventListener("touchstart",this.onMouseDown),this.root.removeEventListener("touchmove",this.onMouseMove),this.root.removeEventListener("touchend",this.onMouseUp),this.root.removeEventListener("touchcancel",this.onMouseUp)),"mouse"!==t&&"compatible"!==t||(this.unbind("mousedown",this.onMouseDown),this.unbind("mouseenter",this.onMouseEnter),this.unbind("mousemove",this.onMoveOnly),this.unbind("mouseleave",this.onMouseLeave),document.removeEventListener("mousemove",this.onMouseMove,!1),document.removeEventListener("mouseup",this.onMouseUp,!1),e.root.removeEventListener("click",this.onBodyClick,!0))}},{key:"render",value:function(){if(!this.config.disable&&!this.playerConfig.isLive){var e=this.player.controls?this.player.controls.config.mode:"";return'\n    <xg-progress class="xgplayer-progress '.concat("bottom"===e?"xgplayer-progress-bottom":"",'">\n      <xg-outer class="xgplayer-progress-outer">\n        <xg-progress-btn class="xgplayer-progress-btn"></xg-progress-btn>\n      </xg-outer>\n    </xg-progress>\n    ')}}}],[{key:"pluginName",get:function(){return"progress"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_CENTER,index:0,disable:!1,isDraggingSeek:!0,closeMoveSeek:!1,isPauseMoving:!1,isCloseClickSeek:!1,fragments:[{percent:1}],fragFocusClass:an.POINT,fragClass:"",fragAutoFocus:!1,miniMoveStep:5,miniStartStep:2,onMoveStart:function(){},onMoveEnd:function(){},endedDiff:.2}}},{key:"FRAGMENT_FOCUS_CLASS",get:function(){return an}}]),t}(),cn=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"_onMouseenter",(function(e){i.emit("icon_mouseenter",{pluginName:i.pluginName})})),x(I(i),"_onMouseLeave",(function(e){i.emit("icon_mouseleave",{pluginName:i.pluginName})})),i}return T(t,[{key:"afterCreate",value:function(){this.bind("mouseenter",this._onMouseenter),this.bind("mouseleave",this._onMouseLeave),this.config.disable&&this.disable()}},{key:"destroy",value:function(){this.unbind("mouseenter",this._onMouseenter),this.unbind("mouseleave",this._onMouseLeave)}}]),t}(),un=function(){S(t,cn);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"btnClick",(function(e){e.preventDefault(),e.stopPropagation();var t=I(i).player;return i.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t.paused,to:!t.paused}),t.ended?t.replay():t.paused?(t.play(),i.animate(!1)):(t.pause(),i.animate(!0)),!1})),i}return T(t,[{key:"afterCreate",value:function(){O(E(t.prototype),"afterCreate",this).call(this),this.config.disable||(this.initIcons(),this.bind(["touchend","click"],this.btnClick),this.listenEvents(),this.animate(!0))}},{key:"listenEvents",value:function(){var e=this,t=this.player;this.on([ie,re,se,me],(function(){e.animate(t.paused)}))}},{key:"registerIcons",value:function(){return{play:{icon:gi,class:"xg-icon-play"},pause:{icon:vi,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.play),this.appendChild(".xgplayer-icon",e.pause)}},{key:"animate",value:function(e){if(this.player){var t=this.i18nKeys,i=this.find(".xg-tips");e?(this.setAttr("data-state","pause"),i&&this.changeLangTextKey(i,t.PLAY_TIPS)):(this.setAttr("data-state","play"),i&&this.changeLangTextKey(i,t.PAUSE_TIPS))}}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this),this.unbind(["touchend","click"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-play">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(bi(this,"PLAY_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"play"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_LEFT,index:0,disable:!1}}}]),t}();function hn(){return(new DOMParser).parseFromString('<svg width="32px" height="40px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M11.2374369,14 L17.6187184,7.61871843 C17.9604272,7.27700968 17.9604272,6.72299032 17.6187184,6.38128157 C17.2770097,6.03957281 16.7229903,6.03957281 16.3812816,6.38128157 L9.38128157,13.3812816 C9.03957281,13.7229903 9.03957281,14.2770097 9.38128157,14.6187184 L16.3812816,21.6187184 C16.7229903,21.9604272 17.2770097,21.9604272 17.6187184,21.6187184 C17.9604272,21.2770097 17.9604272,20.7229903 17.6187184,20.3812816 L11.2374369,14 L11.2374369,14 Z" fill="#FFFFFF"></path>\n    </g>\n</svg>',"image/svg+xml").firstChild}var dn=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.onClick=function(t){t.preventDefault(),t.stopPropagation(),e.config.onClick(t)},this.bind(["click","touchend"],this.onClick)}},{key:"registerIcons",value:function(){return{screenBack:{icon:hn,class:"xg-fullscreen-back"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(this.root,e.screenBack)}},{key:"show",value:function(){z.addClass(this.root,"show")}},{key:"hide",value:function(){z.removeClass(this.root,"show")}},{key:"render",value:function(){return'<xg-icon class="xgplayer-back">\n    </xg-icon>'}}],[{key:"pluginName",get:function(){return"topbackicon"}},{key:"defaultConfig",get:function(){return{position:Tt.ROOT_TOP,index:0}}}]),t}();function fn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n',"image/svg+xml").firstChild}function pn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n',"image/svg+xml").firstChild}var gn=function(){S(t,cn);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"_onOrientationChange",(function(e){i.player.fullscreen&&i.config.rotateFullscreen&&(90===window.orientation||-90===window.orientation?i.player.setRotateDeg(0):i.player.setRotateDeg(90))})),i}return T(t,[{key:"afterCreate",value:function(){var e=this;O(E(t.prototype),"afterCreate",this).call(this);var i=this.config,n=this.playerConfig;if(!i.disable){i.target&&(this.playerConfig.fullscreenTarget=this.config.target);var o=z.getFullScreenEl();n.fullscreenTarget===o&&this.player.getFullscreen().catch((function(e){})),this.initIcons(),this.handleFullscreen=this.hook("fullscreenChange",this.toggleFullScreen,{pre:function(t){var i=e.player.fullscreen;e.emitUserAction(t,"switch_fullscreen",{prop:"fullscreen",from:i,to:!i})}}),this.bind(".xgplayer-fullscreen",["touchend","click"],this.handleFullscreen),this.on(De,(function(t){var i=e.find(".xg-tips");i&&e.changeLangTextKey(i,t?e.i18nKeys.EXITFULLSCREEN_TIPS:e.i18nKeys.FULLSCREEN_TIPS),e.animate(t)})),this.config.needBackIcon&&(this.topBackIcon=this.player.registerPlugin({plugin:dn,options:{config:{onClick:function(t){e.handleFullscreen(t)}}}})),"mobile"===J.device&&window.addEventListener("orientationchange",this._onOrientationChange)}}},{key:"registerIcons",value:function(){return{fullscreen:{icon:fn,class:"xg-get-fullscreen"},exitFullscreen:{icon:pn,class:"xg-exit-fullscreen"}}}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon","mobile"===J.device?"touchend":"click",this.handleFullscreen),"mobile"===J.device&&window.removeEventListener("orientationchange",this._onOrientationChange)}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.fullscreen),this.appendChild(".xgplayer-icon",e.exitFullscreen)}},{key:"toggleFullScreen",value:function(e){e&&(e.preventDefault(),e.stopPropagation());var t=this.player,i=this.config;!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen()?(t.fullscreen?t.exitCssFullscreen():t.getCssFullscreen(),this.animate(t.fullscreen)):i.rotateFullscreen?(t.fullscreen?t.exitRotateFullscreen():t.getRotateFullscreen(),this.animate(t.fullscreen)):i.switchCallback&&"function"==typeof i.switchCallback?i.switchCallback(t.fullscreen):t.fullscreen?(t.exitFullscreen(),i.useScreenOrientation&&this.unlockScreen()):(t.getFullscreen().catch((function(e){})),i.useScreenOrientation&&t.aspectRatio>1&&this.lockScreen(i.lockOrientationType))}},{key:"animate",value:function(e){e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.topBackIcon&&(e?(this.topBackIcon.show(),this.hide()):(this.topBackIcon.hide(),this.show()))}},{key:"render",value:function(){if(!this.config.disable){return'<xg-icon class="xgplayer-fullscreen">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(bi(this,"FULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}},{key:"lockScreen",value:function(e){try{screen.orientation.lock(e).catch((function(e){}))}catch(t){}}},{key:"unlockScreen",value:function(){try{screen.orientation.unlock().catch((function(e){}))}catch(e){}}}],[{key:"pluginName",get:function(){return"fullscreen"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:0,useCssFullscreen:!1,rotateFullscreen:!1,useScreenOrientation:!1,lockOrientationType:"landscape",switchCallback:null,target:null,disable:!1,needBackIcon:!1}}}]),t}(),vn=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),(n=e.call(this,i)).isActiving=!1,n}return T(t,[{key:"duration",get:function(){var e=this.player,t=e.offsetDuration,i=e.duration;return this.playerConfig.customDuration||t||i}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"afterCreate",value:function(){var e=this.player.controls.config.mode;this.mode="flex"===e?"flex":"normal",this.config.disable||("flex"===this.mode&&(this.createCenterTime(),this.root.style.display="none"),this.durationDom=this.find(".time-duration"),this.timeDom=this.find(".time-current"),this.listenEvents())}},{key:"listenEvents",value:function(){var e=this;this.on([de,le,ce],(function(t){"durationchange"===t.eventName&&(e.isActiving=!1),e.onTimeUpdate()})),this.on(oe,(function(){e.onTimeUpdate(!0)})),this.on(me,(function(){e.onReset()}))}},{key:"show",value:function(e){if("flex"===this.mode)return this.centerCurDom&&(this.centerCurDom.style.display="block"),void(this.centerDurDom&&(this.centerDurDom.style.display="block"));this.root.style.display="block"}},{key:"hide",value:function(){if("flex"===this.mode)return this.centerCurDom&&(this.centerCurDom.style.display="none"),void(this.centerDurDom&&(this.centerDurDom.style.display="none"));this.root.style.display="none"}},{key:"onTimeUpdate",value:function(e){var t=this.player,i=this.config,n=this.duration;if(!i.disable&&!this.isActiving&&t.hasStart){var o=this.currentTime+this.timeOffset;o=z.adjustTimeByDuration(o,n,e),"flex"===this.mode?(this.centerCurDom.innerHTML=this.minWidthTime(z.format(o)),n!==1/0&&n>0&&(this.centerDurDom.innerHTML=z.format(n))):(this.timeDom.innerHTML=this.minWidthTime(z.format(o)),n!==1/0&&n>0&&(this.durationDom.innerHTML=z.format(n)))}}},{key:"onReset",value:function(){"flex"===this.mode?(this.centerCurDom.innerHTML=this.minWidthTime(z.format(0)),this.centerDurDom.innerHTML=z.format(0)):(this.timeDom.innerHTML=this.minWidthTime(z.format(0)),this.durationDom.innerHTML=z.format(0))}},{key:"createCenterTime",value:function(){var e=this.player;if(e.controls&&e.controls.center){var t=e.controls.center;this.centerCurDom=z.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-left"),this.centerDurDom=z.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-right"),t.children.length>0?t.insertBefore(this.centerCurDom,t.children[0]):t.appendChild(this.centerCurDom),t.appendChild(this.centerDurDom)}}},{key:"afterPlayerInit",value:function(){var e=this.config;this.duration===1/0||this.playerConfig.isLive?(z.hide(this.durationDom),z.hide(this.timeDom),z.hide(this.find(".time-separator")),z.show(this.find(".time-live-tag"))):z.hide(this.find(".time-live-tag")),e.hide?this.hide():this.show()}},{key:"changeLiveState",value:function(e){e?(z.hide(this.durationDom),z.hide(this.timeDom),z.hide(this.find(".time-separator")),z.show(this.find(".time-live-tag"))):(z.hide(this.find(".time-live-tag")),z.show(this.find(".time-separator")),z.show(this.durationDom),z.show(this.timeDom))}},{key:"updateTime",value:function(e){this.isActiving=!0,!e&&0!==e||e>this.duration||("flex"!==this.mode?this.timeDom.innerHTML=this.minWidthTime(z.format(e)):this.centerCurDom.innerHTML=this.minWidthTime(z.format(e)))}},{key:"minWidthTime",value:function(e){return e.split(":").map((function(e){return'<span class="time-min-width">'.concat(e,"</span>")})).join(":")}},{key:"resetActive",value:function(){var e=this,t=this.player,i=function(){e.isActiving=!1};this.off(le,i),t.isSeeking&&t.media.seeking?this.once(le,i):this.isActiving=!1}},{key:"destroy",value:function(){var e=this.player.controls.center;this.centerCurDom&&e.removeChild(this.centerCurDom),this.centerCurDom=null,this.centerDurDom&&e.removeChild(this.centerDurDom),this.centerDurDom=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-time">\n    <span class="time-current">00:00</span>\n    <span class="time-separator">/</span>\n    <span class="time-duration">00:00</span>\n    <span class="time-live-tag">'.concat(this.i18n.LIVE_TIP,"</span>\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"time"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_LEFT,index:2,disable:!1}}}]),t}(),yn=function(){S(t,bt);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"_onDurationChange",(function(){i.updateSegments();var e=i.player,t=e.currentTime,n=e.timeSegments;if(i._checkIfEnabled(n)){var o=z.getIndexByTime(t,n),r=z.getOffsetCurrentTime(t,n,o);i.player.offsetCurrentTime=r,i.changeIndex(o,n)}})),x(I(i),"_onLoadedData",(function(){var e=i.player.timeSegments;if(i._checkIfEnabled(e)){var t=z.getOffsetCurrentTime(0,e);i.player.offsetCurrentTime=t,i.changeIndex(0,e),i.curPos.start>0&&(i.player.currentTime=i.curPos.start)}})),x(I(i),"_onTimeupdate",(function(){var e=i.player,t=e.currentTime,n=e.timeSegments;if(i._checkIfEnabled(n)){var o=n.length;i.lastCurrentTime=t;var r=z.getIndexByTime(t,n);r!==i.curIndex&&i.changeIndex(r,n);var s=z.getOffsetCurrentTime(t,n,r);if(i.player.offsetCurrentTime=s,i.curPos){var a=i.curPos,l=a.start,c=a.end;t<l?i.player.currentTime=l:t>c&&r>=o-1&&i.player.pause()}}})),x(I(i),"_onSeeking",(function(){var e=i.player,t=e.currentTime,n=e.timeSegments;if(i._checkIfEnabled(n))if(t<n[0].start)i.player.currentTime=n[0].start;else if(t>n[n.length-1].end)i.player.currentTime=n[n.length-1].end;else{var o=z.getIndexByTime(t,n);if(o>=0){var r=i.getSeekTime(t,i.lastCurrentTime,o,n);r>=0&&(i.player.currentTime=r)}}})),x(I(i),"_onPlay",(function(){var e=i.player,t=e.currentTime,n=e.timeSegments;i._checkIfEnabled(n)&&t>=n[n.length-1].end&&(i.player.currentTime=n[0].start)})),i}return T(t,[{key:"afterCreate",value:function(){this.curIndex=-1,this.curPos=null,this.lastCurrentTime=0,this.updateSegments(),this.on(de,this._onDurationChange),this.on(pe,this._onLoadedData),this.on(ce,this._onTimeupdate),this.on(ae,this._onSeeking),this.on(ie,this._onPlay)}},{key:"setConfig",value:function(e){var t=this;if(e){var i=Object.keys(e);i.length<1||(i.forEach((function(i){t.config[i]=e[i]})),this.updateSegments())}}},{key:"updateSegments",value:function(){var e=this.config,t=e.disable,i=e.segments,n=this.player;if(t||!i||0===i.length)n.timeSegments=[],n.offsetDuration=0,n.offsetCurrentTime=-1;else{var o=this.formatTimeSegments(i,n.duration);n.timeSegments=o,n.offsetDuration=o.length>0?o[o.length-1].duration:0}}},{key:"formatTimeSegments",value:function(e,t){var i=[];return e?(e.sort((function(e,t){return e.start-t.start})),e.forEach((function(e,n){var o={};if(o.start=e.start<0?0:e.start,o.end=t>0&&e.end>t?t:e.end,!(t>0&&o.start>t)){i.push(o);var r=o.end-o.start;if(0===n)o.offset=e.start,o.cTime=0,o.segDuration=r,o.duration=r;else{var s=i[n-1];o.offset=s.offset+(o.start-s.end),o.cTime=s.duration+s.cTime,o.segDuration=r,o.duration=s.duration+r}}})),i):[]}},{key:"getSeekTime",value:function(e,t,i,n){var o=-1,r=n[i],s=r.start,a=r.end;if(e>=s&&e<=a)return o;var l=e-t;if(l<0&&e<s){var c=t>s?t-s:0;return o=i-1>=0?n[i-1].end+l+c:0}return-1}},{key:"_checkIfEnabled",value:function(e){return!(!e||e.length<1)}},{key:"changeIndex",value:function(e,t){this.curIndex=e,e>=0&&t.length>0?this.curPos=t[e]:this.curPos=null}}],[{key:"pluginName",get:function(){return"TimeSegmentsControls"}},{key:"defaultConfig",get:function(){return{disable:!0,segments:[]}}}]),t}();function mn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function kn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Cn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n',"image/svg+xml").firstChild}var bn=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onBarMousedown",(function(e){var t=I(i).player,n=i.find(".xgplayer-bar");z.event(e);var o=n.getBoundingClientRect(),r=z.getEventPos(e,t.zoom),s=o.height-(r.clientY-o.top);if(r.h=s,r.barH=o.height,i.pos=r,!(s<-2))return i.updateVolumePos(s,e),document.addEventListener("mouseup",i.onBarMouseUp),i._d.isStart=!0,!1})),x(I(i),"onBarMouseMove",(function(e){var t=I(i)._d;if(t.isStart){var n=I(i),o=n.pos,r=n.player;e.preventDefault(),e.stopPropagation(),z.event(e);var s=z.getEventPos(e,r.zoom);t.isMoving=!0;var a=o.h-s.clientY+o.clientY;a>o.barH||i.updateVolumePos(a,e)}})),x(I(i),"onBarMouseUp",(function(e){z.event(e),document.removeEventListener("mouseup",i.onBarMouseUp);var t=I(i)._d;t.isStart=!1,t.isMoving=!1})),x(I(i),"onMouseenter",(function(e){i._d.isActive=!0,i.focus(),i.emit("icon_mouseenter",{pluginName:i.pluginName})})),x(I(i),"onMouseleave",(function(e){i._d.isActive=!1,i.unFocus(100,!1,e),i.emit("icon_mouseleave",{pluginName:i.pluginName})})),x(I(i),"onVolumeChange",(function(e){if(i.player){var t=i.player,n=t.muted,o=t.volume;i._d.isMoving||(i.find(".xgplayer-drag").style.height=n||0===o?"4px":"".concat(100*o,"%"),i.config.showValueLabel&&i.updateVolumeValue()),i.animate(n,o)}})),i}return T(t,[{key:"registerIcons",value:function(){return{volumeSmall:{icon:kn,class:"xg-volume-small"},volumeLarge:{icon:mn,class:"xg-volume"},volumeMuted:{icon:Cn,class:"xg-volume-mute"}}}},{key:"afterCreate",value:function(){var e=this;if(this._timerId=null,this._d={isStart:!1,isMoving:!1,isActive:!1},!this.config.disable){this.initIcons();var t=this.playerConfig,i=t.commonStyle,n=t.volume;i.volumeColor&&(this.find(".xgplayer-drag").style.backgroundColor=i.volumeColor),this.changeMutedHandler=this.hook("mutedChange",(function(t){e.changeMuted(t)}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this._onMouseenterHandler=this.hook("mouseenter",this.onMouseenter),this._onMouseleaveHandler=this.hook("mouseleave",this.onMouseleave),"mobile"!==J.device&&"mobile"!==this.playerConfig.isMobileSimulateMode&&(this.bind("mouseenter",this._onMouseenterHandler),this.bind(["blur","mouseleave"],this._onMouseleaveHandler),this.bind(".xgplayer-slider","mousedown",this.onBarMousedown),this.bind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.bind(".xgplayer-slider","mouseup",this.onBarMouseUp)),this.bind(".xgplayer-icon",["touchend","click"],this.changeMutedHandler),this.on(fe,this.onVolumeChange),this.once(pe,this.onVolumeChange),"Number"!==z.typeOf(n)&&(this.player.volume=this.config.default),this.onVolumeChange()}}},{key:"updateVolumePos",value:function(e,t){var i=this.player,n=this.find(".xgplayer-drag"),o=this.find(".xgplayer-bar");if(o&&n){var r=parseInt(e/o.getBoundingClientRect().height*1e3,10);n.style.height="".concat(e,"px");var s=Math.max(Math.min(r/1e3,1),0),a={volume:{from:i.volume,to:s}};i.muted&&(a.muted={from:!0,to:!1}),this.emitUserAction(t,"change_volume",{muted:i.muted,volume:i.volume,props:a}),i.volume=Math.max(Math.min(r/1e3,1),0),i.muted&&(i.muted=!1),this.config.showValueLabel&&this.updateVolumeValue()}}},{key:"updateVolumeValue",value:function(){var e=this.player,t=e.volume,i=e.muted,n=this.find(".xgplayer-value-label"),o=Math.max(Math.min(t,1),0);n.innerText=i?0:Math.round(100*o)}},{key:"focus",value:function(){this.player.focus({autoHide:!1}),this._timerId&&(z.clearTimeout(this,this._timerId),this._timerId=null),z.addClass(this.root,"slide-show")}},{key:"unFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2?arguments[2]:void 0,o=this._d,r=this.player;o.isActive||(this._timerId&&(z.clearTimeout(this,this._timerId),this._timerId=null),this._timerId=z.setTimeout(this,(function(){o.isActive||(i?r.blur():r.focus(),z.removeClass(e.root,"slide-show"),o.isStart&&e.onBarMouseUp(n)),e._timerId=null}),t))}},{key:"changeMuted",value:function(e){e&&e.stopPropagation();var t=this.player;this._d.isStart&&this.onBarMouseUp(e),this.emitUserAction(e,"change_muted",{muted:t.muted,volume:t.volume,props:{muted:{from:t.muted,to:!t.muted}}}),t.volume>0&&(t.muted=!t.muted),t.volume<.01&&(t.volume=this.config.miniVolume)}},{key:"animate",value:function(e,t){e||0===t?this.setAttr("data-state","mute"):t<.5&&this.icons.volumeSmall?this.setAttr("data-state","small"):this.setAttr("data-state","normal")}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.volumeSmall),this.appendChild(".xgplayer-icon",e.volumeLarge),this.appendChild(".xgplayer-icon",e.volumeMuted)}},{key:"destroy",value:function(){this._timerId&&(z.clearTimeout(this,this._timerId),this._timerId=null),this.unbind("mouseenter",this.onMouseenter),this.unbind(["blur","mouseleave"],this.onMouseleave),this.unbind(".xgplayer-slider","mousedown",this.onBarMousedown),this.unbind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.unbind(".xgplayer-slider","mouseup",this.onBarMouseUp),document.removeEventListener("mouseup",this.onBarMouseUp),this.unbind(".xgplayer-icon","mobile"===J.device?"touchend":"click",this.changeMutedHandler)}},{key:"render",value:function(){if(!this.config.disable){var e=this.config.default||this.player.volume,t=this.config.showValueLabel;return'\n    <xg-icon class="xgplayer-volume" data-state="normal">\n      <div class="xgplayer-icon">\n      </div>\n      <xg-slider class="xgplayer-slider">\n        '.concat(t?'<div class="xgplayer-value-label">'.concat(100*e,"</div>"):"",'\n        <div class="xgplayer-bar">\n          <xg-drag class="xgplayer-drag" style="height: ').concat(100*e,'%"></xg-drag>\n        </div>\n      </xg-slider>\n    </xg-icon>')}}}],[{key:"pluginName",get:function(){return"volume"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:1,disable:!1,showValueLabel:!1,default:.6,miniVolume:.2}}}]),t}();function _n(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="-4 -6 40 40" fill="none">\n  <g>\n    <path transform="scale(1.5 1.5)" d="M11.6665 9.16663H4.1665C2.78579 9.16663 1.6665 10.2859 1.6665 11.6666V15.8333C1.6665 17.214 2.78579 18.3333 4.1665 18.3333H11.6665C13.0472 18.3333 14.1665 17.214 14.1665 15.8333V11.6666C14.1665 10.2859 13.0472 9.16663 11.6665 9.16663Z" fill="white"/>\n    <path transform="scale(1.5 1.5)" fill-rule="evenodd" clip-rule="evenodd" d="M3.88148 4.06298C3.75371 4.21005 3.67667 4.40231 3.67749 4.61242C3.67847 4.87253 3.79852 5.10435 3.98581 5.25646L6.99111 8.05895C7.32771 8.37283 7.85502 8.35443 8.16891 8.01782C8.48279 7.68122 8.46437 7.15391 8.12778 6.84003L6.62061 5.43457L9.8198 5.4224C9.82848 5.42239 9.8372 5.42221 9.84591 5.4219C10.9714 5.38233 12.0885 5.6285 13.0931 6.13744C14.0976 6.64635 14.957 7.40148 15.5908 8.33234C16.2246 9.2632 16.6122 10.3394 16.7177 11.4606C16.823 12.5819 16.6427 13.7115 16.1934 14.7442C16.0098 15.1661 16.203 15.6571 16.6251 15.8408C17.0471 16.0243 17.5381 15.8311 17.7216 15.4091C18.2833 14.1183 18.5087 12.7063 18.3771 11.3047C18.2453 9.90318 17.7607 8.55792 16.9684 7.39433C16.1761 6.23073 15.1021 5.28683 13.8463 4.65065C12.5946 4.01651 11.203 3.70872 9.80072 3.75583L6.43415 3.76862L7.96326 2.12885C8.27715 1.79225 8.25872 1.26494 7.92213 0.951061C7.58553 0.63718 7.05822 0.655585 6.74433 0.99219L3.90268 4.0395C3.89545 4.04724 3.88841 4.05509 3.88154 4.06303L3.88148 4.06298Z" fill="white"/>\n  </g>\n  <defs>\n    <clipPath>\n      <rect width="40" height="40" fill="white"/>\n    </clipPath>\n  </defs>\n</svg>\n',"image/svg+xml").firstChild}var wn=function(){S(t,cn);var e=A(t);function t(i){var n;return _(this,t),(n=e.call(this,i)).rotateDeg=n.config.rotateDeg||0,n}return T(t,[{key:"afterCreate",value:function(){var e=this;if(!this.config.disable){O(E(t.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.rotate),this.onBtnClick=this.onBtnClick.bind(this),this.bind(".xgplayer-icon",["click","touchend"],this.onBtnClick),this.on(He,(function(){e.rotateDeg&&e.config.innerRotate&&z.setTimeout(e,(function(){e.updateRotateDeg(e.rotateDeg,e.config.innerRotate)}),100)}));var i=this.player.root;this.rootWidth=i.style.width||i.offsetWidth||i.clientWidth,this.rootHeight=i.style.height||i.offsetHeight||i.clientHeight,this.rotateDeg&&this.updateRotateDeg(this.rotateDeg,this.config.innerRotate)}}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon",["click","touchend"],this.onBtnClick)}},{key:"onBtnClick",value:function(e){e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"rotate"),this.rotate(this.config.clockwise,this.config.innerRotate,1)}},{key:"updateRotateDeg",value:function(e,t){if(e||(e=0),t)this.player.videoRotateDeg=e;else{var i=this.player,n=this.rootWidth,o=this.rootHeight,r=i.root,s=i.innerContainer,a=i.media,l=r.offsetWidth,c=s&&t?s.offsetHeight:r.offsetHeight,u=n,h=o,d=0,f=0;.75!==e&&.25!==e||(u="".concat(c,"px"),h="".concat(l,"px"),d=-(c-l)/2,f=-(l-c)/2);var p="translate(".concat(d,"px,").concat(f,"px) rotate(").concat(e,"turn)"),g={transformOrigin:"center center",transform:p,webKitTransform:p,height:h,width:u},v=t?a:r,y=t?i.getPlugin("poster"):null;Object.keys(g).map((function(e){v.style[e]=g[e],y&&y.root&&(y.root.style[e]=g[e])}))}}},{key:"rotate",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.player;this.rotateDeg||(this.rotateDeg=0);var o=e?1:-1;this.rotateDeg=(this.rotateDeg+1+.25*o*i)%1,this.updateRotateDeg(this.rotateDeg,t),n.emit(Ue,360*this.rotateDeg)}},{key:"registerIcons",value:function(){return{rotate:_n}}},{key:"render",value:function(){if(!this.config.disable)return'\n    <xg-icon class="xgplayer-rotate">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(bi(this,"ROTATE_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"rotate"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:6,innerRotate:!0,clockwise:!1,rotateDeg:0,disable:!1}}}]),t}();function Tn(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}function xn(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M9.4998 7.7C9.77595 7.7 9.9998 7.47614 9.9998 7.2V6.5C9.9998 6.22386 9.77595 6 9.4998 6H5.5402L5.52754 6.00016H5.5C5.22386 6.00016 5 6.22401 5 6.50016V10.4598C5 10.7359 5.22386 10.9598 5.5 10.9598H6.2C6.47614 10.9598 6.7 10.7359 6.7 10.4598V8.83005L8.76983 10.9386C8.96327 11.1357 9.27984 11.1386 9.47691 10.9451L9.97645 10.4548C10.1735 10.2613 10.1764 9.94476 9.983 9.7477L7.97289 7.7H9.4998Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}var Sn="picture-in-picture",En="inline",Pn="fullscreen",In=function(){S(t,cn);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"switchPIP",(function(e){if(!i.isPIPAvailable())return!1;e.stopPropagation&&e.stopPropagation(),i.isPip?(i.exitPIP(),i.emitUserAction(e,"change_pip",{props:"pip",from:!0,to:!1}),i.setAttr("data-state","normal")):4===i.player.media.readyState&&(i.requestPIP(),i.emitUserAction(e,"change_pip",{props:"pip",from:!1,to:!0}),i.setAttr("data-state","pip"))})),i}return T(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.pip&&(e.config.showIcon=e.player.config.pip)}},{key:"afterCreate",value:function(){var e=this;this.isPIPAvailable()&&(O(E(t.prototype),"afterCreate",this).call(this),this.pMode=En,this.initPipEvents(),this.config.showIcon&&this.initIcons(),this.once(xe,(function(){e.config.showIcon&&(z.removeClass(e.find(".xgplayer-icon"),"xg-icon-disable"),e.bind("click",e.switchPIP))})))}},{key:"registerIcons",value:function(){return{pipIcon:{icon:Tn,class:"xg-get-pip"},pipIconExit:{icon:xn,class:"xg-exit-pip"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.pipIcon),this.appendChild(".xgplayer-icon",e.pipIconExit)}},{key:"initPipEvents",value:function(){var e=this,i=this.player;this.leavePIPCallback=function(){var t=i.paused;z.setTimeout(e,(function(){!t&&i.mediaPlay()}),0),!t&&i.mediaPlay(),e.setAttr("data-state","normal"),e.pipWindow=null,i.emit(Be,!1)},this.enterPIPCallback=function(t){i.emit(Be,!0),null!=t&&t.pictureInPictureWindow&&(e.pipWindow=t.pictureInPictureWindow),e.setAttr("data-state","pip")},this.onWebkitpresentationmodechanged=function(t){var n=i.media.webkitPresentationMode;e.pMode===Pn&&n!==Pn&&i.onFullscreenChange(null,!1),e.pMode=n,n===Sn?e.enterPIPCallback(t):n===En&&e.leavePIPCallback(t)},i.media&&(i.media.addEventListener("enterpictureinpicture",this.enterPIPCallback),i.media.addEventListener("leavepictureinpicture",this.leavePIPCallback),t.checkWebkitSetPresentationMode(i.media)&&i.media.addEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged))}},{key:"copyStyleIntoPiPWindow",value:function(e){var t=D(document.styleSheets).map((function(t){try{return D(t.cssRules).map((function(e){return e.cssText})).join("")}catch(n){var i=document.createElement("link");i.rel="stylesheet",i.type=t.type,i.media=t.media,i.href=t.href,e.document.head.appendChild(i)}return""})).filter(Boolean).join("\n"),i=document.createElement("style");i.textContent=t,e.document.head.appendChild(i)}},{key:"requestPIP",value:function(){var e=this,i=this.player,n=this.playerConfig,o=this.config;if(this.isPIPAvailable()&&!this.isPip)try{var r=n.poster;if(r&&(i.media.poster="String"===z.typeOf(r)?r:r.poster),o.preferDocument&&this.isDocPIPAvailable()){var s={};if(o.width&&o.height)s.width=o.width,s.height=o.height;else{var a=i.root.getBoundingClientRect();s.width=a.width,s.height=a.height}documentPictureInPicture.requestWindow(s).then((function(t){var n=o.docPiPNode,r=o.docPiPStyle;e.enterPIPCallback();var s=n||i.root,a=s.parentElement,l=s.previousSibling,c=s.nextSibling;e.copyStyleIntoPiPWindow(t);var u=document.createElement("style");if(u.append("body{padding:0; margin:0;}"),r){var h="";"string"==typeof r?h=r:"function"==typeof r&&(h=r.call(o)),h&&u.append(h)}else s===i.root&&u.append("\n              .xgplayer{width: 100%!important; height: 100%!important;}\n            ");t.document.head.append(u),t.document.body.append(s),t.addEventListener("pagehide",(function(t){a&&(c?a.insertBefore(s,c):l?a.insertBefore(s,l.nextSibling):a.appendChild(s)),e.leavePIPCallback()}),{once:!0})}))}else t.checkWebkitSetPresentationMode(i.media)?i.media.webkitSetPresentationMode("picture-in-picture"):i.media.requestPictureInPicture();return!0}catch(l){return!1}}},{key:"exitPIP",value:function(){var e=this.player;try{var i;if(this.isPIPAvailable()&&this.isPip)this.isDocPIPAvailable()&&null!==(i=documentPictureInPicture)&&void 0!==i&&i.window?documentPictureInPicture.window.close():t.checkWebkitSetPresentationMode(e.media)?e.media.webkitSetPresentationMode("inline"):document.exitPictureInPicture();return!0}catch(n){return!1}}},{key:"isPip",get:function(){var e,t=this.player;return!(!this.isDocPIPAvailable()||null===(e=documentPictureInPicture)||void 0===e||!e.window)||document.pictureInPictureElement&&document.pictureInPictureElement===t.media||t.media.webkitPresentationMode===Sn}},{key:"isPIPAvailable",value:function(){var e=this.player.media;return"Boolean"===z.typeOf(document.pictureInPictureEnabled)&&document.pictureInPictureEnabled&&("Boolean"===z.typeOf(e.disablePictureInPicture)&&!e.disablePictureInPicture||e.webkitSupportsPresentationMode&&"Function"===z.typeOf(e.webkitSetPresentationMode))||this.isDocPIPAvailable()}},{key:"isDocPIPAvailable",value:function(){return"documentPictureInPicture"in window&&/^(https|file)/.test(location.protocol)}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this);var e=this.player;e.media.removeEventListener("enterpictureinpicture",this.enterPIPCallback),e.media.removeEventListener("leavepictureinpicture",this.leavePIPCallback),t.checkWebkitSetPresentationMode(e.media)&&e.media.removeEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged),this.exitPIP(),this.unbind("click",this.btnClick)}},{key:"render",value:function(){if(this.config.showIcon&&this.isPIPAvailable())return'<xg-icon class="xgplayer-pip">\n      <div class="xgplayer-icon xg-icon-disable">\n      </div>\n      '.concat(bi(this,"PIP",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"pip"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:6,showIcon:!1,preferDocument:!1,width:void 0,height:void 0,docPiPNode:void 0,docPiPStyle:void 0}}},{key:"checkWebkitSetPresentationMode",value:function(e){return"function"==typeof e.webkitSetPresentationMode}}]),t}();function Ln(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="40" viewBox="10 0 24 40">\n  <path transform="scale(0.038 0.028)" d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n</svg>\n',"image/svg+xml").firstChild}var An,On=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"playNext",(function(e){var t=I(n).player;e.preventDefault(),e.stopPropagation(),n.idx+1<n.config.urlList.length?(n.idx++,n.nextHandler(n.config.urlList[n.idx],n.idx),t.emit(Ve,n.idx+1)):(n.nextHandler(),t.emit(Ve))})),n.idx=-1,n}return T(t,[{key:"afterCreate",value:function(){this.config.urlList&&0!==this.config.urlList.length&&(this.appendChild(".xgplayer-icon",this.icons.playNext),this.initEvents())}},{key:"registerIcons",value:function(){return{playNext:Ln}}},{key:"initEvents",value:function(){this.nextHandler=this.hook("nextClick",this.changeSrc);var e="mobile"===J.device?"touchend":"click";this.bind(e,this.playNext),this.show()}},{key:"changeSrc",value:function(e){var t=this.player;e&&(t.pause(),t.currentTime=0,t.switchURL?t.switchURL(e):t.src=e,t.config.url=e,t.play())}},{key:"destroy",value:function(){this.unbind(["touchend","click"],this.playNext)}},{key:"render",value:function(){if(this.config.urlList&&0!==this.config.urlList.length)return'\n     <xg-icon class="xgplayer-playnext">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(bi(this,"PLAYNEXT_TIPS",this.playerConfig.isHideTips),"\n     </xg-icon>\n    ")}}],[{key:"pluginName",get:function(){return"playNext"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_LEFT,index:1,url:null,urlList:[]}}}]),t}(),Dn={exports:{}};var Rn=An?Dn.exports:(An=1,Dn.exports=function e(t,i,n){var o,r,s=window,a="application/octet-stream",l=n||a,c=t,u=!i&&!n&&c,h=document.createElement("a"),d=function(e){return String(e)},f=s.Blob||s.MozBlob||s.WebKitBlob||d,p=i||"download";if(f=f.call?f.bind(s):Blob,"true"===String(this)&&(l=(c=[c,l])[0],c=c[1]),u&&u.length<2048&&(p=u.split("/").pop().split("?")[0],h.href=u,-1!==h.href.indexOf(u))){var g=new XMLHttpRequest;return g.open("GET",u,!0),g.responseType="blob",g.onload=function(t){e(t.target.response,p,a)},setTimeout((function(){g.send()}),0),g}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(c)){if(!(c.length>2096103.424&&f!==d))return navigator.msSaveBlob?navigator.msSaveBlob(k(c),p):C(c);l=(c=k(c)).type||a}else if(/([\x80-\xff])/.test(c)){for(var v=0,y=new Uint8Array(c.length),m=y.length;v<m;++v)y[v]=c.charCodeAt(v);c=new f([y],{type:l})}function k(e){for(var t=e.split(/[:;,]/),i=t[1],n=("base64"==t[2]?atob:decodeURIComponent)(t.pop()),o=n.length,r=0,s=new Uint8Array(o);r<o;++r)s[r]=n.charCodeAt(r);return new f([s],{type:i})}function C(e,t){if("download"in h)return h.href=e,h.setAttribute("download",p),h.className="download-js-link",h.innerHTML="downloading...",h.style.display="none",document.body.appendChild(h),setTimeout((function(){h.click(),document.body.removeChild(h),!0===t&&setTimeout((function(){s.URL.revokeObjectURL(h.href)}),250)}),66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,a)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var i=document.createElement("iframe");document.body.appendChild(i),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,a)),i.src=e,setTimeout((function(){document.body.removeChild(i)}),333)}if(o=c instanceof f?c:new f([c],{type:l}),navigator.msSaveBlob)return navigator.msSaveBlob(o,p);if(s.URL)C(s.URL.createObjectURL(o),!0);else{if("string"==typeof o||o.constructor===d)try{return C("data:"+l+";base64,"+s.btoa(o))}catch(b){return C("data:"+l+","+encodeURIComponent(o))}(r=new FileReader).onload=function(e){C(this.result)},r.readAsDataURL(o)}return!0});const Mn=l(Rn);function Nn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">\n  <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n    <g transform="translate(-488.000000, -340.000000)" fill="#FFFFFF">\n      <g id="Group-2">\n        <g id="volme_big-copy" transform="translate(488.000000, 340.000000)">\n          <rect id="Rectangle-18" x="11" y="4" width="2" height="12" rx="1"></rect>\n          <rect id="Rectangle-2" x="3" y="18" width="18" height="2" rx="1"></rect>\n          <rect id="Rectangle-2" transform="translate(4.000000, 17.500000) rotate(90.000000) translate(-4.000000, -17.500000) " x="1.5" y="16.5" width="5" height="2" rx="1"></rect><rect id="Rectangle-2-Copy-3" transform="translate(20.000000, 17.500000) rotate(90.000000) translate(-20.000000, -17.500000) " x="17.5" y="16.5" width="5" height="2" rx="1"></rect>\n          <path d="M9.48791171,8.26502656 L9.48791171,14.2650266 C9.48791171,14.8173113 9.04019646,15.2650266 8.48791171,15.2650266 C7.93562696,15.2650266 7.48791171,14.8173113 7.48791171,14.2650266 L7.48791171,7.26502656 C7.48791171,6.71274181 7.93562696,6.26502656 8.48791171,6.26502656 L15.4879117,6.26502656 C16.0401965,6.26502656 16.4879117,6.71274181 16.4879117,7.26502656 C16.4879117,7.81731131 16.0401965,8.26502656 15.4879117,8.26502656 L9.48791171,8.26502656 Z" id="Combined-Shape" transform="translate(11.987912, 10.765027) scale(1, -1) rotate(45.000000) translate(-11.987912, -10.765027) "></path>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n',"image/svg+xml").firstChild}var Fn=function(){S(t,cn);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"download",(function(e){if(!n.isLock){n.emitUserAction(e,"download");var t=n.playerConfig.url,i="";"String"===z.typeOf(t)?i=t:"Array"===z.typeOf(t)&&t.length>0&&(i=t[0].src);var o=n.getAbsoluteURL(i);Mn(o),n.isLock=!0,n.timer=window.setTimeout((function(){n.isLock=!1,window.clearTimeout(n.timer),n.timer=null}),300)}})),n.timer=null,n.isLock=!1,n}return T(t,[{key:"afterCreate",value:function(){O(E(t.prototype),"afterCreate",this).call(this),this.config.disable||(this.appendChild(".xgplayer-icon",this.icons.download),this._handler=this.hook("click",this.download,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(["click","touchend"],this._handler))}},{key:"registerIcons",value:function(){return{download:Nn}}},{key:"getAbsoluteURL",value:function(e){if(!e.match(/^https?:\/\//)){var t=document.createElement("div");t.innerHTML='<a href="'.concat(e,'">x</a>'),e=t.firstChild.href}return e}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.download),window.clearTimeout(this.timer),this.timer=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-download">\n   <div class="xgplayer-icon">\n   </div>\n   '.concat(bi(this,"DOWNLOAD_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"download"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:3,disable:!0}}}]),t}(),Hn=function(){S(t,cn);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.screenShot&&(e.config.disable=!e.player.config.screenShot)}},{key:"afterCreate",value:function(){O(E(t.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.screenshotIcon);var e=this.config;this.initSize=function(t){e.fitVideo&&(e.width=t.vWidth,e.height=t.vHeight)},this.once(He,this.initSize)}},{key:"onPluginsReady",value:function(){this.show(),this.onClickBtn=this.onClickBtn.bind(this),this.bind(["click","touchend"],this.onClickBtn)}},{key:"saveScreenShot",value:function(e,t){var i,n=document.createElement("a");n.href=e,n.download=t;try{"undefined"!=typeof MouseEvent?i=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window}):(i=document.createEvent("MouseEvents")).initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null)}catch(o){}i&&n.dispatchEvent(i)}},{key:"createCanvas",value:function(e,t){var i=document.createElement("canvas"),n=i.getContext("2d");this.canvasCtx=n,this.canvas=i,i.width=e||this.config.width,i.height=t||this.config.height,n.imageSmoothingEnabled=!0,n.imageSmoothingEnabled&&(n.imageSmoothingQuality="high")}},{key:"onClickBtn",value:function(e){var t=this;e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"shot");var i=this.config;this.shot(i.width,i.height).then((function(e){t.emit(je,e),i.saveImg&&t.saveScreenShot(e,i.name+i.format)}))}},{key:"shot",value:function(e,t){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{quality:.92,type:"image/png"},o=this.config,r=this.player,s=n.quality||o.quality,a=n.type||o.type;return new Promise((function(n,l){var c,u=null;if(r.media.canvas)u=r.media.canvas;else{i.canvas?(i.canvas.width=e||o.width,i.canvas.height=t||o.height):i.createCanvas(e,t),u=i.canvas,c=i.canvasCtx;var h,d,f,p,g=r.media.videoWidth/r.media.videoHeight,v=u.width/u.height,y=r.media.videoWidth,m=r.media.videoHeight;g>v?(f=u.width,p=u.width/g,h=0,d=Math.round((u.height-p)/2)):g===v?(f=u.width,p=u.height,h=0,d=0):g<v&&(f=u.height*g,p=u.height,h=Math.round((u.width-f)/2),d=0),c.drawImage(r.media,0,0,y,m,h,d,f,p)}var k=u.toDataURL(a,s).replace(a,"image/octet-stream");n(k=k.replace(/^data:image\/[^;]+/,"data:application/octet-stream"))}))}},{key:"registerIcons",value:function(){return{screenshotIcon:null}}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.onClickBtn),this.off(He,this.initSize)}},{key:"render",value:function(){if(!this.config.disable){var e=this.icons.screenshotIcon?"xgplayer-icon":"xgplayer-icon btn-text",t="SCREENSHOT";return'\n      <xg-icon class="xgplayer-shot">\n      <div class="'.concat(e,'">\n      ').concat(this.icons.screenshotIcon?"":'<span lang-key="'.concat(this.i18nKeys[t],'">').concat(this.i18n[t],"</span>"),"\n      </div>\n    </xg-icon>")}}}],[{key:"pluginName",get:function(){return"screenShot"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:5,quality:.92,type:"image/png",format:".png",width:600,height:337,saveImg:!0,fitVideo:!0,disable:!1,name:"screenshot"}}}]),t}(),Bn=function(){function e(t){_(this,e),this.config=t.config,this.parent=t.root,this.root=z.createDom("ul","",{},"xg-options-list xg-list-slide-scroll ".concat(this.config.className)),t.root.appendChild(this.root);var i=this.config.maxHeight;i&&this.setStyle({maxHeight:i}),this.onItemClick=this.onItemClick.bind(this),this.renderItemList();var n="touch"===this.config.domEventType?"touchend":"click";this._delegates=It.delegate.call(this,this.root,"li",n,this.onItemClick)}return T(e,[{key:"renderItemList",value:function(e){var t=this,i=this.config,n=this.root;e?i.data=e:e=i.data,i.style&&Object.keys(i.style).map((function(e){n.style[e]=i[e]})),e.length>0&&(this.attrKeys=Object.keys(e[0])),this.root.innerHTML="",e.map((function(e,i){var n=e.selected?"option-item selected":"option-item";e["data-index"]=i,t.root.appendChild(z.createDom("li","<span>".concat(e.showText,"</span>"),e,n))}))}},{key:"onItemClick",value:function(e){e.delegateTarget||(e.delegateTarget=e.target);var t=e.delegateTarget;if(t&&z.hasClass(t,"selected"))return!1;var i="function"==typeof this.config.onItemClick?this.config.onItemClick:null,n=this.root.querySelector(".selected");z.addClass(t,"selected"),n&&z.removeClass(n,"selected"),i(e,{from:n?this.getAttrObj(n,this.attrKeys):null,to:this.getAttrObj(t,this.attrKeys)})}},{key:"getAttrObj",value:function(e,t){if(!e||!t)return{};var i={};t.map((function(t){i[t]=e.getAttribute(t)}));var n=e.getAttribute("data-index");return n&&(i.index=Number(n)),i}},{key:"show",value:function(){z.removeClass(this.root,"hide"),z.addClass(this.root,"active")}},{key:"hide",value:function(){z.removeClass(this.root,"active"),z.addClass(this.root,"hide")}},{key:"setStyle",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.root.style[i]=e[i]}))}},{key:"destroy",value:function(){this._delegates&&(this._delegates.map((function(e){e.destroy&&e.destroy()})),this._delegates=null),this.root.innerHTML=null,this.parent.removeChild(this.root),this.root=null}}]),e}(),Un="side",jn="middle",Vn="default",Wn="click",Gn="hover";var zn="mobile"===J.device,Kn=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"onEnter",(function(e){e.stopPropagation(),n.emit("icon_mouseenter",{pluginName:n.pluginName}),n.switchActiveState(e)})),x(I(n),"switchActiveState",(function(e){e.stopPropagation(),n.config.toggleMode===Wn?n.toggle(!n.isActive):n.toggle(!0)})),x(I(n),"onLeave",(function(e){e.stopPropagation(),n.emit("icon_mouseleave",{pluginName:n.pluginName}),n.config.listType!==Un&&n.isActive&&n.toggle(!1)})),x(I(n),"onListEnter",(function(e){n.enterType=2})),x(I(n),"onListLeave",(function(e){n.enterType=0,n.isActive&&n.toggle(!1)})),n.isIcons=!1,n.isActive=!1,n.curValue=null,n.curIndex=0,n}return T(t,[{key:"updateLang",value:function(e){this.renderItemList(this.config.list,this.curIndex)}},{key:"afterCreate",value:function(){var e=this,t=this.config;this.initIcons(),(zn=zn||"touch"===this.domEventType)&&"mobile"===J.device&&t.listType===Vn&&(t.listType=Un),t.hidePortrait&&z.addClass(this.root,"portrait"),this.on([He,De],(function(){e._resizeList()})),this.once(he,(function(){t.list&&t.list.length>0&&(e.renderItemList(t.list),e.show())})),zn&&this.on(ke,(function(){e.isActive&&(e.optionsList&&e.optionsList.hide(),e.isActive=!1)})),zn?(t.toggleMode=Wn,this.activeEvent="touchend"):this.activeEvent=t.toggleMode===Wn?"click":"mouseenter",t.toggleMode===Wn?this.bind(this.activeEvent,this.switchActiveState):(this.bind(this.activeEvent,this.onEnter),this.bind("mouseleave",this.onLeave)),this.isIcons&&this.bind("click",this.onIconClick)}},{key:"initIcons",value:function(){var e=this,t=this.icons,i=Object.keys(t),n=!1;i.length>0&&(i.forEach((function(i){e.appendChild(".xgplayer-icon",t[i]),!n&&(n=t[i])})),this.isIcons=n),n||(this.appendChild(".xgplayer-icon",z.createDom("span","",{},"icon-text")),z.addClass(this.find(".xgplayer-icon"),"btn-text"))}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||z.addClass(this.root,"show")}},{key:"hide",value:function(){z.removeClass(this.root,"show")}},{key:"getTextByLang",value:function(e,t,i){if(void 0===e)return"";var n=this.config.list;!i&&(i=this.player.lang),t=!t||z.isUndefined(e[t])?"text":t,"number"==typeof e&&(e=n[e]);try{return"object"===b(e[t])?e[t][i]||e[t].en:e[t]}catch(o){return""}}},{key:"toggle",value:function(e){if(e!==this.isActive&&!this.config.disable){var t=this.player.controls,i=this.config.listType;e?(i===Un?t.blur():t.focus(),this.optionsList&&this.optionsList.show()):(i===Un?t.focus():t.focusAwhile(),this.optionsList&&this.optionsList.hide()),this.isActive=e}}},{key:"onItemClick",value:function(e,t){e.stopPropagation();var i=this.config,n=i.listType,o=i.list;this.curIndex=t.to.index,this.curItem=o[this.curIndex],this.changeCurrentText(),(this.config.isItemClickHide||zn||n===Un)&&this.toggle(!1)}},{key:"onIconClick",value:function(e){}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,t=e[this.curIndex<e.length?this.curIndex:0];t&&(this.find(".icon-text").innerHTML=this.getTextByLang(t,"iconText"))}}},{key:"renderItemList",value:function(e,t){var i=this,n=this.config,o=this.optionsList,r=this.player;if("number"==typeof t&&(this.curIndex=t,this.curItem=n.list[t]),o)return o.renderItemList(e),void this.changeCurrentText();var s,a,l={config:{data:e||[],className:(s=n.listType,a=n.position,s===Un?a===Tt.CONTROLS_LEFT?"xg-side-list xg-left-side":"xg-side-list xg-right-side":""),onItemClick:function(e,t){i.onItemClick(e,t)},domEventType:zn?"touch":"mouse"},root:n.listType===Un?r.innerContainer||r.root:this.root};if(this.config.isShowIcon){var c=this.player.root.getBoundingClientRect().height,u=n.listType===jn?c-50:c;u&&n.heightLimit&&(l.config.maxHeight="".concat(u,"px")),this.optionsList=new Bn(l),this.changeCurrentText(),this.show()}this._resizeList()}},{key:"_resizeList",value:function(){if(this.config.heightLimit){var e=this.player.root.getBoundingClientRect().height,t=this.config.listType===jn?e-50:e;this.optionsList&&this.optionsList.setStyle({maxHeight:"".concat(t,"px")})}}},{key:"destroy",value:function(){this.config.toggleMode===Wn?this.unbind(this.activeEvent,this.switchActiveState):(this.unbind(this.activeEvent,this.onEnter),this.unbind("mouseleave",this.onLeave)),this.isIcons&&this.unbind("click",this.onIconClick),this.optionsList&&(this.optionsList.destroy(),this.optionsList=null)}},{key:"render",value:function(){if(this.config.isShowIcon)return'<xg-icon class="xg-options-icon '.concat(this.config.className||"",'">\n    <div class="xgplayer-icon">\n    </div>\n   </xg-icon>')}}],[{key:"pluginName",get:function(){return"optionsIcon"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:100,list:[],listType:"default",listStyle:{},hidePortrait:!0,isShowIcon:!1,isItemClickHide:!0,toggleMode:Gn,heightLimit:!0}}}]),t}(),Yn=function(){S(t,Kn);var e=A(t);function t(i){var n;return _(this,t),(n=e.call(this,i)).curTime=0,n.isPaused=!0,n}return T(t,[{key:"beforeCreate",value:function(e){var t=e.config.list;Array.isArray(t)&&t.length>0&&(e.config.list=t.map((function(e){return!e.text&&e.name&&(e.text=e.name),e.text||(e.text=e.definition),e})))}},{key:"afterCreate",value:function(){var e=this;O(E(t.prototype),"afterCreate",this).call(this),this.on("resourceReady",(function(t){e.changeDefinitionList(t)})),this.on(Ne,(function(t){e.renderItemList(e.config.list,t.to)})),this.player.definitionList.length<2&&this.hide()}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||z.addClass(this.root,"show")}},{key:"initDefinition",value:function(){var e=this.config,t=e.list,i=e.defaultDefinition;if(t.length>0){var n=null;t.map((function(e){e.definition===i&&(n=e)})),n||(n=t[0]),this.changeDefinition(n)}}},{key:"renderItemList",value:function(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.list||[],n=arguments.length>1?arguments[1]:void 0,o=n&&n.definition?n.definition:this.config.defaultDefinition;n&&i.forEach((function(e){e.selected=!1}));var r=0,s=i.map((function(t,i){var n=C(C({},t),{},{showText:e.getTextByLang(t)||t.definition,selected:!1});return(t.selected||t.definition&&t.definition==o)&&(n.selected=!0,r=i),n}));O(E(t.prototype),"renderItemList",this).call(this,s,r)}},{key:"changeDefinitionList",value:function(e){Array.isArray(e)&&(this.config.list=e.map((function(e){return!e.text&&e.name&&(e.text=e.name),e.text||(e.text=e.definition),e})),this.renderItemList(),this.config.list.length<2?this.hide():this.show())}},{key:"changeDefinition",value:function(e,t){this.player.changeDefinition(e,t)}},{key:"onItemClick",value:function(e,i){var n=this.player.definitionList;O(E(t.prototype),"onItemClick",this).apply(this,arguments),this.emitUserAction(e,"change_definition",{from:i.from,to:i.to});for(var o=0;o<n.length;o++)i.to&&n[o].definition===i.to.definition&&(i.to.url=n[o].url),i.from&&n[o].definition===i.from.definition&&(i.from.url=n[o].url);this.player.changeDefinition(i.to,i.from)}}],[{key:"pluginName",get:function(){return"definition"}},{key:"defaultConfig",get:function(){return C(C({},Kn.defaultConfig),{},{position:Tt.CONTROLS_RIGHT,index:3,list:[],defaultDefinition:"",disable:!1,hidePortrait:!1,className:"xgplayer-definition",isShowIcon:!0})}}]),t}(),Xn=function(){S(t,Kn);var e=A(t);function t(i){var n;return _(this,t),(n=e.call(this,i)).curRate=1,n}return T(t,[{key:"beforeCreate",value:function(e){var t=e.player.config.playbackRate,i=t?Array.isArray(t)?t:e.config.list:[];Array.isArray(i)&&(e.config.list=i.map((function(e){return"number"==typeof e?e={rate:e,text:"".concat(e,"x")}:!e.text&&e.rate&&(e.text="".concat(e.rate,"x")),e})))}},{key:"afterCreate",value:function(){var e=this;O(E(t.prototype),"afterCreate",this).call(this),this.on(ge,(function(){e.curValue!==e.player.playbackRate&&e.renderItemList()})),this.renderItemList()}},{key:"show",value:function(e){this.config.list&&0!==this.config.list.length&&O(E(t.prototype),"show",this).call(this)}},{key:"onItemClick",value:function(e,i){O(E(t.prototype),"onItemClick",this).call(this,e,i);var n=e.delegateTarget,o=Number(n.getAttribute("rate"));if(!o||o===this.curValue)return!1;var r={playbackRate:{from:this.player.playbackRate,to:o}};this.emitUserAction(e,"change_rate",{props:r}),this.curValue=o,this.player.playbackRate=o}},{key:"renderItemList",value:function(){var e=this,i=this.player.playbackRate||1;this.curValue=i;var n=-1,o=this.config.list.map((function(t,o){var r={rate:t.rate};return r.rate===i&&(r.selected=!0,n=o),r.showText=e.getTextByLang(t),r}));O(E(t.prototype),"renderItemList",this).call(this,o,n)}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,t=e[this.curIndex<e.length?this.curIndex:0],i="";i=!t||this.curIndex<0?"".concat(this.player.playbackRate,"x"):this.getTextByLang(t,"iconText"),this.find(".icon-text").innerHTML=i}}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this)}}],[{key:"pluginName",get:function(){return"playbackRate"}},{key:"defaultConfig",get:function(){return C(C({},Kn.defaultConfig),{},{position:Tt.CONTROLS_RIGHT,index:4,list:[2,1.5,1,.75,.5],className:"xgplayer-playbackrate",isShowIcon:!0,hidePortrait:!1})}}]),t}();function qn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" class=\'path_full\' d="M9,10v1a.9.9,0,0,1-1,1,.9.9,0,0,1-1-1V9A.9.9,0,0,1,8,8h2a.9.9,0,0,1,1,1,.9.9,0,0,1-1,1Zm6,4V13a1,1,0,0,1,2,0v2a.9.9,0,0,1-1,1H14a1,1,0,0,1,0-2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Zn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" d="M9,10V9a.9.9,0,0,1,1-1,.9.9,0,0,1,1,1v2a.9.9,0,0,1-1,1H8a.9.9,0,0,1-1-1,.9.9,0,0,1,1-1Zm6,4v1a1,1,0,0,1-2,0V13a.9.9,0,0,1,1-1h2a1,1,0,0,1,0,2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Jn=function(){S(t,cn);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.cssFullscreen&&(e.config.disable=!e.player.config.cssFullscreen)}},{key:"afterCreate",value:function(){var e=this;O(E(t.prototype),"afterCreate",this).call(this),this.config.disable||(this.config.target&&(this.playerConfig.fullscreenTarget=this.config.target),this.initIcons(),this.on(Re,(function(t){e.animate(t)})),this.btnClick=this.btnClick.bind(this),this.handleCssFullscreen=this.hook("cssFullscreen_change",this.btnClick,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(["click","touchend"],this.handleCssFullscreen))}},{key:"initIcons",value:function(){var e=this.icons,t=this.find(".xgplayer-icon");t.appendChild(e.cssFullscreen),t.appendChild(e.exitCssFullscreen)}},{key:"btnClick",value:function(e){e.preventDefault(),e.stopPropagation();var t=this.player.isCssfullScreen;this.emitUserAction(e,"switch_cssfullscreen",{cssfullscreen:t}),t?this.player.exitCssFullscreen():this.player.getCssFullscreen()}},{key:"animate",value:function(e){this.root&&(e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.switchTips(e))}},{key:"switchTips",value:function(e){var t=this.i18nKeys,i=this.find(".xg-tips");i&&this.changeLangTextKey(i,e?t.EXITCSSFULLSCREEN_TIPS:t.CSSFULLSCREEN_TIPS)}},{key:"registerIcons",value:function(){return{cssFullscreen:{icon:qn,class:"xg-get-cssfull"},exitCssFullscreen:{icon:Zn,class:"xg-exit-cssfull"}}}},{key:"destroy",value:function(){O(E(t.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return"<xg-icon class='xgplayer-cssfullscreen'>\n    <div class=\"xgplayer-icon\">\n    </div>\n    ".concat(bi(this,"CSSFULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"cssFullscreen"}},{key:"defaultConfig",get:function(){return{position:Tt.CONTROLS_RIGHT,index:1,disable:!1,target:null}}}]),t}(),$n=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"afterCreate",value:function(){var e=this;this.clickHandler=this.hook("errorRetry",this.errorRetry,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.onError=this.hook("showError",this.handleError),this.bind(".xgplayer-error-refresh","click",this.clickHandler),this.on(se,(function(t){e.onError(t)}))}},{key:"errorRetry",value:function(e){this.emitUserAction(e,"error_retry",{}),this.player.retry()}},{key:"handleError",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.player,i=e.errorType,n=t.errorNote?this.i18n[t.errorNote]:"";if(!n)switch(i){case"decoder":n=this.i18n.MEDIA_ERR_DECODE;break;case"network":n=this.i18n.MEDIA_ERR_NETWORK;break;default:n=this.i18n.MEDIA_ERR_SRC_NOT_SUPPORTED}this.find(".xgplayer-error-text").innerHTML=n,this.find(".xgplayer-error-tips").innerHTML="".concat(this.i18n.REFRESH_TIPS,'<span class="xgplayer-error-refresh">').concat(this.i18n.REFRESH,"</span>")}},{key:"destroy",value:function(){this.unbind(".xgplayer-error-refresh","click",this.clickHandler)}},{key:"render",value:function(){return'<xg-error class="xgplayer-error">\n      <div class="xgplayer-errornote">\n       <span class="xgplayer-error-text"></span>\n       <span class="xgplayer-error-tips"><em class="xgplayer-error-refresh"></em></span>\n      </div>\n    </xg-error>'}}],[{key:"pluginName",get:function(){return"error"}}]),t}(),Qn=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"afterCreate",value:function(){var e=this;this.intervalId=0,this.customConfig=null,this.bind(".highlight",["click","touchend"],(function(t){(e.config.onClick||e.customOnClick)&&(t.preventDefault(),t.stopPropagation(),e.customOnClick?e.customOnClick(t):e.config.onClick(t))})),this.player.showPrompt=function(){e.showPrompt.apply(e,arguments)},this.player.hidePrompt=function(){e.hide()}}},{key:"setStyle",value:function(e){var t=this;Object.keys(e).map((function(i){t.root.style[i]=e[i]}))}},{key:"showPrompt",value:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){this.customOnClick=n;var o=this.config.interval;if(this.intervalId&&(clearTimeout(this.intervalId),this.intervalId=null),z.addClass(this.root,"show"),"arrow"===i.mode&&z.addClass(this.root,"arrow"),this.find(".xgplayer-prompt-detail").innerHTML="string"==typeof e?e:"".concat(e.text||"")+"".concat(e.highlight?'<i class="highlight">'.concat(e.highlight,"</i>"):""),i.style&&this.setStyle(i.style),"boolean"==typeof i.autoHide?i.autoHide:this.config.autoHide){var r=i.interval||o;this.intervalId=setTimeout((function(){t.hide()}),r)}}}},{key:"hide",value:function(){z.removeClass(this.root,"show"),z.removeClass(this.root,"arrow"),this.root.removeAttribute("style"),this.customOnClick=null}},{key:"render",value:function(){return'<xg-prompt class="xgplayer-prompt '.concat(Dt.CONTROLS_FOLLOW,'">\n    <span class="xgplayer-prompt-detail"></span>\n    </xg-prompt>')}}],[{key:"pluginName",get:function(){return"prompt"}},{key:"defaultConfig",get:function(){return{interval:3e3,style:{},mode:"arrow",autoHide:!0,detail:{text:"",highlight:""},onClick:function(){}}}}]),t}(),eo={time:0,text:"",id:1,duration:1,color:"#fff",style:{},width:6,height:6};function to(e){Object.keys(eo).map((function(t){void 0===e[t]&&(e[t]=eo[t])}))}var io={_updateDotDom:function(e,t){if(t){var i=this.calcuPosition(e.time,e.duration),n=e.style||{};n.left="".concat(i.left,"%"),n.width="".concat(i.width,"%"),t.setAttribute("data-text",e.text),t.setAttribute("data-time",e.time),i.isMini?z.addClass(t,"mini"):z.removeClass(t,"mini"),Object.keys(n).map((function(e){t.style[e]=n[e]}))}},initDots:function(){var e=this;this._ispots.map((function(t){e.createDot(t,!1)})),this.ispotsInit=!0},createDot:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.player.plugins.progress;if(i&&(t&&(to(e),this._ispots.push(e)),this.ispotsInit||!t)){var n=this.calcuPosition(e.time,e.duration),o=e.style||{};o.left="".concat(n.left,"%"),o.width="".concat(n.width,"%");var r="xgspot_".concat(e.id," xgplayer-spot");n.isMini&&(r+=" mini");var s=e.template?'<div class="xgplayer-spot-pop">'.concat(e.template,"</div>"):"",a=z.createDom("xg-spot",s,{"data-text":e.text,"data-time":e.time,"data-id":e.id},r);Object.keys(o).map((function(e){a.style[e]=o[e]})),i.outer&&i.outer.appendChild(a),this.positionDot(a,e.id)}},findDot:function(e){if(this.player.plugins.progress){var t=this._ispots.filter((function(t,i){return t.id===e}));return t.length>0?t[0]:null}},updateDot:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.player.plugins.progress;if(i){var n=this.findDot(e.id);if(n&&Object.keys(e).map((function(t){n[t]=e[t]})),this.ispotsInit){var o=i.find('xg-spot[data-id="'.concat(e.id,'"]'));o&&(this._updateDotDom(e,o),t&&this.showDot(e.id))}}},deleteDot:function(e){var t=this._ispots,i=this.player.plugins.progress;if(i){for(var n=[],o=0;o<t.length;o++)t[o].id===e&&n.push(o);for(var r=n.length-1;r>=0;r--)if(t.splice(n[r],1),this.ispotsInit){var s=i.find('xg-spot[data-id="'.concat(e,'"]'));s&&s.parentElement.removeChild(s)}}},deleteAllDots:function(){var e=this.player.plugins.progress;if(e)if(this.ispotsInit){for(var t=e.root.getElementsByTagName("xg-spot"),i=t.length-1;i>=0;i--)e.outer.removeChild(t[i]);this._ispots=[]}else this._ispots=[]},updateAllDots:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=this.player.plugins.progress;if(i)if(this.ispotsInit){this._ispots=[];var n=i.root.getElementsByTagName("xg-spot"),o=n.length;if(o>t.length)for(var r=o-1;r>t.length-1;r--)i.outer.removeChild(n[r]);t.forEach((function(t,i){i<o?(n[i].setAttribute("data-id","".concat(t.id)),e._ispots.push(t),e.updateDot(t)):e.createDot(t)}))}else this._ispots=t},positionDots:function(){var e=this,t=this._ispots,i=this.playerSize,n=this.player.sizeInfo,o=this.player.plugins.progress;o&&n.width!==i.width&&(i.width=n.width,i.left=n.left,t.forEach((function(t){var i=o.find('xg-spot[data-id="'.concat(t.id,'"]'));i&&e.positionDot(i,t.id)})))},positionDot:function(e,t){var i=z.findDom(e,".xgplayer-spot-pop");if(i){var n=this.playerSize,o=e.getBoundingClientRect(),r=i.getBoundingClientRect(),s=o.left-n.left,a=n.width-s-o.width/2;if(s<r.width/2||n.width<r.width){var l=r.width/2-s;i.style.left="".concat(l,"px")}else if(a<r.width/2){var c=a-r.width/2+o.width/2;i.style.left="".concat(c,"px")}else i.style.left="50%"}},updateDuration:function(){var e=this,t=this.player.plugins.progress;t&&this._ispots.forEach((function(i){var n=t.find('xg-spot[data-id="'.concat(i.id,'"]'));e._updateDotDom(i,n)}))},getAllDotsDom:function(){var e=this.player.plugins.progress;return e?e.root.getElementsByTagName("xg-spot"):[]},getDotDom:function(e){var t=this.player.plugins.progress;if(t)return t.find('xg-spot[data-id="'.concat(e,'"]'))}};var no={dragmove:"onProgressMove",dragstart:"onProgressDragStart",dragend:"onProgressDragEnd",click:"onProgressClick",mouseover:"onProgressMouseOver",mouseenter:"onProgressMouseOver"},oo=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),x(I(n=e.call(this,i)),"onMousemove",(function(e){n.config.disable||(z.hasClass(e.target,"xg-spot-content")&&n.config.isHideThumbnailHover?n.player.plugins.progress.onMouseLeave(e):(n._state.f||z.hasClass(e.target,"xg-spot-content"))&&(z.event(e),e.stopPropagation()))})),x(I(n),"onMousedown",(function(e){n.config.disable||(n._state.f||z.hasClass(e.target,"xg-spot-content"))&&(z.event(e),e.stopPropagation())})),x(I(n),"onMouseup",(function(e){if(n.isDrag){var t=n.player.plugins.progress;t&&t.pos&&(t.onMouseUp(e),!t.pos.isEnter&&t.onMouseLeave(e))}})),x(I(n),"onDotMouseLeave",(function(e){if(!n.config.disable){n._curDot.removeEventListener("mouseleave",n.onDotMouseLeave),n.blurDot(e.target),n._curDot=null;var t=n.player.plugins.progress;t&&t.enableBlur(),n.show()}})),x(I(n),"onProgressMouseOver",(function(e,t){if(!n.config.disable&&z.hasClass(t.target,"xgplayer-spot")&&!n._curDot){n._curDot=t.target,n.focusDot(t.target),n._curDot.children.length>0&&n.hide();var i=n.player.plugins.progress;i&&i.disableBlur(),n._curDot.addEventListener("mouseleave",n.onDotMouseLeave)}})),n._ispots=[],n.videoPreview=null,n.videothumbnail=null,n.thumbnail=null,n.timeStr="",n._state={now:0,f:!1},n}return T(t,[{key:"beforeCreate",value:function(e){var t=e.player.plugins.progress;t&&(e.root=t.root)}},{key:"afterCreate",value:function(){var e=this;this._curDot=null,this.handlerSpotClick=this.hook("spotClick",(function(t,i){i.seekTime&&e.player.seek(i.seekTime)})),this.transformTimeHook=this.hook("transformTime",(function(t){e.setTimeContent(z.format(t))})),function(e){var t=e.config,i=e.player;Object.keys(io).map((function(t){e[t]=io[t].bind(e)}));var n=i.config.progressDot||t.ispots||[];e._ispots=n.map((function(e){return to(e),e})),e.ispotsInit=!1,e.playerSize={left:i.sizeInfo.left,width:i.sizeInfo.width},e.on(de,(function(){e.ispotsInit?e.updateDuration():e.initDots()})),e.on(He,(function(){e.positionDots()}))}(this),this.on(de,(function(){e.show()})),this.config.disable&&this.disable(),this.extTextRoot=this.find(".xg-spot-ext-text")}},{key:"setConfig",value:function(e){var t=this;e&&Object.keys(e).map((function(i){t.config[i]=e[i]}))}},{key:"onPluginsReady",value:function(){this.player.plugins.progress&&(this.previewLine=this.find(".xg-spot-line"),this.timePoint=this.find(".xgplayer-progress-point"),this.timeText=this.find(".xg-spot-time"),this.tipText=this.find(".spot-inner-text"),this._hasThumnail=!1,this.registerThumbnail(),this.bindEvents())}},{key:"bindEvents",value:function(){var e=this,t=this.player.plugins.progress;if(t&&(Object.keys(no).map((function(i){e[no[i]]=e[no[i]].bind(e),t.addCallBack(i,e[no[i]])})),"mobile"!==J.device)){this.bind(".xg-spot-info","mousemove",this.onMousemove),this.bind(".xg-spot-info","mousedown",this.onMousedown),this.bind(".xg-spot-info","mouseup",this.onMouseup);var i=this.hook("previewClick",(function(){}));this.handlerPreviewClick=function(t){t.stopPropagation(),i(parseInt(1e3*e._state.now,10)/1e3,t)},this.bind(".xg-spot-content","mouseup",this.handlerPreviewClick)}}},{key:"onProgressMove",value:function(e,t){!this.config.disable&&this.player.duration&&this.updatePosition(e.offset,e.width,e.currentTime,e.e)}},{key:"onProgressDragStart",value:function(e){!this.config.disable&&this.player.duration&&(this.isDrag=!0,this.videoPreview&&z.addClass(this.videoPreview,"show"))}},{key:"onProgressDragEnd",value:function(e){!this.config.disable&&this.player.duration&&(this.isDrag=!1,this.videoPreview&&z.removeClass(this.videoPreview,"show"))}},{key:"onProgressClick",value:function(e,t){this.config.disable||z.hasClass(t.target,"xgplayer-spot")&&(t.stopPropagation(),t.preventDefault(),["time","id","text"].map((function(i){e[i]=t.target.getAttribute("data-".concat(i))})),e.time&&(e.time=Number(e.time)),this.handlerSpotClick(t,e))}},{key:"updateLinePos",value:function(e,t){var i=this.root,n=this.previewLine,o=this.player,r=this.config,s="flex"===o.controls.mode,a=i.getBoundingClientRect().width;if(a||!this._hasThumnail){var l,c=e-(a=this._hasThumnail&&a<r.width?r.width:a)/2;c<0&&!s?(c=0,l=e-a/2):c>t-a&&!s?(l=c-(t-a),c=t-a):l=0,void 0!==l&&(n.style.transform="translateX(".concat(l.toFixed(2),"px)")),i.style.transform="translateX(".concat(c.toFixed(2),"px) translateZ(0)")}}},{key:"updateTimeText",value:function(e){var t=this.timeText,i=this.timePoint;t.innerHTML=e,!this.thumbnail&&(i.innerHTML=e)}},{key:"updatePosition",value:function(e,t,i,n){var o=this.root,r=this.config,s=this._state;if(o){s.now=i,this.transformTimeHook(i);var a=this.timeStr;n&&n.target&&z.hasClass(n.target,"xgplayer-spot")?(this.showTips(n.target.getAttribute("data-text"),!1,a),this.focusDot(n.target),s.f=!0,r.isFocusDots&&s.f&&(s.now=parseInt(n.target.getAttribute("data-time"),10))):r.defaultText?(s.f=!1,this.showTips(r.defaultText,!0,a)):(s.f=!1,this.hideTips("")),this.updateTimeText(a),this.updateThumbnails(s.now),this.updateLinePos(e,t)}}},{key:"setTimeContent",value:function(e){this.timeStr=e}},{key:"updateThumbnails",value:function(e){var t=this.player,i=this.videoPreview,n=this.config,o=t.plugins.thumbnail;if(o&&o.usable){this.thumbnail&&o.update(this.thumbnail,e,n.width,n.height);var r=i&&i.getBoundingClientRect();this.videothumbnail&&o.update(this.videothumbnail,e,r.width,r.height)}}},{key:"registerThumbnail",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("mobile"!==J.device){var t=this.player,i=this.config,n=t.getPlugin("thumbnail");if(n&&n.setConfig(e),n&&n.usable&&i.isShowThumbnail){z.removeClass(this.root,"short-line no-thumbnail"),"short"===i.mode&&z.addClass(this.root,"short-line"),this._hasThumnail=!0;var o=this.find(".xg-spot-thumbnail");this.thumbnail=n.createThumbnail(o,"progress-thumbnail"),i.isShowCoverPreview&&(this.videoPreview=z.createDom("xg-video-preview","",{},"xgvideo-preview"),t.root.appendChild(this.videoPreview),this.videothumbnail=n.createThumbnail(this.videoPreview,"xgvideo-thumbnail")),this.updateThumbnails(0)}else z.addClass(this.root,"short-line no-thumbnail")}}},{key:"calcuPosition",value:function(e,t){var i=this.player.plugins.progress,n=this.player,o=i.root.getBoundingClientRect().width,r=n.duration/o*6;return e+t>n.duration&&(t=n.duration-e),n.duration,n.duration,{left:e/n.duration*100,width:t/n.duration*100,isMini:t<r}}},{key:"showDot",value:function(e){var t=this.findDot(e);if(t){var i=this.root.getBoundingClientRect().width,n=t.time/this.player.duration*i;this.updatePosition(n,i,t.time)}}},{key:"focusDot",value:function(e,t){e&&(t||(t=e.getAttribute("data-id")),z.addClass(e,"active"),this._activeDotId=t)}},{key:"blurDot",value:function(e){if(!e){var t=this._activeDotId;e=this.getDotDom(t)}e&&(z.removeClass(e,"active"),this._activeDotId=null)}},{key:"showTips",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";z.addClass(this.root,"no-timepoint"),e&&(z.addClass(this.find(".xg-spot-content"),"show-text"),t&&"production"===this.config.mode?(z.addClass(this.root,"product"),this.tipText.textContent=e):(z.removeClass(this.root,"product"),this.tipText.textContent=this._hasThumnail?e:"".concat(i," ").concat(e)))}},{key:"hideTips",value:function(){z.removeClass(this.root,"no-timepoint"),this.tipText.textContent="",z.removeClass(this.find(".xg-spot-content"),"show-text"),z.removeClass(this.root,"product")}},{key:"hide",value:function(){z.addClass(this.root,"hide")}},{key:"show",value:function(e){z.removeClass(this.root,"hide")}},{key:"enable",value:function(){var e=this.config,t=this.playerConfig;this.config.disable=!1,this.show(),!this.thumbnail&&e.isShowThumbnail&&this.registerThumbnail(t.thumbnail||{})}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){var e=this,t=this.player.plugins.progress;t&&Object.keys(no).map((function(i){t.removeCallBack(i,e[no[i]])})),this.videothumbnail=null,this.thumbnail=null,this.videoPreview&&this.player.root.removeChild(this.videoPreview),this.unbind(".xg-spot-info","mousemove",this.onMousemove),this.unbind(".xg-spot-info","mousedown",this.onMousedown),this.unbind(".xg-spot-info","mouseup",this.onMouseup),this.unbind(".xg-spot-content","mouseup",this.handlerPreviewClick)}},{key:"render",value:function(){return"mobile"===J.device||"mobile"===this.playerConfig.isMobileSimulateMode?"":'<div class="xg-spot-info hide '.concat("short"===this.config.mode?"short-line":"",'">\n      <div class="xg-spot-content">\n        <div class="xg-spot-thumbnail">\n          <span class="xg-spot-time"></span>\n        </div>\n        <div class="xg-spot-text"><span class="spot-inner-text"></span></div>\n      </div>\n      <div class="xgplayer-progress-point">00:00</div>\n      <div class="xg-spot-ext-text"></div>\n      <div class="xg-spot-line"></div>\n    </div>')}}],[{key:"pluginName",get:function(){return"progresspreview"}},{key:"defaultConfig",get:function(){return{index:1,miniWidth:6,ispots:[],defaultText:"",isFocusDots:!0,isHideThumbnailHover:!0,isShowThumbnail:!0,isShowCoverPreview:!1,mode:"",disable:!1,width:160,height:90}}}]),t}(),ro=function(){S(t,It);var e=A(t);function t(i){var n;return _(this,t),(n=e.call(this,i)).ratio=1,n.interval=null,n._preloadMark={},n}return T(t,[{key:"afterCreate",value:function(){var e=this;this.usable&&this.initThumbnail(),this.on([de],(function(){var t=e.config,i=t.pic_num,n=t.interval;e.usable&&(e.interval=n>0?n:Math.round(1e3*e.player.duration/i)/1e3)}))}},{key:"setConfig",value:function(e){var t=this;if(e){var i=Object.keys(e);i.length<1||(i.forEach((function(i){t.config[i]=e[i]})),this.usable&&this.initThumbnail())}}},{key:"usable",get:function(){var e=this.config,t=e.urls,i=e.pic_num;return t&&t.length>0&&i>0}},{key:"initThumbnail",value:function(){var e=this.config,t=e.width,i=e.height,n=e.pic_num,o=e.interval;this.ratio=t/i*100,this.interval=o||Math.round(this.player.duration/n),this._preloadMark={}}},{key:"getUrlByIndex",value:function(e){return e>=0&&e<this.config.urls.length?this.config.urls[e]:""}},{key:"preload",value:function(e){var t=this;if(!this._preloadMark[e]){var i=this.config.urls,n=i.length,o=[];e>0&&o.push(e-1),o.push(e),e>0&&e<n-1&&o.push(e+1),o.map((function(e){!t._preloadMark[e]&&e>=0&&e<n&&(t._preloadMark[e]=1,z.preloadImg(i[e],(function(){t._preloadMark[e]=2})))}))}}},{key:"getPosition",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=this.config,o=n.pic_num,r=n.row,s=n.col,a=n.width,l=n.height;this.interval=Math.round(this.player.duration/o);var c=Math.ceil(e/this.interval),u=(c=c>o?o:c)<r*s?0:Math.ceil(c/(r*s))-1,h=c-u*(s*r),d=h>0?Math.ceil(h/s)-1:0,f=h>0?h-d*s-1:0,p=0,g=0;t&&i?t/i<a/l?p=(g=i)*(a/l):g=(p=t)/(a/l):i?t||(p=(g=i||l)*(a/l)):g=(p=t||a)/(a/l);var v=this.getUrlByIndex(u);return{urlIndex:u,rowIndex:d,colIndex:f,url:v,height:g,width:p,style:{backgroundImage:"url(".concat(v,")"),backgroundSize:"".concat(p*s,"px auto"),backgroundPosition:"-".concat(f*p,"px -").concat(d*g,"px"),width:"".concat(p,"px"),height:"".concat(g,"px")}}}},{key:"update",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",r=this.config,s=r.pic_num,a=r.urls;if(!(s<=0)&&a&&0!==a.length){var l=this.getPosition(t,i,n);this.preload(l.urlIndex),Object.keys(l.style).map((function(t){e.style[t]=l.style[t]})),Object.keys(o).map((function(t){e.style[t]=o[t]}))}}},{key:"changeConfig",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.setConfig(e)}},{key:"createThumbnail",value:function(e,t){var i=z.createDom("xg-thumbnail","",{},"thumbnail ".concat(t));return e&&e.appendChild(i),i}}],[{key:"pluginName",get:function(){return"thumbnail"}},{key:"defaultConfig",get:function(){return{isShow:!1,urls:[],pic_num:0,col:0,row:0,height:90,width:160,scale:1,className:"",hidePortrait:!1}}}]),t}();function so(e){return e?"background:".concat(e,";"):""}var ao=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onTimeupdate",(function(){var e=i.player.ended,t=I(i).offsetDuration,n=i.currentTime;n=z.adjustTimeByDuration(n,t,e),i.update({played:n},t)})),i}return T(t,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i}},{key:"afterCreate",value:function(){var e=this;this.root&&(this.on(ce,this.onTimeupdate),this.on(me,(function(){e.reset()})))}},{key:"reset",value:function(){this.update({played:0,cached:0},0)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;t&&this.root&&(e.cached&&(this.find("xg-mini-progress-cache").style.width="".concat(e.cached/t*100,"%")),e.played&&(this.find("xg-mini-progress-played").style.width="".concat(e.played/t*100,"%")))}},{key:"render",value:function(){var e=this.playerConfig,t=e.commonStyle;if(e.miniprogress){var i=this.config,n=i.mode,o=i.height,r={cached:so(t.cachedColor),played:so(t.playedColor),progress:so(t.progressColor),height:o>0&&2!==o?"height: ".concat(o,"px;"):""};return'<xg-mini-progress class="xg-mini-progress '.concat("show"===n?"xg-mini-progress-show":"",'" style="').concat(r.progress," ").concat(r.height,'">\n    <xg-mini-progress-cache class="xg-mini-progress-cache" style="').concat(r.cached,'"></xg-mini-progress-cache>\n    <xg-mini-progress-played class="xg-mini-progress-played" style="').concat(r.played,'"></xg-mini-progress-played>\n    </xg-mini-progress>')}}}],[{key:"pluginName",get:function(){return"MiniProgress"}},{key:"defaultConfig",get:function(){return{mode:"auto",height:2}}}]),t}(),lo="realtime",co="firstframe",uo="poster";var ho=null,fo=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onLoadedData",(function(e){i.player&&(i._frameCount=i.config.startFrameCount,i.stop(),i.renderOnTimeupdate(e),i.off(ce,i.renderOnTimeupdate),i.on(ce,i.renderOnTimeupdate))})),x(I(i),"onVisibilitychange",(function(e){"visible"===document.visibilityState?i._checkIfCanStart()&&i.start():"hidden"===document.visibilityState&&i.stop()})),x(I(i),"renderOnTimeupdate",(function(e){if(i._frameCount>0)i.renderOnce(),i._frameCount--;else{i._isLoaded=!0,i.off(ce,i.renderOnTimeupdate);var t=i.config.startInterval;!i.player.paused&&i._checkIfCanStart()&&i.start(0,t)}})),x(I(i),"start",(function(e,t){var n=i.player.video,o=function(){try{return parseInt(window.performance.now(),10)}catch(e){return(new Date).getTime()}}(),r=i.checkVideoIsSupport(n);r&&i.canvasCtx&&(t||(t=i.interval),i.stop(),n.videoWidth&&n.videoHeight&&(i.videoPI=n.videoHeight>0?parseInt(n.videoWidth/n.videoHeight*100,10):0,(i.config.mode===lo||o-i.preTime>=t)&&(n&&n.videoWidth&&i.update(r,i.videoPI),i.preTime=o)),i.frameId="timer"===i._loopType?z.setTimeout(I(i),i.start,t):z.requestAnimationFrame(i.start))})),x(I(i),"stop",(function(){i.frameId&&("timer"===i._loopType?z.clearTimeout(I(i),i.frameId):z.cancelAnimationFrame(i.frameId),i.frameId=null)})),i}return T(t,[{key:"afterCreate",value:function(){var e=this;!0===this.playerConfig.dynamicBg&&(this.config.disable=!1),t.isSupport||(this.config.disable=!0);var i=this.config,n=i.disable,o=i.mode,r=i.frameRate;n||(this._pos={width:0,height:0,rwidth:0,rheight:0,x:0,y:0,pi:0},this.isStart=!1,this._isLoaded=!1,this.videoPI=0,this.preTime=0,this.interval=parseInt(1e3/r,10),this.canvas=null,this.canvasCtx=null,this._frameCount=0,this._loopType=this.config.mode!==lo&&this.interval>=1e3?"timer":"animation",this.once(xe,(function(){e.player&&(e.init(),e.renderByPoster(),e.player.paused||e.start())})),o!==uo&&(o!==co&&(this.on(me,(function(){e.stop()})),this.on(ie,(function(){var t=e.config.startInterval;e._checkIfCanStart()&&e.start(0,t)})),this.on(re,(function(){e.stop()}))),this.on(pe,this.onLoadedData),this.on(ye,(function(){e._isLoaded=!1,e.stop()})),document.addEventListener("visibilitychange",this.onVisibilitychange)))}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){"root"===i&&e[i]!==t.config[i]?t.reRender(e[i]):"frameRate"===i?t.interval=parseInt(1e3/e[i],10):"disable"===i&&e[i]&&t.stop(),t.config[i]=e[i]}))}},{key:"init",value:function(e){var i=this.player,n=this.config;this.canvasFilter=t.supportCanvasFilter();try{var o=e||n.root;o||(o=n.isInnerRender&&i.innerContainer||i.root),o.insertAdjacentHTML("afterbegin",'<div class="xgplayer-dynamic-bg" data-index="'.concat(n.index,'"><canvas>\n        </canvas><xgmask></xgmask></div>')),this.root=o.children[0],this.canvas=this.find("canvas"),this.canvasFilter||(this.canvas.style.filter=n.filter,this.canvas.style.webkitFilter=n.filter),this.mask=this.find("xgmask"),n.addMask&&(this.mask.style.background=n.maskBg),this.canvasCtx=this.canvas.getContext("2d")}catch(r){W.logError("plugin:DynamicBg",r)}}},{key:"reRender",value:function(e){if(this.config.disable||this.root){this.stop();var t=this.root?this.root.parentElement:null;if(t!==e&&t.removeChild(this.root),e){this.init(e),this.renderOnce();var i=this.config.startInterval;this._checkIfCanStart()&&this.start(0,i)}else this.root=null}}},{key:"checkVideoIsSupport",value:function(e){if(!e)return null;var t=e&&e instanceof window.HTMLVideoElement?e:e.canvas?e.canvas:e.flyVideo?e.flyVideo:null;if(t&&("safari"!==J.browser||!z.isMSE(t)))return t;var i=t?t.tagName.toLowerCase():"";return"canvas"===i||"img"===i?t:null}},{key:"renderByPoster",value:function(){var e=this.playerConfig.poster;if(e){var t="String"===z.typeOf(e)?e:"String"===z.typeOf(e.poster)?e.poster:null;this.updateImg(t)}}},{key:"_checkIfCanStart",value:function(){var e=this.config.mode;return this._isLoaded&&!this.player.paused&&e!==co&&e!==uo}},{key:"renderOnce",value:function(){var e=this.player.video;if(e.videoWidth&&e.videoHeight){this.videoPI=parseInt(e.videoWidth/e.videoHeight*100,10);var t=this.checkVideoIsSupport(e);t&&this.update(t,this.videoPI)}}},{key:"updateImg",value:function(e){var t=this;if(e){var i=this.canvas.getBoundingClientRect(),n=i.width,o=i.height,r=new window.Image;r.onload=function(){if(t.canvas&&!t.frameId&&!t.isStart){t.canvas.height=o,t.canvas.width=n;var e=parseInt(n/o*100,10);t.update(r,e),r=null}},r.src=e}}},{key:"update",value:function(e,t){if(this.canvas&&this.canvasCtx&&t)try{var i=this._pos,n=this.config,o=this.canvas.getBoundingClientRect(),r=o.width,s=o.height;if(r!==i.width||s!==i.height||i.pi!==t){var a=parseInt(r/s*100,10);i.pi=t,i.width!==r&&(i.width=this.canvas.width=r),i.height!==s&&(i.height=this.canvas.height=s);var l=s,c=r;a<t?c=parseInt(s*t/100,10):a>t&&(l=parseInt(100*r/t,10)),i.rwidth=c*n.multiple,i.rheight=l*n.multiple,i.x=(r-i.rwidth)/2,i.y=(s-i.rheight)/2}this.canvasFilter&&(this.canvasCtx.filter=n.filter),this.canvasCtx.drawImage(e,i.x,i.y,i.rwidth,i.rheight)}catch(u){W.logError("plugin:DynamicBg",u)}}},{key:"destroy",value:function(){this.stop(),document.removeEventListener("visibilitychange",this.onVisibilitychange),this.canvasCtx=null,this.canvas=null}},{key:"render",value:function(){return""}}],[{key:"pluginName",get:function(){return"dynamicBg"}},{key:"defaultConfig",get:function(){return{isInnerRender:!1,disable:!0,index:-1,mode:"framerate",frameRate:10,filter:"blur(50px)",startFrameCount:2,startInterval:0,addMask:!0,multiple:1.2,maskBg:"rgba(0,0,0,0.7)"}}},{key:"isSupport",get:function(){return"boolean"==typeof ho?ho:ho=function(){try{return!!document.createElement("canvas").getContext}catch(e){return!1}}()}},{key:"supportCanvasFilter",value:function(){return!("safari"===J.browser||"firefox"===J.browser)}}]),t}(),po="info",go=Ze,vo=function(){S(t,bt);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"_recordUserActions",(function(e){var t=i._getTime(),n=Object.assign({},e,{msg:e.msg||e.action});i._stats[po].push(C(C({type:"userAction"},t),{},{payload:n}))})),x(I(i),"_onReset",(function(){i.reset()})),x(I(i),"_recordInfo",(function(e){i.info(e)})),x(I(i),"_downloadStats",(function(){var e=i.getStats(),t=new Blob([JSON.stringify(e)],{type:"application/json"}),n=window.URL.createObjectURL(t),o=document.createElement("a");o.style.display="none",o.href=n,o.download="player.txt",o.disabled=!1,o.click()})),i}return T(t,[{key:"_getTime",value:function(){return{timestamp:Date.now(),timeFormat:(new Date).toISOString()}}},{key:"afterCreate",value:function(){this.reset(),this.on(ze,this._recordUserActions),this.on(go.STATS_INFO,this._recordInfo),this.on(go.STATS_DOWNLOAD,this._downloadStats),this.on(go.STATS_RESET,this._onReset)}},{key:"destroy",value:function(){this.offAll()}},{key:"downloadStats",value:function(){this._downloadStats()}},{key:"info",value:function(e){e.profile?this._infoProfile(e):this._info(e)}},{key:"_info",value:function(e){var t=this._getTime();this._stats[po].push(C(C({},t),{},{payload:e}))}},{key:"_infoProfile",value:function(e){if(e&&e.startMs){var t=C({cat:"function",dur:Date.now()-e.startMs,name:e.name||e.msg,ph:"X",pid:0,tid:0,ts:e.startMs,profile:!0},e);this._info(t)}}},{key:"reset",value:function(){var e;this._stats=(x(e={},po,[]),x(e,"media",{}),e)}},{key:"getStats",value:function(){for(var e=this.player.media,t=[],i=0;i<e.buffered.length;i++)t.push({start:e.buffered.start(i),end:e.buffered.end(i)});var n={currentTime:e.currentTime,readyState:e.readyState,buffered:t,paused:e.paused,ended:e.ended};return this._stats.media=n,{raw:this._stats,timestat:this._getTimeStats(),profile:this._getProfile()}}},{key:"_getTimeStats",value:function(){return this._stats[po].map((function(e){var t=e.payload.data,i="";try{t instanceof Error?i=t.msg:void 0!==t&&(i=JSON.stringify(t))}catch(n){}return"[".concat(e.timeFormat,"] : ").concat(e.payload.msg," ").concat(i," ")}))}},{key:"_getProfile",value:function(){var e={traceEvents:[]};return this._stats[po].forEach((function(t){t.payload.profile&&e.traceEvents.push(t.payload)})),e}}],[{key:"pluginName",get:function(){return"stats"}},{key:"defaultConfig",get:function(){return{}}}]),t}(),yo=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onGapJump",(function(){var e=I(i),n=e.player,o=e.config;if(n.media.readyState!==HTMLMediaElement.HAVE_NOTHING){if(n.media.seeking){if(!i.seekingEventReceived)return}else i.seekingEventReceived=!1;if(!n.media.paused||0===n.media.currentTime||!i.hasPlayed){var r=n.media.buffered,s=o.smallGapLimit||.5,a=o.gapDetectionThreshold||.3,l=n.media.currentTime,c=i._getIndex(r,l,a);if(null!==c&&0!==c){var u=r.start(c)+.1;if(!(u>n.media.duration)){var h=u-l,d=h<=s;h<t.BROWSER_GAP_TOLERANCE||d&&(!1!==o.useGapJump&&(n.media.currentTime=i.isSafari?u+.1:u),i.player&&i.player.emit("detectGap"),.08!==u&&n&&n.emit("log",{type:"oneevent",end_type:"gap",vid:n.config.vid,ext:{video_postion:Math.floor(1e3*u)}}))}}}}})),i}return T(t,[{key:"afterCreate",value:function(){var e=this;!1!==this.config.useGapJump&&(this.hasPlayed=!1,this.seekingEventReceived=!1,this.isSafari=/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),this.on(ue,this.onGapJump),this.on(ie,(function(){e.hasPlayed=!0})),this.on(ae,(function(){e.seekingEventReceived=!0})))}},{key:"_getIndex",value:function(e,t,i){if(!e||!e.length)return null;if(1===e.length&&e.end(0)-e.start(0)<1e-6)return null;for(var n=this._getBuffered(e),o=null,r=0;r<n.length;r++){if(n[r].start>t&&(0===r||n[r-1].end-t<=i)){o=r;break}}return o}},{key:"_getBuffered",value:function(e){if(!e)return[];for(var t=[],i=0;i<e.length;i++)t.push({start:e.start(i),end:e.end(i)});return t}}],[{key:"pluginName",get:function(){return"gapJump"}},{key:"defaultConfig",get:function(){return{useGapJump:!1,smallGapLimit:.5,gapDetectionThreshold:.3}}}]),t}();yo.BROWSER_GAP_TOLERANCE=.001;var mo=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"onWaiting",(function(){var e=I(i).config;i.jumpCnt>e.jumpCntMax||i.timer||!1===e.useWaitingTimeoutJump||(i.timer=setTimeout(i.onJump,1e3*e.waitingTime))})),x(I(i),"onJump",(function(){var e=I(i),t=e.player,n=e.config;if(clearTimeout(i.timer),i.timer=null,!(i.jumpCnt>n.jumpCntMax||!1===n.useWaitingTimeoutJump||t.media.paused&&0!==t.media.currentTime&&i.hasPlayed)){i.jumpSize=n.jumpSize*(i.jumpCnt+1),i.jumpCnt===n.jumpSize&&i.jumpSize<6&&(i.jumpSize=6);var o=t.currentTime+i.jumpSize;o>t.media.duration||(i.jumpCnt++,t.currentTime=o)}})),i}return T(t,[{key:"afterCreate",value:function(){var e=this,t=this.config,i=t.useWaitingTimeoutJump,n=t.jumpSize;!1!==i&&(this.hasPlayed=!1,this.jumpCnt=0,this.timer=null,this.jumpSize=n,this.on(ue,this.onWaiting),this.on([ne,he],(function(){clearTimeout(e.timer),e.timer=null,e.jumpSize=e.config.jumpSize})),this.on(ie,(function(){e.hasPlayed=!0})))}}],[{key:"pluginName",get:function(){return"waitingTimeoutJump"}},{key:"defaultConfig",get:function(){return{useWaitingTimeoutJump:!1,waitingTime:15,jumpSize:2,jumpCntMax:4}}}]),t}(),ko="cdn",Co=["cdn"],bo=function(){S(t,It);var e=A(t);function t(){var i;_(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return x(I(i=e.call.apply(e,[this].concat(o))),"getSpeed",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ko;if(!i.speedListCache||!i.speedListCache[e])return 0;if(i.speedListCache[e].length<=0)return 0;var t=0;return i.speedListCache[e].map((function(e){t+=e})),Math.floor(t/i.speedListCache[e].length)})),x(I(i),"startTimer",(function(){z.isMSE(i.player.video)||(i.initSpeedList(),i.cnt=0,i.timer=setTimeout(i.testSpeed,i.config.testTimeStep))})),x(I(i),"initSpeedList",(function(){i.speedListCache={},Co.forEach((function(e){i.speedListCache[e]=[]}))})),x(I(i),"_onRealSpeedChange",(function(e){e.speed&&i.appendList(e.speed,e.type||ko)})),x(I(i),"testSpeed",(function(){if(clearTimeout(i.timer),i.timer=null,i.player&&i.config.openSpeed){var e=i.config,t=e.url,n=e.loadSize,o=e.testCnt,r=e.testTimeStep,s=t+(t.indexOf("?")<0?"?testst=":"&testst=")+Date.now();if(!(i.cnt>=o)){i.cnt++;try{var a=(new Date).getTime(),l=null,c=new XMLHttpRequest;i.xhr=c,c.open("GET",s);var u={},h=Math.floor(10*Math.random());u.Range="bytes="+h+"-"+(n+h),u&&Object.keys(u).forEach((function(e){c.setRequestHeader(e,u[e])})),c.onreadystatechange=function(){if(4===c.readyState){i.xhr=null,l=(new Date).getTime();var e=c.getResponseHeader("Content-Length")/1024*8,t=Math.round(1e3*e/(l-a));i.appendList(t),i.timer=setTimeout(i.testSpeed,r)}},c.send()}catch(d){}}}})),x(I(i),"appendList",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ko;if(i.speedListCache&&i.speedListCache[t]){var n=i.config.saveSpeedMax;i.speedListCache[t].length>=n&&i.speedListCache[t].shift(),i.speedListCache[t].push(e);var o=I(i).player;o&&(t===ko?o.realTimeSpeed=e:o[i.getSpeedName("realTime",t)]=e),i.updateSpeed(t)}})),x(I(i),"updateSpeed",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ko,t=i.getSpeed(e),n=I(i).player;if(n)if(e===ko)n.avgSpeed&&t===n.avgSpeed||(n.avgSpeed=t,n.emit(Ie,{speed:t,realTimeSpeed:n.realTimeSpeed}));else{var o=i.getSpeedName("avg",e);n[o]&&t===n[o]||(n[o]=t,n.emit(Ie,{speed:t,realTimeSpeed:n.realTimeSpeed}))}})),i}return T(t,[{key:"afterCreate",value:function(){var e=this.config,t=e.openSpeed,i=e.addSpeedTypeList;(null==i?void 0:i.length)>0&&Co.push.apply(Co,D(i)),this.initSpeedList(),this.on("real_time_speed",this._onRealSpeedChange),this.timer=null,this.cnt=0,this.xhr=null,t&&this.on([pe,Se],this.startTimer)}},{key:"getSpeedName",value:function(e,t){return e+"Speed"+t.toUpperCase()}},{key:"openSpeed",get:function(){return this.config.openSpeed},set:function(e){if(this.config.openSpeed=e,!e&&this.timer)return clearTimeout(this.timer),void(this.timer=null);if(this.config.openSpeed){if(this.timer)return;this.timer=setTimeout(this.testSpeed,this.config.testTimeStep)}}},{key:"destroy",value:function(){var e=this;this.off("real_time_speed",this._onRealSpeedChange),this.off([pe,Se],this.startTimer),Co.forEach((function(t){e.speedListCache&&e.speedListCache[t]&&(e.speedListCache[t]=[])})),this.speedListCache&&(this.speedListCache={}),clearTimeout(this.timer),this.timer=null,this.xhr&&4!==this.xhr.readyState&&(this.xhr.cancel&&this.xhr.cancel(),this.xhr=null)}}],[{key:"pluginName",get:function(){return"testspeed"}},{key:"defaultConfig",get:function(){return{openSpeed:!1,testCnt:3,loadSize:204800,testTimeStep:3e3,url:"",saveSpeedMax:5,addSpeedTypeList:[]}}}]),t}(),_o=function(){S(t,It);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t,[{key:"afterCreate",value:function(){var e=this,t=this.player,i=this.config,n=t.media||t.video;(this.timer=null,this._lastDecodedFrames=0,this._currentStuckCount=0,this._lastCheckPoint=null,this._payload=[],i.disabled)||n.getVideoPlaybackQuality&&(this.on(ie,(function(){e._startTick()})),this.on(re,(function(){e._stopTick()})),this.on(oe,(function(){e._stopTick()})),this.on(me,(function(){e._stopTick()})))}},{key:"_startTick",value:function(){var e=this;this._stopTick(),this._timer=setTimeout((function(){e._checkDecodeFPS(),e._startTick()}),this.config.tick)}},{key:"_stopTick",value:function(){clearTimeout(this._timer),this._timer=null}},{key:"_checkBuffer",value:function(e,t){for(var i=!1,n=[],o=0;o<t.length;o++){var r=t.start(o),s=t.end(o);if(n.push({start:r,end:s}),r<=e&&e<=s-1){i=!0;break}}return{enoughBuffer:i,buffers:n}}},{key:"_checkStuck",value:function(e,t,i,n){var o=this.player.media||this.player.video,r=document.hidden,s=o.paused,a=o.readyState,l=o.currentTime,c=o.buffered;if(!(r||s||a<4)){var u=this._checkBuffer(l,c),h=u.enoughBuffer,d=u.buffers;h&&(e<=this.config.reportFrame?(this._currentStuckCount++,this._payload.push({currentTime:l,buffers:d,curDecodedFrames:e,totalVideoFrames:t,droppedVideoFrames:i,checkInterval:n}),this._currentStuckCount>=this.config.stuckCount&&(this.emit(Je,this._payload),this._reset())):this._reset())}}},{key:"_reset",value:function(){this._payload=[],this._currentStuckCount=0}},{key:"_checkDecodeFPS",value:function(){var e=this.player.media||this.player.video;if(e){var t=e.getVideoPlaybackQuality(),i=t.totalVideoFrames,n=t.droppedVideoFrames,o=performance.now();if(i&&this._lastCheckPoint){var r=i-this._lastDecodedFrames,s=o-this._lastCheckPoint;this._checkStuck(r,i,n,s)}this._lastDecodedFrames=i,this._lastCheckPoint=o}}},{key:"destroy",value:function(){this._stopTick()}}],[{key:"pluginName",get:function(){return"FpsDetect"}},{key:"defaultConfig",get:function(){return{disabled:!1,tick:1e3,stuckCount:3,reportFrame:0}}}]),t}();Ut.use({LANG:"zh-cn",TEXT:{ERROR_TYPES:{network:{code:1,msg:"视频下载错误"},mse:{code:2,msg:"流追加错误"},parse:{code:3,msg:"解析错误"},format:{code:4,msg:"格式错误"},decoder:{code:5,msg:"解码错误"},runtime:{code:6,msg:"语法错误"},timeout:{code:7,msg:"播放超时"},other:{code:8,msg:"其他错误"}},HAVE_NOTHING:"没有关于音频/视频是否就绪的信息",HAVE_METADATA:"音频/视频的元数据已就绪",HAVE_CURRENT_DATA:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒",HAVE_FUTURE_DATA:"当前及至少下一帧的数据是可用的",HAVE_ENOUGH_DATA:"可用数据足以开始播放",NETWORK_EMPTY:"音频/视频尚未初始化",NETWORK_IDLE:"音频/视频是活动的且已选取资源，但并未使用网络",NETWORK_LOADING:"浏览器正在下载数据",NETWORK_NO_SOURCE:"未找到音频/视频来源",MEDIA_ERR_ABORTED:"取回过程被用户中止",MEDIA_ERR_NETWORK:"网络错误",MEDIA_ERR_DECODE:"解码错误",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音频/视频格式",REPLAY:"重播",ERROR:"网络连接似乎出现了问题",PLAY_TIPS:"播放",PAUSE_TIPS:"暂停",PLAYNEXT_TIPS:"下一集",DOWNLOAD_TIPS:"下载",ROTATE_TIPS:"旋转",RELOAD_TIPS:"重新载入",FULLSCREEN_TIPS:"进入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"进入样式全屏",EXITCSSFULLSCREEN_TIPS:"退出样式全屏",TEXTTRACK:"字幕",PIP:"画中画",SCREENSHOT:"截图",LIVE:"正在直播",OFF:"关闭",OPEN:"开启",MINI_DRAG:"点击按住可拖动视频",MINISCREEN:"小屏幕",REFRESH_TIPS:"请试试",REFRESH:"刷新",FORWARD:"快进中",LIVE_TIP:"直播"}});var wo=T((function e(t,i){var n,o,r;_(this,e);var s=i&&"mobile"===i.isMobileSimulateMode,a=i.isLive,l=[].concat(a?[]:[yn,ln,ao,oo,vn],[un,gn,wn,On,Yn,Xn,Fn,Hn,bn,In]),c=[fi,pi,ki,on,Ci,$n,Qn,ro,Di];this.plugins=[vo,hi].concat(D(l),c,[yo,mo]);var u=s?"mobile":J.device;switch(u){case"pc":(n=this.plugins).push.apply(n,[tn,Ni,Jn,bo,_o]);break;case"mobile":(o=this.plugins).push.apply(o,[Qi]);break;default:(r=this.plugins).push.apply(r,[tn,Ni,Jn])}(J.os.isIpad||"pc"===u)&&this.plugins.push(fo),J.os.isIpad&&this.plugins.push(Ni),this.ignores=[],this.i18n=[]})),To=function(){S(t,ii);var e=A(t);function t(){return _(this,t),e.apply(this,arguments)}return T(t)}();x(To,"defaultPreset",wo),x(To,"Util",z),x(To,"Sniffer",J),x(To,"Errors",te),x(To,"Events",$e),x(To,"Plugin",It),x(To,"BasePlugin",bt),x(To,"I18N",Ut),x(To,"STATE_CLASS",Dt),x(To,"InstManager",Jt);const xo=["id"],So=c((Eo=a({},{name:"ArtVideoPlayer"}),Po={__name:"index",props:{playerId:{default:""},videoUrl:{default:""},posterUrl:{default:""},autoplay:{type:Boolean,default:!1},volume:{default:1},playbackRates:{},loop:{type:Boolean,default:!1},muted:{type:Boolean,default:!1},commonStyle:{}},setup(e){const t=e,i=u(null),n={progressColor:"rgba(255, 255, 255, 0.3)",playedColor:"#00AEED",cachedColor:"rgba(255, 255, 255, 0.6)",sliderBtnStyle:{width:"10px",height:"10px",backgroundColor:"#00AEED"},volumeColor:"#00AEED"};return h((()=>{i.value=new To({id:t.playerId,lang:"zh",volume:t.volume,autoplay:t.autoplay,screenShot:!0,url:t.videoUrl,poster:t.posterUrl,fluid:!0,playbackRate:t.playbackRates,loop:t.loop,muted:t.muted,commonStyle:a(a({},n),t.commonStyle)}),i.value.on("play",(()=>{})),i.value.on("pause",(()=>{})),i.value.on("error",(e=>{}))})),d((()=>{i.value&&i.value.destroy()})),(e,t)=>(p(),f("div",{id:e.playerId},null,8,xo))}},t(Eo,i(Po))));var Eo,Po;const Io={class:"page-content"},Lo={class:"video-container"},Ao=m(c({__name:"index",setup(e){const t=u("//lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4"),i=u(y);return(e,n)=>{const o=So;return p(),f("div",Io,[g("div",Lo,[v(o,{playerId:"my-video-1",videoUrl:t.value,posterUrl:i.value,autoplay:!1,volume:1,playbackRates:[.5,1,1.5,2]},null,8,["videoUrl","posterUrl"])])])}}}),[["__scopeId","data-v-27be0383"]]);export{Ao as default};
