import api from '@/utils/http'
import type { CustomerItem } from '@/types/appointment'

export interface CustomerDetailResponse extends CustomerItem {
  stats: {
    totalAppointments: number
    totalSpent: number
    favoritePackage: string | null
    firstAppointment: string | null
    lastAppointment: string | null
  }
}

export class CustomerService {
  // 获取客户详情
  static async getCustomerDetail(id: string): Promise<CustomerDetailResponse> {
    return api.get({
      url: `/customers/${id}`
    })
  }

  // 创建客户
  static async createCustomer(data: Partial<CustomerItem>): Promise<CustomerItem> {
    return api.post({
      url: '/customers',
      data
    })
  }

  // 更新客户
  static async updateCustomer(id: string, data: Partial<CustomerItem>): Promise<CustomerItem> {
    return api.put({
      url: `/customers/${id}`,
      data
    })
  }

  // 删除客户
  static async deleteCustomer(id: string, deleteRelatedAppointments: boolean = false): Promise<void> {
    return api.del({
      url: `/customers/${id}${deleteRelatedAppointments ? '?deleteRelated=true' : ''}`
    })
  }
  
  // 批量删除客户
  static async deleteCustomers(ids: string[], deleteRelatedAppointments: boolean = false): Promise<{
    message: string;
    deletedCount: number;
    invalidIds?: string[];
    notFoundIds?: string[];
  }> {
    return api.post({
      url: '/customers/batch-delete',
      data: { 
        ids,
        deleteRelated: deleteRelatedAppointments 
      }
    })
  }
}
