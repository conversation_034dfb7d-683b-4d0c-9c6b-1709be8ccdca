import{_ as s}from"./index-CWUKzE_Q.js";import{d as t}from"./index-C5Q-N6Yp.js";import{k as e,c as a,B as o,C as r,D as i,S as n,a6 as l,u as c}from"./vendor-9ydHGNSq.js";import{_ as m}from"./_plugin-vue_export-helper-BCo6x5W8.js";const d=""+new URL("lf_icon2-CrdSAdav.webp",import.meta.url).href,p=m(e({__name:"Banner",setup(e){const m=t(),p=a((()=>m.getUserInfo)),u=()=>{};return(t,e)=>{const a=s;return r(),o(a,{class:"banner",height:"13.3rem",title:`欢迎回来 ${c(p).userName}`,backgroundColor:"var(--el-color-primary-light-9)",titleColor:"var(--art-gray-900)",decoration:!1,meteorConfig:{enabled:!0,count:10},buttonConfig:{show:!1,text:""},imageConfig:{src:c(d),width:"18rem",bottom:"-7.5rem"},onClick:u},{default:i((()=>e[0]||(e[0]=[n("div",{class:"banner-slot"},[n("div",{class:"item"},[n("p",{class:"title"},[l("¥2,340"),n("i",{class:"iconfont-sys text-success"},"")]),n("p",{class:"subtitle"},"今日销售额")]),n("div",{class:"item"},[n("p",{class:"title"},[l("35%"),n("i",{class:"iconfont-sys text-success"},"")]),n("p",{class:"subtitle"},"较昨日")])],-1)]))),_:1,__:[0]},8,["title","imageConfig"])}}}),[["__scopeId","data-v-7fcaa69c"]]);export{p as default};
