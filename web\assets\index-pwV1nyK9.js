var e=Object.defineProperty,t=Object.defineProperties,o=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,l=(t,o,r)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(e,t)=>{for(var o in t||(t={}))a.call(t,o)&&l(e,o,t[o]);if(r)for(var o of r(t))i.call(t,o)&&l(e,o,t[o]);return e},s=(e,r)=>t(e,o(r));import"./index-C5Q-N6Yp.js";/* empty css                   */import{_ as d}from"./index-BqI1XISX.js";import{u as c,a as p}from"./useChart-CoPw7zBp.js";import{k as u,a2 as f,aG as h,O as g,C as m,Q as b,B as y,R as v,u as w}from"./vendor-9ydHGNSq.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";const j=x(u(s(n({},{name:"ArtRingChart"}),{__name:"index",props:{data:{default:()=>[]},radius:{default:()=>["50%","80%"]},borderRadius:{default:10},centerText:{default:""},showLabel:{type:Boolean,default:!1},height:{default:c().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>c().colors},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"right"}},setup(e){const t=e,{chartRef:o,isDark:r,getAnimationConfig:a,getTooltipStyle:i,getLegendStyle:l,isEmpty:c}=p({props:t,checkEmpty:()=>{var e;return!(null==(e=t.data)?void 0:e.length)||t.data.every((e=>0===e.value))},watchSources:[()=>t.data,()=>t.centerText],generateOptions:()=>{const e=()=>{if(!t.showLegend)return["50%","50%"];switch(t.legendPosition){case"left":return["60%","50%"];case"right":return["40%","50%"];case"top":return["50%","60%"];case"bottom":return["50%","40%"];default:return["50%","50%"]}},o={tooltip:t.showTooltip?i("item",{formatter:"{b}: {c} ({d}%)"}):void 0,legend:t.showLegend?l(t.legendPosition):void 0,series:[s(n({name:"数据占比",type:"pie",radius:t.radius,center:e(),avoidLabelOverlap:!1,itemStyle:{borderRadius:t.borderRadius,borderColor:r.value?"#2c2c2c":"#fff",borderWidth:0},label:{show:t.showLabel,formatter:"{b}\n{d}%",position:"outside",color:r.value?"#ccc":"#999",fontSize:12},emphasis:{label:{show:!1,fontSize:14,fontWeight:"bold"}},labelLine:{show:t.showLabel,length:15,length2:25,smooth:!0},data:t.data,color:t.colors},a()),{animationType:"expansion"})]};if(t.centerText){const a=e();o.title={text:t.centerText,left:a[0],top:a[1],textAlign:"center",textVerticalAlign:"middle",textStyle:{fontSize:18,fontWeight:500,color:r.value?"#999":"#ADB0BC"}}}return o}});return(e,r)=>{const a=d,i=h;return f((m(),g("div",{ref_key:"chartRef",ref:o,class:"art-ring-chart",style:b({height:t.height})},[w(c)?(m(),y(a,{key:0})):v("",!0)],4)),[[i,t.loading]])}}})),[["__scopeId","data-v-2ea9dec9"]]);export{j as _};
