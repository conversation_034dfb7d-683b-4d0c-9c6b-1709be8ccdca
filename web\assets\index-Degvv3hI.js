var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,i=(a,t,o)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o,n=(e,a)=>{for(var t in a||(a={}))l.call(a,t)&&i(e,t,a[t]);if(o)for(var t of o(a))r.call(a,t)&&i(e,t,a[t]);return e},s=(e,o)=>a(e,t(o));import{O as u,P as d}from"./index-C5Q-N6Yp.js";/* empty css                   */import{_ as c}from"./index-BqI1XISX.js";import{L as y,g as h}from"./index-DEP0vMzR.js";import{u as m,b as f}from"./useChart-CoPw7zBp.js";import{k as p,r as v,c as g,w as b,d as x,bw as w,a2 as A,aG as L,O as S,C as j,Q as O,B as _,R as B,u as D}from"./vendor-9ydHGNSq.js";import{_ as T}from"./_plugin-vue_export-helper-BCo6x5W8.js";const P=T(p(s(n({},{name:"ArtLineChart"}),{__name:"index",props:{data:{default:()=>[0,0,0,0,0,0,0]},xAxisData:{default:()=>[]},lineWidth:{default:2.5},showAreaColor:{type:Boolean,default:!1},smooth:{type:Boolean,default:!0},symbol:{default:"none"},symbolSize:{default:6},animationDelay:{default:200},height:{default:m().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>m().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!0},showSplitLine:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(e){const a=e,{chartRef:t,isDark:o,initChart:l,getAxisLineStyle:r,getAxisLabelStyle:i,getAxisTickStyle:m,getSplitLineStyle:p,getTooltipStyle:T,getLegendStyle:P,getGridWithLegend:C}=f(),W=v(!1),k=v(),E=v([]),z=()=>{k.value&&(clearTimeout(k.value),k.value=void 0)},G=g((()=>{if(a.isEmpty)return!0;if(Array.isArray(a.data)&&"number"==typeof a.data[0]){const e=a.data;return!e.length||e.every((e=>0===e))}if(Array.isArray(a.data)&&"object"==typeof a.data[0]){const e=a.data;return!e.length||e.every((e=>{var a;return!(null==(a=e.data)?void 0:a.length)||e.data.every((e=>0===e))}))}return!0})),M=g((()=>Array.isArray(a.data)&&a.data.length>0&&"object"==typeof a.data[0]&&"name"in a.data[0])),R=g((()=>{if(M.value){return a.data.reduce(((e,a)=>{var t;if(null==(t=a.data)?void 0:t.length){const t=Math.max(...a.data);return Math.max(e,t)}return e}),0)}{const e=a.data;return(null==e?void 0:e.length)?Math.max(...e):0}})),I=()=>{if(M.value){return a.data.map((e=>s(n({},e),{data:new Array(e.data.length).fill(0)})))}{const e=a.data;return new Array(e.length).fill(0)}},V=()=>(M.value,[...a.data]),q=(e,t)=>e||(void 0!==t?a.colors[t%a.colors.length]:u("--el-color-primary")),H=e=>{var t,o,l,r,i;return{name:e.name,data:e.data,type:"line",color:e.color,smooth:null!=(t=e.smooth)?t:a.smooth,symbol:null!=(o=e.symbol)?o:a.symbol,symbolSize:null!=(l=e.symbolSize)?l:a.symbolSize,lineStyle:{width:null!=(r=e.lineWidth)?r:a.lineWidth,color:e.color},areaStyle:e.areaStyle,emphasis:{focus:"series",lineStyle:{width:(null!=(i=e.lineWidth)?i:a.lineWidth)+1}}}},Q=(e=!1)=>{const t={animation:!0,animationDuration:e?0:1300,animationDurationUpdate:e?0:1300,grid:C(a.showLegend&&M.value,a.legendPosition,{top:15,right:15,left:0}),tooltip:a.showTooltip?T():void 0,xAxis:{type:"category",boundaryGap:!1,data:a.xAxisData,axisTick:m(),axisLine:r(a.showAxisLine),axisLabel:i(a.showAxisLabel)},yAxis:{type:"value",min:0,max:R.value,axisLabel:i(a.showAxisLabel),axisLine:r(a.showAxisLine),splitLine:p(a.showSplitLine)}};if(a.showLegend&&M.value&&(t.legend=P(a.legendPosition)),M.value){const e=E.value;t.series=e.map(((e,t)=>{const o=q(a.colors[t],t),l=((e,t)=>{if(!e.areaStyle&&!e.showAreaColor&&!a.showAreaColor)return;const o=e.areaStyle||{};return o.custom?o.custom:{color:new y(0,0,0,1,[{offset:0,color:d(t,o.startOpacity||.2).rgba},{offset:1,color:d(t,o.endOpacity||.02).rgba}])}})(e,o);return H({name:e.name,data:e.data,color:o,smooth:e.smooth,symbol:e.symbol,lineWidth:e.lineWidth,areaStyle:l})}))}else{const e=E.value,o=q(a.colors[0]),l=(()=>{if(!a.showAreaColor)return;const e=q(a.colors[0]);return{color:new y(0,0,0,1,[{offset:0,color:d(e,.2).rgba},{offset:1,color:d(e,.02).rgba}])}})();t.series=[H({data:e,color:o,areaStyle:l})]}return t},U=e=>{G.value||l(e)},F=()=>{if(G.value)E.value=V(),U(Q(!1));else if(z(),W.value=!0,M.value){const e=a.data;E.value=I(),U(Q(!0)),e.forEach(((e,t)=>{setTimeout((()=>{const a=E.value;a[t]=n({},e),E.value=[...a],U(Q(!1))}),t*a.animationDelay+100)}));const t=(e.length-1)*a.animationDelay+1500;setTimeout((()=>{W.value=!1}),t)}else E.value=I(),U(Q(!0)),k.value=setTimeout((()=>{E.value=V(),U(Q(!1)),W.value=!1}),100)},J=()=>{F()},K=()=>{F()};return b([()=>a.data,()=>a.xAxisData,()=>a.colors],(()=>{W.value||J()}),{deep:!0}),b(o,(()=>{var e;const a=(null==(e=t.value)?void 0:e.__echart__)||h(t.value);if(a&&!G.value){const e=Q(!1);a.setOption(e)}})),x((()=>{J(),t.value&&t.value.addEventListener("chartVisible",K)})),w((()=>{z(),t.value&&t.value.removeEventListener("chartVisible",K)})),(e,o)=>{const l=c,r=L;return A((j(),S("div",{ref_key:"chartRef",ref:t,class:"art-line-chart",style:O({height:a.height})},[D(G)?(j(),_(l,{key:0})):B("",!0)],4)),[[r,a.loading]])}}})),[["__scopeId","data-v-79c89510"]]);export{P as _};
