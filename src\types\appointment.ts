/**
 * 预约状态类型
 */
export type AppointmentStatus = 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'created'

/**
 * 支付状态类型
 */
export type PaymentStatus = 'paid' | 'unpaid' | 'refunded'

/**
 * 支付方式类型
 */
export type PaymentMethod = 'wechat' | 'alipay' | 'cash' | 'card' | 'other'

/**
 * 预约状态标签映射
 */
export const APPOINTMENT_STATUS_MAP = {
  pending: '待确认',
  confirmed: '已确认',
  completed: '已完成',
  cancelled: '已取消',
  created: '已创建'
}

/**
 * 支付状态标签映射
 */
export const PAYMENT_STATUS_MAP = {
  paid: '已支付',
  unpaid: '未支付',
  refunded: '已退款'
}

/**
 * 支付方式标签映射
 */
export const PAYMENT_METHOD_MAP = {
  wechat: '微信支付',
  alipay: '支付宝',
  cash: '现金',
  card: '银行卡',
  other: '其他'
}

/**
 * 预约项接口
 */
export interface AppointmentItem {
  _id: string
  customer?: string
  customerInfo?: {
    name: string
    phone: string
    email?: string
    address?: string
  }
  customerTags?: string[]
  packageType?: string
  packageInfo?: {
    name: string
    price: number
  }
  changeOption?: string
  changeOptionInfo?: {
    name: string
    price: number
  }
  memoryCard?: string
  memoryCardInfo?: {
    size: string
    price: number
  }
  totalPrice: number
  appointmentTime: string
  status: AppointmentStatus
  remark?: string
  paymentStatus: PaymentStatus
  paymentMethod?: PaymentMethod
  location?: string
  photographer?: string
  images?: string[]
  timeline?: {
    status: AppointmentStatus | string
    time: string
    description: string
    reason?: string
  }[]
  createdAt: string
  updatedAt: string
}

/**
 * 预约列表响应接口
 */
export interface AppointmentListResponse {
  total: number
  items: AppointmentItem[]
  page: number
  limit: number
}

/**
 * 预约查询参数
 */
export interface AppointmentQueryParams {
  page?: number
  limit?: number
  keyword?: string
  packageTypeId?: string
  changeOptionId?: string
  memoryCardId?: string
  minPrice?: number
  maxPrice?: number
  startDate?: string
  endDate?: string
  status?: AppointmentStatus
  sortProp?: string
  sortOrder?: 'ascending' | 'descending'
}

/**
 * 预约创建参数
 */
export interface AppointmentCreateParams {
  customerName: string
  customerPhone?: string
  customerEmail?: string
  customerAddress?: string
  customerTags?: string[]
  packageTypeName: string
  packageTypePrice: number

  changeOptionName?: string
  changeOptionPrice?: number
  memoryCardSize?: string
  memoryCardPrice?: number
  totalPrice?: number
  appointmentTime: string
  status?: AppointmentStatus
  remark?: string
}

/**
 * 套餐类型选项接口
 */
export interface PackageTypeOption {
  _id: string
  name: string
  description?: string
  price: number
  isActive: boolean
}

/**
 * 随心换选项接口
 */
export interface ChangeOption {
  _id: string
  name: string
  description?: string
  price: number
  isActive: boolean
}

/**
 * 内存卡选项接口
 */
export interface MemoryCardOption {
  _id: string
  size: string
  description?: string
  price: number
  isActive: boolean
}

/**
 * 客户信息接口
 */
export interface CustomerItem {
  _id: string
  name: string
  phone?: string
  email?: string
  address?: string
  tags?: string[]
  remark?: string
  latestAppointment?: AppointmentItem | null
  appointmentCount: number
  createdAt: string
  updatedAt: string
  // 预约相关字段
  appointmentTime?: string
  status?: AppointmentStatus
  packageType?: string
  packageTypeName?: string
  packageInfo?: {
    name: string
    price: number
  }
  changeOption?: string
  changeOptionInfo?: {
    name: string
    price: number
  }
  memoryCard?: string
  memoryCardInfo?: {
    size: string
    price: number
  }
  appointmentRemark?: string
}

/**
 * 客户列表响应接口
 */
export interface CustomerListResponse {
  total: number
  items: CustomerItem[]
  page: number
  limit: number
}

/**
 * 客户查询参数
 */
export interface CustomerQueryParams {
  page?: number
  limit?: number
  keyword?: string
  tags?: string
  hasAppointment?: boolean
  appointmentStatus?: AppointmentStatus
}

/**
 * 预约统计数据接口
 */
export interface AppointmentStatsResponse {
  todayAppointments: number
  pendingAppointments: number
  completedAppointments: number
  totalCustomers: number
  todayChange: number
  pendingChange: number
  completedChange: number
  customersChange: number
}

/**
 * 客户统计数据接口
 */
export interface CustomerStatsResponse {
  totalCustomers: number
  newCustomersToday: number
  newCustomersThisMonth: number
  activeCustomers: number
  customerGrowthRate: number
}

/**
 * 今日预约数据接口
 */
export interface TodayAppointmentsResponse {
  total: number
  pending: number
  confirmed: number
  completed: number
  cancelled: number
  appointments: AppointmentItem[]
}

/**
 * 预约状态统计接口
 */
export interface AppointmentStatusStatsResponse {
  pending: number
  confirmed: number
  completed: number
  cancelled: number
  total: number
}
