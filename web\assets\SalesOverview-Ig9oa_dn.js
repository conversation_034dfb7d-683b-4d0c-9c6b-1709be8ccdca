import{_ as a}from"./index-Degvv3hI.js";import{k as s,O as t,C as e,bp as r,x as i}from"./vendor-9ydHGNSq.js";import{_ as o}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./index-DEP0vMzR.js";import"./useChart-CoPw7zBp.js";const d={class:"card art-custom-card"},c=o(s({__name:"SalesOverview",setup(s){const o=[50,25,40,20,70,35,65,30,35,20,40,44],c=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];return(s,p)=>{const l=a;return e(),t("div",d,[p[0]||(p[0]=r('<div class="card-header" data-v-ff2928a7><div class="title" data-v-ff2928a7><h4 class="box-title" data-v-ff2928a7>访问量</h4><p class="subtitle" data-v-ff2928a7>今年增长<span class="text-success" data-v-ff2928a7>+15%</span></p></div></div>',1)),i(l,{class:"chart",height:"calc(100% - 40px)",data:o,xAxisData:c,showAreaColor:!0,showAxisLine:!1})])}}}),[["__scopeId","data-v-ff2928a7"]]);export{c as default};
