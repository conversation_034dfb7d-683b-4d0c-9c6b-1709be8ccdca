/**
 * 日志管理工具
 * 统一管理项目中的日志输出，支持环境区分
 */

interface LoggerOptions {
  prefix?: string
  showTimestamp?: boolean
}

class Logger {
  private isDev: boolean
  private prefix: string
  private showTimestamp: boolean

  constructor(options: LoggerOptions = {}) {
    this.isDev = import.meta.env.DEV
    this.prefix = options.prefix || '[App]'
    this.showTimestamp = options.showTimestamp ?? true
  }

  private formatMessage(level: string, message: any, ...args: any[]): any[] {
    const timestamp = this.showTimestamp ? new Date().toLocaleTimeString() : ''
    const prefix = timestamp ? `${this.prefix} [${timestamp}] [${level}]` : `${this.prefix} [${level}]`
    
    return [prefix, message, ...args]
  }

  /**
   * 调试日志 - 已禁用输出
   */
  debug(message: any, ...args: any[]): void {
    // 调试日志已禁用
  }

  /**
   * 信息日志 - 已禁用输出
   */
  info(message: any, ...args: any[]): void {
    // 信息日志已禁用
  }

  /**
   * 警告日志 - 已禁用输出
   */
  warn(message: any, ...args: any[]): void {
    // 警告日志已禁用
  }

  /**
   * 错误日志 - 已禁用输出
   */
  error(message: any, ...args: any[]): void {
    // 错误日志已禁用
  }

  /**
   * 性能日志 - 已禁用输出
   */
  perf(label: string, fn?: () => void): void {
    // 性能日志已禁用
    if (fn) {
      fn()
    }
  }

  /**
   * 结束性能计时 - 已禁用输出
   */
  perfEnd(label: string): void {
    // 性能计时已禁用
  }
}

// 创建默认日志实例
export const logger = new Logger()

// 创建特定模块的日志实例
export const createLogger = (options: LoggerOptions) => new Logger(options)

// 便捷方法
export const log = {
  debug: logger.debug.bind(logger),
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  perf: logger.perf.bind(logger),
  perfEnd: logger.perfEnd.bind(logger)
}
