var e=Object.defineProperty,n=Object.defineProperties,t=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,l=(n,t,a)=>t in n?e(n,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[t]=a;import{k as o,as as u,r as d,c as r,p as c,O as m,C as p,x as y,ap as b,D as h,a2 as f,S as v,Q as x,u as w,F as g,Z as k,W as L,R as D,X as M,ai as P}from"./vendor-9ydHGNSq.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const T={class:"menu-right"},W=["onClick"],E=["innerHTML"],H={class:"menu-label"},$={class:"submenu-title"},j=["innerHTML"],_={class:"menu-label"},R=["onClick"],I=["innerHTML"],C={class:"menu-label"},X=o((A=((e,n)=>{for(var t in n||(n={}))i.call(n,t)&&l(e,t,n[t]);if(a)for(var t of a(n))s.call(n,t)&&l(e,t,n[t]);return e})({},{name:"ArtMenuRight"}),n(A,t({__name:"index",props:{menuItems:{},menuWidth:{default:120},submenuWidth:{default:150},itemHeight:{default:32},boundaryDistance:{default:10},menuPadding:{default:5},itemPaddingX:{default:6},borderRadius:{default:6},animationDuration:{default:100}},emits:["select","show","hide"],setup(e,{expose:n,emit:t}){u((e=>({"03a3f243":a.menuWidth+"px","4122aad2":a.borderRadius+"px","78ce3298":a.animationDuration+"ms"})));const a=e,i=t,s=d(!1),l=d({x:0,y:0});let o=null,O=!1;const X=r((()=>({position:"fixed",left:`${l.value.x}px`,top:`${l.value.y}px`,zIndex:2e3,width:`${a.menuWidth}px`}))),A=r((()=>({padding:`${a.menuPadding}px`}))),S=r((()=>({height:`${a.itemHeight}px`,padding:`0 ${a.itemPaddingX}px`,borderRadius:"4px"}))),q=r((()=>({minWidth:`${a.submenuWidth}px`,padding:`${a.menuPadding}px 0`,borderRadius:`${a.borderRadius}px`}))),z=e=>{const n=window.innerWidth,t=window.innerHeight,i=(()=>{let e=2*a.menuPadding;return a.menuItems.forEach((n=>{e+=a.itemHeight,n.showLine&&(e+=10)})),e})();let s=e.clientX,l=e.clientY;return s+a.menuWidth>n-a.boundaryDistance&&(s=Math.max(a.boundaryDistance,s-a.menuWidth)),l+i>t-a.boundaryDistance&&(l=Math.max(a.boundaryDistance,t-i-a.boundaryDistance)),s=Math.max(a.boundaryDistance,Math.min(s,n-a.menuWidth-a.boundaryDistance)),l=Math.max(a.boundaryDistance,Math.min(l,t-i-a.boundaryDistance)),{x:s,y:l}},B=()=>{O&&(document.removeEventListener("click",F),document.removeEventListener("contextmenu",Q),document.removeEventListener("keydown",Y),O=!1)},F=e=>{const n=e.target,t=document.querySelector(".context-menu");t&&t.contains(n)||Z()},Q=()=>{Z()},Y=e=>{"Escape"===e.key&&Z()},Z=()=>{s.value&&(s.value=!1,i("hide"),o&&(window.clearTimeout(o),o=null),B())},G=e=>{e.disabled||(i("select",e),Z())},J=e=>{e.style.transformOrigin="top left"},K=()=>{B(),o&&(window.clearTimeout(o),o=null)};return c((()=>{B(),o&&(window.clearTimeout(o),o=null)})),n({show:e=>{e.preventDefault(),e.stopPropagation(),o&&(window.clearTimeout(o),o=null),l.value=z(e),s.value=!0,i("show"),o=window.setTimeout((()=>{s.value&&(O||(document.addEventListener("click",F),document.addEventListener("contextmenu",Q),document.addEventListener("keydown",Y),O=!0)),o=null}),50)},hide:Z,visible:r((()=>s.value))}),(e,n)=>(p(),m("div",T,[y(b,{name:"context-menu",onBeforeEnter:J,onAfterLeave:K},{default:h((()=>[f(v("div",{style:x(w(X)),class:"context-menu"},[v("ul",{class:"menu-list",style:x(w(A))},[(p(!0),m(g,null,k(e.menuItems,(e=>(p(),m(g,{key:e.key},[e.children?(p(),m("li",{key:1,class:"menu-item submenu",style:x(w(S))},[v("div",$,[e.icon?(p(),m("i",{key:0,class:"iconfont-sys",innerHTML:e.icon},null,8,j)):D("",!0),v("span",_,M(e.label),1),n[0]||(n[0]=v("i",{class:"iconfont-sys submenu-arrow"},"",-1))]),v("ul",{class:"submenu-list",style:x(w(q))},[(p(!0),m(g,null,k(e.children,(e=>(p(),m("li",{key:e.key,class:L(["menu-item",{"is-disabled":e.disabled,"has-line":e.showLine}]),style:x(w(S)),onClick:n=>G(e)},[e.icon?(p(),m("i",{key:0,class:"iconfont-sys",innerHTML:e.icon},null,8,I)):D("",!0),v("span",C,M(e.label),1)],14,R)))),128))],4)],4)):(p(),m("li",{key:0,class:L(["menu-item",{"is-disabled":e.disabled,"has-line":e.showLine}]),style:x(w(S)),onClick:n=>G(e)},[e.icon?(p(),m("i",{key:0,class:"iconfont-sys",innerHTML:e.icon},null,8,E)):D("",!0),v("span",H,M(e.label),1)],14,W))],64)))),128))],4)],4),[[P,w(s)]])])),_:1})]))}}))));var A;const S=O(X,[["__scopeId","data-v-1f2e2f4a"]]);export{S as A};
