import{_ as e}from"./index-pwV1nyK9.js";import{_ as s}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as r,C as a,S as t,x as o}from"./vendor-9ydHGNSq.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const i={class:"card art-custom-card",style:{height:"13.3rem"}};const l=s({},[["render",function(s,l){const n=e;return a(),r("div",i,[l[0]||(l[0]=t("div",{class:"card-header"},[t("p",{class:"title",style:{"font-size":"24px"}},"205,216"),t("p",{class:"subtitle"},"总订单量")],-1)),o(n,{data:[{value:30,name:"已完成"},{value:25,name:"处理中"},{value:45,name:"待发货"}],color:["#4C87F3","#93F1B4","#8BD8FC"],radius:["56%","76%"],height:"7rem",showLabel:!1,borderRadius:0})])}]]);export{l as default};
