import"./index-C5Q-N6Yp.js";/* empty css                        *//* empty css                  */import{C as a}from"./vue3-count-to.esm-Dsfbf00q.js";import{k as l,r as e,V as t,O as u,C as n,S as s,x as r,u as o,D as d,a2 as i,B as v,a3 as c,a6 as p,bA as _}from"./vendor-9ydHGNSq.js";import{_ as f}from"./_plugin-vue_export-helper-BCo6x5W8.js";const m={class:"page-content"},V={class:"mt-4"},h=f(l({__name:"index",setup(l){const f=e(1e3),h=e(19999.99),x=e(2023.45),j=e(5e3),k=e(),y=e(!1),C=()=>{var a,l;if(!y.value)try{null==(a=k.value)||a.reset(),null==(l=k.value)||l.start(),y.value=!0}catch(e){}},b=()=>{var a;if(y.value)try{null==(a=k.value)||a.pause(),y.value=!1}catch(l){}},g=()=>{var a;try{null==(a=k.value)||a.reset(),y.value=!1}catch(l){}};return(l,e)=>{const y=c,q=_,w=t("ripple");return n(),u("div",m,[e[3]||(e[3]=s("h2",null,"基础用法",-1)),r(o(a),{endVal:f.value,duration:1e3},null,8,["endVal"]),e[4]||(e[4]=s("h2",null,"带前缀后缀",-1)),r(o(a),{prefix:"¥",suffix:"元",startVal:0,endVal:h.value,duration:2e3},null,8,["endVal"]),e[5]||(e[5]=s("h2",null,"小数点和分隔符",-1)),r(o(a),{startVal:0,endVal:x.value,decimals:2,decimal:".",separator:",",duration:2500},null,8,["endVal"]),e[6]||(e[6]=s("h2",null,"控制按钮",-1)),r(o(a),{ref_key:"countTo",ref:k,startVal:0,endVal:j.value,duration:3e3,autoplay:!1},null,8,["endVal"]),s("div",V,[r(q,null,{default:d((()=>[i((n(),v(y,{onClick:C},{default:d((()=>e[0]||(e[0]=[p("开始")]))),_:1,__:[0]})),[[w]]),i((n(),v(y,{onClick:b},{default:d((()=>e[1]||(e[1]=[p("暂停")]))),_:1,__:[1]})),[[w]]),i((n(),v(y,{onClick:g},{default:d((()=>e[2]||(e[2]=[p("重置")]))),_:1,__:[2]})),[[w]])])),_:1})])])}}}),[["__scopeId","data-v-83e02bf6"]]);export{h as default};
