/**
 * API接口统一导出
 */

// 导出所有API服务类和接口
export * from './usersApi'
export * from './menuApi'
export * from './roleApi'
export * from './userManageApi'
export * from './appointmentApi'

// 创建统一的API服务映射
import { UserService } from './usersApi'
import { MenuService } from './menuApi'
import { RoleService } from './roleApi'
import { UserManageService } from './userManageApi'
import { AppointmentService } from './appointmentApi'

// 统一API服务对象
export const API = {
  user: UserService,
  menu: MenuService,
  role: RoleService,
  userManage: UserManageService,
  appointment: AppointmentService
}
