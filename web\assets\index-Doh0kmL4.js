const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-C5Q-N6Yp.js","./vendor-9ydHGNSq.js","./index-C3d_MO4K.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,s=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,l=(a,s,t)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[s]=t,n=(e,a)=>{for(var s in a||(a={}))r.call(a,s)&&l(e,s,a[s]);if(t)for(var s of t(a))o.call(a,s)&&l(e,s,a[s]);return e},i=(e,t)=>a(e,s(t)),c=(e,a,s)=>new Promise(((t,r)=>{var o=e=>{try{n(s.next(e))}catch(a){r(a)}},l=e=>{try{n(s.throw(e))}catch(a){r(a)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,l);n((s=s.apply(e,a)).next())}));import{c as u,u as d,d as p,A as m,n as g,U as f,_ as h,H as y,o as v}from"./index-C5Q-N6Yp.js";/* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_ as b}from"./index-C015bGm8.js";/* empty css                        *//* empty css                  *//* empty css                     *//* empty css                         */import{k as _,s as w,M as j,r as x,aa as k,c as C,d as O,O as S,C as T,S as E,x as P,D as V,az as I,F as R,Z as D,u as M,aA as A,W as U,R as q,X as K,aB as L,ac as $,$ as z,a0 as B,B as F,Y as Z,aE as Y,a3 as H,a6 as J,ab as N,E as W,n as X,f as G}from"./vendor-9ydHGNSq.js";import{_ as Q}from"./login-bg-Dqmxv51G.js";import{a as ee}from"./index-DiJiIjk5.js";import{_ as ae}from"./_plugin-vue_export-helper-BCo6x5W8.js";const se={class:"page-login"},te={class:"box"},re={class:"top-right-wrap"},oe={class:"menu-txt"},le={key:0,class:"iconfont-sys icon-check"},ne={class:"logo"},ie={class:"icon"},ce={class:"desc"},ue={class:"form"},de=["src"],pe={class:"op"},me=ae(_(i(n({},{name:"Login"}),{__name:"index",setup(e){const{t:a}=u(),s=d(),{isDark:t,systemThemeType:r}=w(s),o=p(),l=j(),_=x(""),ae=x(""),me=m.systemInfo.name,ge=x(),fe=k({username:"",password:"",captchaCode:"",rememberPassword:!1}),he=C((()=>({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,message:"用户名至少需要2位字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线，不能输入中文",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码至少需要6位字符",trigger:"blur"}],captchaCode:[{required:!0,message:"请输入验证码",trigger:"blur"}]}))),ye=x(!1);ee();const ve=()=>c(this,null,(function*(){try{const e=yield f.getCaptcha();e&&e.data?(_.value=e.data.captchaKey,ae.value=e.data.captchaImage):e&&e.captchaKey?(_.value=e.captchaKey,ae.value=e.captchaImage):W.error(a("login.captchaFailed"))}catch(e){W.error(a("login.captchaFailed"))}})),be=()=>{ve()};O((()=>{ve()}));const _e=()=>c(this,null,(function*(){if(ge.value)try{if(!(yield ge.value.validate()))return;ye.value=!0;const{username:a,password:s,captchaCode:t}=fe,r={username:a,password:s,captchaKey:_.value,captchaCode:t},u=yield f.login(r);let d;if(u&&u.data)d=u.data;else{if(!u||!u.accessToken)throw new Error("登录失败 - 响应格式不正确");d=u}if(!d||!d.accessToken)throw new Error("登录失败 - 未收到有效的令牌");if(o.setToken(d.accessToken,d.refreshToken),d.user){const e=i(n({},d.user),{permissions:d.user.perms||[]});o.setUserInfo(e)}else o.setUserInfo({permissions:[],roles:[]});o.setLoginStatus(!0);const{resetRouterState:p}=yield h((()=>c(this,null,(function*(){const{resetRouterState:e}=yield import("./index-C5Q-N6Yp.js").then((e=>e.Z));return{resetRouterState:e}}))),__vite__mapDeps([0,1,2]),import.meta.url);p(l);const{useMenuStore:m}=yield h((()=>c(this,null,(function*(){const{useMenuStore:e}=yield import("./index-C5Q-N6Yp.js").then((e=>e.Y));return{useMenuStore:e}}))),__vite__mapDeps([0,1,2]),import.meta.url),g=m();try{yield g.forceRefreshMenu()}catch(e){}yield X(),we(),setTimeout((()=>{l.push(y)}),150)}catch(e){e instanceof v||W.error(e instanceof Error?e.message:a("login.failed")),ve()}finally{ye.value=!1}})),we=()=>{setTimeout((()=>{const e=o.info.name||o.info.username||fe.username;G({title:a("login.success.title"),type:"success",duration:2500,zIndex:1e4,message:`${a("login.success.message")}, ${e}!`})}),150)},{locale:je}=u(),xe=e=>{je.value!==e&&(je.value=e,o.setLanguage(e))};return(e,a)=>{const s=A,t=I,r=L,o=b,l=z,n=$,i=Z,c=H,u=N;return T(),S("div",se,[E("div",te,[E("div",re,[P(r,{onCommand:xe,"popper-class":"langDropDownStyle"},{dropdown:V((()=>[P(t,null,{default:V((()=>[(T(!0),S(R,null,D(M(g),(e=>(T(),S("div",{key:e.value,class:"lang-btn-item"},[P(s,{command:e.value,class:U({"is-selected":M(je)===e.value})},{default:V((()=>[E("span",oe,K(e.label),1),M(je)===e.value?(T(),S("i",le,"")):q("",!0)])),_:2},1032,["command","class"])])))),128))])),_:1})])),default:V((()=>[a[3]||(a[3]=E("div",{class:"btn language-btn"},[E("i",{class:"iconfont-sys icon-language"},"")],-1))])),_:1,__:[3]})]),E("div",ne,[E("div",ie,[P(o)]),E("span",null,K(M(me)),1)]),E("p",ce,K(e.$t("login.subTitle")),1),E("div",ue,[P(u,{ref_key:"formRef",ref:ge,model:M(fe),rules:M(he),"label-position":"top",class:"form",disabled:M(ye),"hide-required-asterisk":!0},{default:V((()=>[P(n,{label:"用户名",prop:"username"},{default:V((()=>[P(l,{modelValue:M(fe).username,"onUpdate:modelValue":a[0]||(a[0]=e=>M(fe).username=e),placeholder:"请输入用户名",maxlength:"20"},null,8,["modelValue"])])),_:1}),P(n,{label:"密码",prop:"password"},{default:V((()=>[P(l,{modelValue:M(fe).password,"onUpdate:modelValue":a[1]||(a[1]=e=>M(fe).password=e),type:"password",placeholder:"请输入密码",maxlength:"20","show-password":"",autocomplete:"new-password"},null,8,["modelValue"])])),_:1}),P(n,{label:"验证码",prop:"captchaCode"},{default:V((()=>[P(l,{modelValue:M(fe).captchaCode,"onUpdate:modelValue":a[2]||(a[2]=e=>M(fe).captchaCode=e),placeholder:"验证码",maxlength:"4",onKeyup:B(_e,["enter"])},{suffix:V((()=>[E("div",{class:"captcha-image",onClick:be,title:"点击刷新验证码"},[M(ae)?(T(),S("img",{key:0,src:M(ae),alt:"验证码",draggable:"false"},null,8,de)):(T(),F(i,{key:1,class:"loading-icon"},{default:V((()=>[P(M(Y))])),_:1}))])])),_:1},8,["modelValue"])])),_:1}),E("div",pe,[P(c,{type:"primary",loading:M(ye),onClick:_e},{default:V((()=>a[4]||(a[4]=[J(" 登录 ")]))),_:1,__:[4]},8,["loading"])])])),_:1},8,["model","rules","disabled"])])]),a[5]||(a[5]=E("div",{class:"bg"},[E("img",{src:Q,alt:"background"})],-1)),a[6]||(a[6]=E("a",{href:"https://gitee.com/Moon__Sun",class:"copyright"}," Copyright © Sun ",-1))])}}})),[["__scopeId","data-v-802011f0"]]);export{me as default};
