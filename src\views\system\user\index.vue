<template>
  <div class="page-content user-management-container">
    <!-- PC端搜索栏 -->
    <div v-if="!isMobile" class="pc-search-bar">
      <ElRow :gutter="12">
        <ElCol :xs="24" :sm="14" :md="16" :lg="18" :xl="20">
          <ElInput v-model="formFilters.keyword" placeholder="用户名、昵称" @keyup.enter="handleSearch"
            @clear="handleClearSearch" @input="handleInputChange" clearable></ElInput>
        </ElCol>
        <ElCol :xs="24" :sm="10" :md="8" :lg="6" :xl="4" class="el-col2">
          <ElButton v-ripple @click="handleSearch">搜索</ElButton>
          <ElButton @click="showDialog('add')" v-if="hasCreatePermission" v-ripple>新增用户</ElButton>
        </ElCol>
      </ElRow>
    </div>

    <!-- 移动端搜索栏 -->
    <div v-if="isMobile" class="mobile-search-bar">
      <div class="search-input-wrapper">
        <ElInput v-model="formFilters.keyword" placeholder="搜索用户名、昵称" @keyup.enter="handleSearch"
          @clear="handleClearSearch" @input="handleInputChange" clearable class="search-input">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </ElInput>
        <ElButton type="primary" @click="handleSearch" class="search-button">
          <el-icon>
            <Search />
          </el-icon>
        </ElButton>
      </div>
    </div>

    <!-- 网络状态提示 -->
    <ElAlert v-if="!networkStatus.isOnline" type="error" :title="networkErrorMessage" show-icon class="network-alert"
      :closable="false">
      <template #default>
        <p class="network-message">{{ networkRetryDescription }}</p>
        <ElButton size="small" @click="handleManualRetry" type="primary">手动重试</ElButton>
      </template>
    </ElAlert>

    <!-- PC端表格 -->
    <ArtTable v-if="!isMobile" :data="tableData" index :index-fixed="false" v-loading="loading"
      :currentPage="pagination.currentPage" :pageSize="pagination.pageSize" :total="pagination.total"
      @size-change="handleSizeChange" @current-change="handleCurrentChange">
      <template #default>
        <ElTableColumn prop="avatar" label="用户信息" :min-width="width < 500 ? 180 : 250">
          <template #default="scope">
            <div class="user" style="display: flex; align-items: center">
              <img class="avatar"
                :src="scope.row.avatar || 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'"
                :style="width < 500 ? 'width: 35px; height: 35px;' : ''"
                onerror="this.src='https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg';" />
              <div :style="width < 500 ? 'margin-left: 6px;' : ''">
                <p class="user-name">{{ scope.row.username }}</p>
                <p class="nickname">{{ scope.row.nickname || '-' }}</p>
              </div>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="role" label="角色" width="150">
          <template #default="scope">
            <ElTag v-if="scope.row.roles && scope.row.roles.length"
              :type="scope.row.roles[0] === 'superadmin' ? 'danger' : scope.row.roles[0] === 'admin' ? 'warning' : 'info'">
              {{ getRoleDisplayName(scope.row.roles[0]) }}
            </ElTag>
            <span v-else>-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" width="100">
          <template #default="scope">
            <ElTag :type="(scope.row.status || 'active') === 'active' ? 'success' : 'danger'" effect="dark">
              {{ (scope.row.status || 'active') === 'active' ? '正常' : '已禁用' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="createdAt" label="创建时间" sortable min-width="140" class-name="created-time-column">
          <template #default="scope">
            <div class="created-time-cell">
              {{ formatDate(scope.row.createdAt) }}
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn fixed="right" label="操作" :width="width < 768 ? 140 : 150">
          <template #default="scope">
            <div class="operation-cell">
              <!-- 启用/禁用开关 -->
              <ElSwitch :model-value="scope.row.status === 'active'"
                @update:model-value="() => updateUserStatus(scope.row.userId, scope.row.status || 'active')"
                active-color="#13ce66" inactive-color="#ff4949" class="status-switch" :disabled="!isAdmin || scope.row.userId === userStore.getUserInfo.id ||
                  scope.row.roles.includes('superadmin') ||
                  (scope.row.roles.includes('admin') && !isSuperAdmin)" />

              <!-- 更多操作按钮 -->
              <ElDropdown trigger="click">
                <el-button link class="more-button">更多</el-button>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem @click="showDialog('edit', scope.row)"
                      :disabled="!isAdmin || scope.row.roles.includes('superadmin')">
                      编辑
                    </ElDropdownItem>
                    <ElDropdownItem
                      v-if="isSuperAdmin && scope.row.userId !== userStore.getUserInfo.id && !scope.row.roles.includes('superadmin')"
                      @click="showPasswordDialog(scope.row)">
                      修改密码
                    </ElDropdownItem>
                    <ElDropdownItem @click="deleteUser(scope.row.userId)" :disabled="!isAdmin || scope.row.userId === userStore.getUserInfo.id ||
                      (scope.row.roles.includes('admin') && !isSuperAdmin) ||
                      scope.row.roles.includes('superadmin')">
                      删除
                    </ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </div>
          </template>
        </ElTableColumn>
      </template>
    </ArtTable>

    <!-- 移动端卡片列表 -->
    <div v-if="isMobile" class="mobile-card-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="mobile-skeleton">
        <div v-for="i in 3" :key="i" class="mobile-skeleton-card">
          <el-skeleton animated>
            <template #template>
              <div class="skeleton-header">
                <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
                <div class="skeleton-content">
                  <el-skeleton-item variant="text" style="width: 60%; height: 16px;" />
                  <el-skeleton-item variant="text" style="width: 40%; height: 14px;" />
                </div>
              </div>
              <el-skeleton-item variant="rect" style="width: 100%; height: 60px; margin-top: 12px;" />
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- 用户卡片列表 -->
      <transition-group name="card-list" tag="div" class="card-container" v-if="!loading">
        <div v-for="(user, index) in tableData" :key="user.userId" class="user-card">
          <!-- 卡片头部 -->
          <div class="card-header-mobile">
            <div class="user-info">
              <div class="user-avatar">
                <img :src="user.avatar || 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'"
                  :alt="user.username"
                  @error="$event.target.src = 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'" />
              </div>
              <div class="user-details">
                <div class="user-name">{{ user.username }}</div>
                <div class="user-nickname">{{ user.nickname || '-' }}</div>
              </div>
            </div>
            <div class="user-status">
              <el-tag :type="(user.status || 'active') === 'active' ? 'success' : 'danger'" size="small">
                {{ (user.status || 'active') === 'active' ? '正常' : '已禁用' }}
              </el-tag>
            </div>
          </div>

          <!-- 用户信息 -->
          <div class="user-content">
            <div class="info-section">
              <div class="info-item">
                <span class="info-label">角色</span>
                <span class="info-value">
                  <el-tag v-if="user.roles && user.roles.length"
                    :type="user.roles[0] === 'superadmin' ? 'danger' : user.roles[0] === 'admin' ? 'warning' : 'info'"
                    size="small">
                    {{ getRoleDisplayName(user.roles[0]) }}
                  </el-tag>
                  <span v-else>-</span>
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value">{{ formatDate(user.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="card-footer">
            <div class="action-buttons-mobile">
              <!-- 启用/禁用开关 -->
              <div class="switch-wrapper">
                <span class="switch-label">状态</span>
                <el-switch :model-value="user.status === 'active'"
                  @update:model-value="() => updateUserStatus(user.userId, user.status || 'active')"
                  active-color="#13ce66" inactive-color="#ff4949" :disabled="!isAdmin || user.userId === userStore.getUserInfo.id ||
                    user.roles.includes('superadmin') ||
                    (user.roles.includes('admin') && !isSuperAdmin)" />
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button size="small" type="primary" plain @click="showDialog('edit', user)"
                  :disabled="!isAdmin || user.roles.includes('superadmin')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  <span>编辑</span>
                </el-button>
                <el-button size="small" type="danger" plain @click="deleteUser(user.userId)" :disabled="!isAdmin || user.userId === userStore.getUserInfo.id ||
                  (user.roles.includes('admin') && !isSuperAdmin) ||
                  user.roles.includes('superadmin')">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  <span>删除</span>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- 空状态 -->
      <div v-if="!loading && tableData.length === 0" class="empty-state">
        <div class="empty-icon">👤</div>
        <div class="empty-text">暂无用户数据</div>
        <el-button type="primary" @click="showDialog('add')" v-if="hasCreatePermission" class="empty-action">
          <el-icon>
            <Plus />
          </el-icon>
          <span>新增用户</span>
        </el-button>
      </div>
    </div>

    <!-- 移动端底部操作栏 -->
    <div v-if="isMobile" class="mobile-action-bar">
      <div class="action-bar-container">
        <!-- 新增按钮 -->
        <div class="action-item primary-action" @click="showDialog('add')" v-if="hasCreatePermission">
          <div class="action-icon add-icon">
            <el-icon :size="24">
              <Plus />
            </el-icon>
          </div>
          <span class="action-label">新增</span>
        </div>

        <!-- 刷新按钮 -->
        <div class="action-item" @click="getUserList">
          <div class="action-icon refresh-icon">
            <el-icon :size="20">
              <Refresh />
            </el-icon>
          </div>
          <span class="action-label">刷新</span>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-show="!isMobile || !isSearching">
      <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]" :total="pagination.total"
        :layout="width < 768 ? 'prev, pager, next, sizes' : 'total, sizes, prev, pager, next, jumper'"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" :small="width < 768"
        :pager-count="width < 768 ? 5 : 7" background />
    </div>

    <ElDialog v-model="dialogVisible" :title="dialogType === 'add' ? '添加用户' : '编辑用户'" width="30%" align-center>
      <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px">
        <ElFormItem label="用户名" prop="username">
          <ElInput v-model="formData.username" @blur="handleInputBlur('username')" />
        </ElFormItem>
        <ElFormItem label="昵称" prop="nickname">
          <ElInput v-model="formData.nickname" @blur="handleInputBlur('nickname')" />
        </ElFormItem>
        <ElFormItem label="密码" prop="password" v-if="dialogType === 'add'">
          <ElInput v-model="formData.password" type="password" show-password @blur="handleInputBlur('password')" />
        </ElFormItem>
        <ElFormItem label="角色" prop="role">
          <ElSelect v-model="formData.role" placeholder="请选择角色" @change="handleInputBlur('role')">
            <ElOption v-for="role in availableRoles" :key="role.name" :value="role.name"
              :label="getRoleDisplayName(role.name)" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="头像" prop="avatar">
          <ElInput v-model="formData.avatar" placeholder="头像URL地址" clearable @blur="handleInputBlur('avatar')">
            <template #append>
              <ElTooltip content="点击预览头像" placement="top">
                <ElButton @click="previewAvatar">
                  <ElIcon>
                    <Picture />
                  </ElIcon>
                </ElButton>
              </ElTooltip>
            </template>
          </ElInput>
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="dialogVisible = false">取消</ElButton>
          <ElButton type="primary" @click="handleSubmit">提交</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 修改密码对话框 -->
    <ElDialog v-model="passwordDialogVisible" title="重置用户密码" width="500px" align-center>
      <div v-loading="passwordLoading" :element-loading-text="loadingText">
        <!-- 步骤指示器 -->
        <ElSteps :active="passwordStep" finish-status="success" style="margin-bottom: 20px;">
          <ElStep title="验证管理员密码" />
          <ElStep title="生成安全码" />
          <ElStep title="重置密码" />
        </ElSteps>

        <!-- 第一步：验证管理员密码 -->
        <div v-if="passwordStep === 0" class="admin-password-step">
          <ElAlert title="安全验证" type="info" description="为了确保安全，请输入您当前的登录密码来验证身份" show-icon :closable="false"
            style="margin-bottom: 20px;" />

          <ElForm ref="adminPasswordFormRef" :model="adminPasswordForm" :rules="adminPasswordRules" label-width="120px">
            <ElFormItem label="目标用户">
              <ElInput :value="selectedUserForPassword?.username" disabled />
            </ElFormItem>
            <ElFormItem label="管理员密码" prop="adminPassword">
              <ElInput v-model="adminPasswordForm.adminPassword" type="password" placeholder="请输入您的当前登录密码" show-password
                @keyup.enter="handleGenerateSecurityCode" />
            </ElFormItem>
          </ElForm>
        </div>

        <!-- 第二步：显示安全码 -->
        <div v-if="passwordStep === 1" class="security-code-step">
          <ElAlert title="安全提醒" type="warning" :description="securityCodeData.warning" show-icon :closable="false"
            style="margin-bottom: 20px;" />

          <ElAlert v-if="securityCodeData.securityNotice" title="验证成功" type="success"
            :description="securityCodeData.securityNotice" show-icon :closable="false" style="margin-bottom: 20px;" />

          <ElForm label-width="100px">
            <ElFormItem label="目标用户">
              <ElInput :value="securityCodeData.targetUser?.username" disabled />
            </ElFormItem>
            <ElFormItem label="安全码">
              <div class="security-code-display">
                <ElInput :value="securityCodeData.securityCode" disabled class="security-code-input" />
                <ElButton @click="copySecurityCode" type="primary" size="small" style="margin-left: 10px;">
                  复制
                </ElButton>
              </div>
            </ElFormItem>
            <ElFormItem label="有效期">
              <ElInput :value="securityCodeData.validUntil" disabled />
            </ElFormItem>
          </ElForm>
        </div>

        <!-- 第三步：重置密码 -->
        <div v-if="passwordStep === 2">
          <ElAlert title="安全提示" type="warning" description="为了增强安全性，需要再次输入您的管理员密码进行二次确认" show-icon :closable="false"
            style="margin-bottom: 20px;" />

          <ElForm ref="passwordFormRef" :model="passwordFormData" :rules="passwordRules" label-width="120px">
            <ElFormItem label="目标用户" prop="username">
              <ElInput v-model="passwordFormData.username" disabled />
            </ElFormItem>
            <ElFormItem label="安全码" prop="securityCode">
              <ElInput v-model="passwordFormData.securityCode" placeholder="请手动输入安全码（不支持自动填充）" :disabled="false"
                autocomplete="off" />
            </ElFormItem>
            <ElFormItem label="新密码" prop="newPassword">
              <ElInput v-model="passwordFormData.newPassword" type="password" placeholder="请输入新密码" show-password
                autocomplete="new-password" />
            </ElFormItem>
            <ElFormItem label="确认密码" prop="confirmPassword">
              <ElInput v-model="passwordFormData.confirmPassword" type="password" placeholder="请再次输入新密码" show-password
                autocomplete="new-password" />
            </ElFormItem>
            <ElFormItem label="管理员密码" prop="adminPassword">
              <ElInput v-model="passwordFormData.adminPassword" type="password" placeholder="请输入您的当前登录密码进行二次确认"
                show-password autocomplete="current-password" />
            </ElFormItem>
          </ElForm>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="closePasswordDialog">取消</ElButton>
          <ElButton v-if="passwordStep === 0" type="primary" @click="handleGenerateSecurityCode"
            :loading="passwordLoading">
            验证并生成安全码
          </ElButton>
          <ElButton v-if="passwordStep === 1" type="primary" @click="nextStep">下一步</ElButton>
          <ElButton v-if="passwordStep === 2" @click="prevStep">上一步</ElButton>
          <ElButton v-if="passwordStep === 2" type="primary" @click="handlePasswordSubmit" :loading="passwordLoading">
            重置密码
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { h, nextTick, ref as vueRef, computed, onMounted, onUnmounted } from 'vue'
import { ElDialog, FormInstance } from 'element-plus'
import { ElMessageBox, ElMessage, ElTag, ElAlert, ElTooltip, ElIcon, ElSwitch, ElDropdown, ElDropdownMenu, ElDropdownItem, ElSteps, ElStep } from 'element-plus'
import { Picture, Search, User, Edit, Delete, Plus, Refresh } from '@element-plus/icons-vue'
import type { FormRules } from 'element-plus'
import { useCheckedColumns } from '@/composables/useCheckedColumns'
import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
import { UserManageService, UserItem, GenerateSecurityCodeData } from '@/api/userManageApi'
import { SearchChangeParams, SearchFormItem } from '@/types'
import { useWindowSize } from '@/composables/useWindowSize'
import { RoleService, Role } from '@/api/roleApi'
import { useNetworkStatus } from '@/composables/useNetworkStatus'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'User' }) // 定义组件名称，用于 KeepAlive 缓存控制

const userStore = useUserStore()

// 判断当前用户权限
const isAdmin = computed(() => {
  const userRoles = userStore.getUserInfo.roles || []
  return userRoles.includes('admin') || userRoles.includes('superadmin')
})

const isSuperAdmin = computed(() => {
  const userRoles = userStore.getUserInfo.roles || []
  return userRoles.includes('superadmin')
})

// 判断是否有创建用户权限（管理员和超级管理员可以创建）
const hasCreatePermission = computed(() => {
  return isAdmin.value
})

// 根据当前用户角色获取可分配的角色列表
const availableRoles = computed(() => {
  if (isSuperAdmin.value) {
    // 超级管理员可以分配管理员和普通用户角色
    return roleList.value.filter((role) => role.name !== 'superadmin')
  } else if (isAdmin.value) {
    // 普通管理员只能分配普通用户角色
    return roleList.value.filter((role) => role.name === 'user')
  }
  return []
})

const dialogType = ref('add')
const dialogVisible = ref(false)
const loading = ref(false)
const { width } = useWindowSize()
const loadingFailed = ref(false)

// 移动端检测
const isMobile = vueRef(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 修改密码对话框相关
const passwordDialogVisible = ref(false)
const passwordLoading = ref(false)
const passwordStep = ref(0) // 0: 验证管理员密码, 1: 显示安全码, 2: 重置密码
const loadingText = ref('正在获取用户信息...')
const passwordFormRef = ref<FormInstance>()
const adminPasswordFormRef = ref<FormInstance>()

// 管理员密码验证表单
const adminPasswordForm = reactive({
  adminPassword: ''
})

// 选中的用户信息（用于密码重置）
const selectedUserForPassword = ref<any>(null)

// 安全码数据
const securityCodeData = reactive({
  securityCode: '',
  targetUser: {
    userId: '',
    username: '',
    nickname: ''
  },
  validUntil: '',
  warning: ''
})

// 密码表单数据
const passwordFormData = reactive({
  userId: '',
  username: '',
  securityCode: '',
  newPassword: '',
  confirmPassword: '',
  adminPassword: ''
})

// 网络状态监测
const networkStatus = useNetworkStatus({
  maxRetries: 3,
  retryInterval: 5000,
  onReconnect: () => {
    // 网络恢复时重新加载数据
    ElMessage.success('网络已恢复，重新加载数据')
    getUserList()
  },
  onOffline: () => {
    // 网络已断开，等待重新连接
    loadingFailed.value = true
  }
})

// 网络错误信息
const networkErrorMessage = computed(() => '网络连接已断开')
const networkRetryDescription = computed(() => {
  if (networkStatus.retryCount.value < networkStatus.maxRetries) {
    return `正在尝试重新连接 (${networkStatus.retryCount.value}/${networkStatus.maxRetries})...`
  } else {
    return '已达到最大重试次数，请检查网络后手动刷新'
  }
})

// 手动重试
const handleManualRetry = async () => {
  const isOnline = await networkStatus.checkNetworkConnection()
  if (isOnline) {
    getUserList()
  } else {
    ElMessage.error('网络仍然不可用，请稍后再试')
  }
}

// 角色名称中英文映射
const roleNameMap: Record<string, string> = {
  user: '普通用户',
  admin: '管理员',
  superadmin: '超级管理员'
}

// 获取角色中文名称
const getRoleDisplayName = (roleName: string): string => {
  return roleNameMap[roleName] || roleName
}

// 定义表单搜索初始值
const initialSearchState = {
  keyword: ''
}

const roleList = ref<Role[]>([])

// 响应式表单数据
const formFilters = reactive({ ...initialSearchState })

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const tableData = ref<UserItem[]>([])

// 搜索状态
const isSearching = ref(false)

// 重置表单
const handleReset = () => {
  Object.assign(formFilters, { ...initialSearchState })
  pagination.currentPage = 1 // 重置到第一页
  isSearching.value = false // 重置搜索状态
  getUserList()
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1 // 搜索时重置到第一页
  // 设置搜索状态：如果有搜索关键词则为搜索状态
  isSearching.value = !!(formFilters.keyword && formFilters.keyword.trim())
  getUserList()
}

// 清空搜索处理
const handleClearSearch = () => {
  isSearching.value = false // 清空搜索状态
  pagination.currentPage = 1 // 重置到第一页
  getUserList()
}

// 输入变化处理 - 自动检测空值并重置
let inputTimer: NodeJS.Timeout | null = null
const handleInputChange = (value: string) => {
  // 清除之前的定时器
  if (inputTimer) {
    clearTimeout(inputTimer)
  }

  // 如果输入为空，延迟500ms后自动重置
  if (!value || value.trim() === '') {
    inputTimer = setTimeout(() => {
      if (!formFilters.keyword || formFilters.keyword.trim() === '') {
        isSearching.value = false
        pagination.currentPage = 1
        getUserList()
      }
    }, 500)
  }
}

// 表单项变更处理
const handleFormChange = (params: SearchChangeParams): void => {
  // 处理表单变更
}

// 简化的表单配置项 - 只保留用户名搜索
const formItems: SearchFormItem[] = [
  {
    label: '关键词',
    prop: 'keyword',
    type: 'input',
    config: {
      clearable: true,
      placeholder: '用户名、昵称'
    },
    onChange: handleFormChange
  }
]

// 显示对话框
const showDialog = async (type: string, row?: UserItem) => {
  // 检查是否有权限
  if (!isAdmin.value) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  dialogType.value = type

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }

  if (type === 'edit' && row) {
    try {
      loading.value = true
      // 获取用户详细信息
      const userDetail = await UserManageService.getUserDetail(row.userId)

      // 填充表单数据
      formData.username = userDetail.username
      formData.nickname = userDetail.nickname || ''
      formData.role = userDetail.roles ? userDetail.roles[0] : ''
      formData.avatar =
        userDetail.avatar || 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'
      formData.userId = userDetail.userId
      formData.password = '' // 编辑时不需要修改密码，清空密码字段

      loading.value = false
      dialogVisible.value = true
    } catch (error) {
      ElMessage.error('获取用户详情失败')
      loading.value = false
    }
  } else {
    formData.username = ''
    formData.nickname = ''
    formData.role = ''
    formData.avatar = 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'
    formData.userId = ''
    formData.password = '' // 清空密码字段
    dialogVisible.value = true
  }
}

// 删除用户
const deleteUser = (userId: string) => {
  // 检查权限
  if (!isAdmin.value) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  // 检查是否尝试删除自己
  const currentUserId = userStore.getUserInfo.id
  if (userId === currentUserId) {
    ElMessage.error('不能删除当前登录的用户')
    return
  }

  // 检查网络连接
  if (!networkStatus.isOnline.value) {
    ElMessage.error('网络连接已断开，无法执行此操作')
    return
  }

  ElMessageBox.confirm('确定要删除该用户吗？', '删除用户', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(async () => {
      try {
        const response = await UserManageService.deleteUser(userId)
        ElMessage.success(response.message || '删除用户成功')
        getUserList() // 刷新列表
      } catch (error: any) {

        // 处理特定错误情况
        let errorMessage = '删除用户失败'
        if (error.response) {
          switch (error.response.status) {
            case 400:
              errorMessage = '无效的用户ID或尝试删除当前登录用户'
              break
            case 401:
              errorMessage = '未授权，请重新登录'
              break
            case 403:
              errorMessage = '权限不足，只有超级管理员可以删除管理员账号'
              break
            case 404:
              errorMessage = '用户不存在'
              break
          }
        }

        ElMessage.error(errorMessage)
      }
    })
    .catch(() => {
      // 用户取消删除操作
    })
}

// 动态列配置
const { columnChecks, columns } = useCheckedColumns(() => [
  { type: 'selection' }, // 勾选列
  {
    prop: 'avatar',
    label: '用户信息',
    minWidth: width.value < 500 ? 180 : 250,
    formatter: (row: UserItem) => {
      return h('div', { class: 'user', style: 'display: flex; align-items: center' }, [
        h('img', {
          class: 'avatar',
          src: row.avatar || 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg',
          onerror:
            "this.src='https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg';",
          style: width.value < 500 ? 'width: 35px; height: 35px;' : ''
        }),
        h(
          'div',
          {
            style: width.value < 500 ? 'margin-left: 6px;' : ''
          },
          [
            h('p', { class: 'user-name' }, row.username),
            h('p', { class: 'nickname' }, row.nickname || '-')
          ]
        )
      ])
    }
  },
  {
    prop: 'role',
    label: '角色',
    width: 150,
    formatter: (row: UserItem) => {
      if (!row.roles || !row.roles.length) return '-'

      // 获取角色名称（现在只有一个角色）
      const roleName = row.roles[0]
      const displayName = getRoleDisplayName(roleName)
      const type =
        roleName === 'superadmin' ? 'danger' : roleName === 'admin' ? 'warning' : 'info'

      return h(ElTag, { type }, () => displayName)
    }
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row: UserItem) => {
      const status = row.status || 'active'
      const isActive = status === 'active'

      return h(
        ElTag,
        {
          type: isActive ? 'success' : 'danger',
          effect: 'dark'
        },
        () => isActive ? '正常' : '已禁用'
      )
    }
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    sortable: true,
    formatter: (row: UserItem) => formatDate(row.createdAt)
  },
  {
    prop: 'operation',
    label: '操作',
    width: width.value < 768 ? 120 : 150,
    formatter: (row: UserItem) => {
      // 当前登录用户ID
      const currentUserId = userStore.getUserInfo.id

      // 检查是否是超级管理员
      const isSuperAdminUser = row.roles.includes('superadmin')
      // 检查是否是管理员
      const isAdminUser = row.roles.includes('admin')
      // 检查是否是当前用户
      const isCurrentUser = row.userId === currentUserId
      // 获取用户状态
      const status = row.status || 'active'
      const isActive = status === 'active'

      // 不允许编辑或删除超级管理员，除非当前用户也是超级管理员
      if (isSuperAdminUser && !isSuperAdmin.value) {
        return '-'
      }

      return h('div', { class: 'operation-buttons' }, [
        // 启用/禁用开关
        h(ElSwitch, {
          modelValue: isActive,
          'onUpdate:modelValue': () => updateUserStatus(row.userId, status),
          activeColor: '#13ce66',
          inactiveColor: '#ff4949',
          class: 'status-switch',
          disabled: !isAdmin.value || isCurrentUser || isSuperAdminUser || (isAdminUser && !isSuperAdmin.value)
        }),
        // 更多操作按钮（下拉菜单）
        h(ElDropdown, {
          trigger: 'click'
        }, {
          default: () => h(ArtButtonTable, {
            type: 'more',
            class: 'more-button'
          }),
          dropdown: () => h(ElDropdownMenu, {}, [
            // 编辑按钮
            h(ElDropdownItem, {
              onClick: () => showDialog('edit', row),
              disabled: !isAdmin.value || isSuperAdminUser
            }, { default: () => '编辑' }),
            // 修改密码按钮（只有超级管理员可见，且不能修改自己和其他超级管理员的密码）
            ...(isSuperAdmin.value && !isCurrentUser && !isSuperAdminUser ? [
              h(ElDropdownItem, {
                onClick: () => showPasswordDialog(row)
              }, { default: () => '修改密码' })
            ] : []),
            // 删除按钮
            h(ElDropdownItem, {
              onClick: () => deleteUser(row.userId),
              disabled: !isAdmin.value || isCurrentUser || (isAdminUser && !isSuperAdmin.value) || isSuperAdminUser
            }, { default: () => '删除' })
          ])
        })
      ])
    }
  }
])

// 表单实例
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  userId: '',
  username: '',
  nickname: '',
  avatar: 'https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg',
  password: '',
  role: '' // 单个角色
})

onMounted(() => {
  getUserList()
  getRoleList()
})

// 获取用户列表数据
const getUserList = async () => {
  // 检查网络连接
  if (!networkStatus.isOnline.value) {
    loadingFailed.value = true
    return
  }

  loading.value = true
  loadingFailed.value = false

  try {
    const response = await UserManageService.getUserList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: formFilters.keyword
    })

    if (response) {
      // 复制数组，避免直接修改原始响应数据
      let items = [...(response.items || [])]

      // 对用户列表进行排序，使超级管理员排在最前面
      items.sort((a, b) => {
        // 检查是否是超级管理员
        const isASuperAdmin = a.roles && a.roles.includes('superadmin')
        const isBSuperAdmin = b.roles && b.roles.includes('superadmin')

        // 检查是否是管理员
        const isAAdmin = a.roles && a.roles.includes('admin')
        const isBAdmin = b.roles && b.roles.includes('admin')

        // 超级管理员优先
        if (isASuperAdmin && !isBSuperAdmin) return -1
        if (!isASuperAdmin && isBSuperAdmin) return 1

        // 其次是管理员
        if (isAAdmin && !isBAdmin) return -1
        if (!isAAdmin && isBAdmin) return 1

        // 其他情况按创建时间排序
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      })

      // 确保排序后的数据被应用到表格
      tableData.value = [...items]
      pagination.total = response.total || 0
      pagination.currentPage = response.page || 1
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    tableData.value = []
    pagination.total = 0

    // 检查是否是网络错误
    networkStatus.checkNetworkConnection().then((online) => {
      if (!online) {
        loadingFailed.value = true
      }
    })
  } finally {
    loading.value = false
  }
}

// 获取角色列表
const getRoleList = async () => {
  // 检查网络连接
  if (!networkStatus.isOnline.value) {
    return
  }

  try {
    const response = await RoleService.getRoles({
      page: 1,
      limit: 100
    })

    if (response && response.items) {
      roleList.value = response.items
    }
  } catch (error) {
    roleList.value = []

    // 检查是否是网络错误
    networkStatus.checkNetworkConnection()
  }
}

const handleRefresh = () => {
  getUserList()
}

// 表单验证规则
const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        const chineseRegex = /[\u4e00-\u9fa5]/
        if (chineseRegex.test(value)) {
          callback(new Error('用户名不能包含中文字符'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: dialogType.value === 'add', message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 15, message: '密码长度在 6 到 15 个字符', trigger: 'blur' }
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }]
})

// 管理员密码验证规则
const adminPasswordRules = reactive<FormRules>({
  adminPassword: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { min: 6, max: 15, message: '密码长度在 6 到 15 个字符', trigger: 'blur' }
  ]
})

// 修改密码表单验证规则
const passwordRules = reactive<FormRules>({
  securityCode: [
    { required: true, message: '请输入安全码', trigger: 'blur' },
    { len: 8, message: '安全码长度为8位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 15, message: '密码长度在 6 到 15 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordFormData.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  adminPassword: [
    { required: true, message: '请输入管理员密码进行二次确认', trigger: 'blur' },
    { min: 6, max: 15, message: '密码长度在 6 到 15 个字符', trigger: 'blur' }
  ]
})

// 在输入框失去焦点时去除前后空格
const handleInputBlur = (field: keyof typeof formData) => {
  if (typeof formData[field] === 'string') {
    formData[field] = (formData[field] as string).trim()
  }
}

// 提交表单
const handleSubmit = async () => {
  // 检查网络连接
  if (!networkStatus.isOnline.value) {
    ElMessage.error('网络连接已断开，无法提交表单')
    return
  }

  if (!formRef.value) return

  // 检查角色分配权限
  if (!isSuperAdmin.value && formData.role === 'admin') {
    ElMessage.error('您没有权限分配管理员角色')
    return
  }

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 创建新用户
          await UserManageService.createUser({
            username: formData.username,
            password: formData.password,
            nickname: formData.nickname,
            avatar: formData.avatar,
            roles: [formData.role]
          })
          ElMessage.success('添加用户成功')
        } else {
          // 更新用户信息
          const response = await UserManageService.updateUser({
            userId: formData.userId,
            username: formData.username,
            nickname: formData.nickname,
            avatar: formData.avatar,
            roles: [formData.role]
          })
          ElMessage.success('更新用户成功')

          // 处理需要重新登录的情况
          if (response.needRelogin) {
            ElMessageBox.confirm('由于您修改了关键信息，需要重新登录才能生效', '需要重新登录', {
              confirmButtonText: '立即登录',
              cancelButtonText: '稍后处理',
              type: 'warning' as const
            })
              .then(() => {
                // 跳转到登录页面或调用登出接口
                userStore.logOut()
              })
              .catch(() => {
                // 用户选择稍后处理，不做额外操作
              })
          }
        }
        dialogVisible.value = false
        getUserList() // 刷新列表
      } catch (error: any) {
        let errorMessage = dialogType.value === 'add' ? '添加用户失败' : '更新用户失败'

        // 处理特定错误情况
        if (error.response) {
          switch (error.response.status) {
            case 400:
              errorMessage = '请求参数错误或用户名已存在'
              break
            case 401:
              errorMessage = '未认证，请重新登录'
              break
            case 403:
              errorMessage = '权限不足或尝试创建超级管理员'
              break
          }
        }

        ElMessage.error(errorMessage)
      }
    }
  })
}

// 处理表格分页变化
const handleSizeChange = (newPageSize: number) => {
  pagination.pageSize = newPageSize
  getUserList()
}

const handleCurrentChange = (newCurrentPage: number) => {
  pagination.currentPage = newCurrentPage
  getUserList()
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    .replace(/\//g, '-')
}

// 预览头像
const previewAvatar = () => {
  if (!formData.avatar) {
    ElMessage.warning('请先输入头像URL')
    return
  }

  // 使用Element Plus的图片预览组件
  ElMessageBox.alert(
    `<div style="text-align:center"><img src="${formData.avatar}" style="max-width: 100%; max-height: 300px;" onerror="this.src='https://i.postimg.cc/5NWj5zbn/9abd44496b816f0d39ffdc417c85a585.jpg'; this.onerror=null;"></div>`,
    '头像预览',
    {
      dangerouslyUseHTMLString: true,
      showConfirmButton: false,
      showCancelButton: true,
      cancelButtonText: '关闭'
    }
  )
}

// 更新用户状态（启用/禁用）
const updateUserStatus = async (userId: string, currentStatus: string) => {
  // 检查权限
  if (!isAdmin.value) {
    ElMessage.error('您没有权限执行此操作')
    return
  }

  // 检查是否尝试操作自己
  const currentUserId = userStore.getUserInfo.id
  if (userId === currentUserId) {
    ElMessage.error('不能修改当前登录用户的状态')
    return
  }

  // 检查网络连接
  if (!networkStatus.isOnline.value) {
    ElMessage.error('网络连接已断开，无法执行此操作')
    return
  }

  // 确定新状态（切换当前状态）
  const newStatus: 'active' | 'disabled' = currentStatus === 'disabled' ? 'active' : 'disabled'
  const actionText = newStatus === 'disabled' ? '禁用' : '启用'

  ElMessageBox.confirm(`确定要${actionText}该用户吗？`, `${actionText}用户`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: newStatus === 'disabled' ? 'warning' : 'info'
  })
    .then(async () => {
      try {
        const response = await UserManageService.updateUserStatus({
          userId,
          status: newStatus
        })
        ElMessage.success(response.message || `${actionText}用户成功`)
        getUserList() // 刷新列表
      } catch (error: any) {

        // 处理特定错误情况
        let errorMessage = `${actionText}用户失败`
        if (error.response) {
          switch (error.response.status) {
            case 400:
              errorMessage = '无效的状态值'
              break
            case 401:
              errorMessage = '未授权，请重新登录'
              break
            case 403:
              errorMessage = '权限不足，不能修改超级管理员的状态'
              break
            case 404:
              errorMessage = '用户不存在'
              break
          }
        }

        ElMessage.error(errorMessage)
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 显示修改密码对话框
const showPasswordDialog = async (row: UserItem) => {
  // 只有超级管理员可以修改密码
  if (!isSuperAdmin.value) {
    ElMessage.error('您没有权限重置用户密码')
    return
  }

  // 不能修改自己的密码
  const currentUserId = userStore.getUserInfo.id
  if (row.userId === currentUserId) {
    ElMessage.error('不能重置当前登录用户的密码')
    return
  }

  // 不能修改超级管理员的密码
  const isSuperAdminUser = row.roles.includes('superadmin')
  if (isSuperAdminUser) {
    ElMessage.error('不能重置超级管理员的密码')
    return
  }

  try {
    // 重置步骤和数据
    passwordStep.value = 0
    resetPasswordFormData()
    resetSecurityCodeData()
    resetAdminPasswordForm()

    // 设置选中的用户信息
    selectedUserForPassword.value = {
      userId: row.userId,
      username: row.username,
      nickname: row.nickname
    }

    // 填充表单基本信息
    passwordFormData.userId = row.userId
    passwordFormData.username = row.username

    passwordDialogVisible.value = true
  } catch (error: any) {
    let errorMessage = '生成安全码失败'

    // request工具的错误处理
    if (error.response?.status) {
      switch (error.response.status) {
        case 400:
          errorMessage = '请求参数错误'
          break
        case 403:
          errorMessage = '权限不足，只有超级管理员可以操作'
          break
        case 404:
          errorMessage = '目标用户不存在'
          break
        case 500:
          errorMessage = '服务器错误'
          break
      }
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
    passwordLoading.value = false
  }
}

// 重置密码表单数据
const resetPasswordFormData = () => {
  Object.assign(passwordFormData, {
    userId: '',
    username: '',
    securityCode: '',
    newPassword: '',
    confirmPassword: '',
    adminPassword: ''
  })
}

// 重置安全码数据
const resetSecurityCodeData = () => {
  Object.assign(securityCodeData, {
    securityCode: '',
    targetUser: {
      userId: '',
      username: '',
      nickname: ''
    },
    validUntil: '',
    warning: ''
  })
}

// 下一步
const nextStep = () => {
  passwordStep.value = 2
  // 根据新的安全要求，不自动填充安全码，需要手动输入
  // passwordFormData.securityCode = securityCodeData.securityCode
}

// 上一步
const prevStep = () => {
  passwordStep.value = 1
}

// 复制安全码
const copySecurityCode = async () => {
  try {
    await navigator.clipboard.writeText(securityCodeData.securityCode)
    ElMessage.success('安全码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 关闭修改密码对话框
const closePasswordDialog = () => {
  passwordDialogVisible.value = false
  passwordStep.value = 0
  resetPasswordFormData()
  resetSecurityCodeData()
  resetAdminPasswordForm()
  selectedUserForPassword.value = null

  // 重置表单验证状态
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  if (adminPasswordFormRef.value) {
    adminPasswordFormRef.value.resetFields()
  }
}

// 生成安全码（需要管理员密码验证）
const handleGenerateSecurityCode = async () => {
  if (!adminPasswordFormRef.value) return

  await adminPasswordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordLoading.value = true
      loadingText.value = '正在验证管理员密码并生成安全码...'

      try {
        // 调用生成安全码接口，需要管理员密码验证
        const data = await UserManageService.generateResetSecurityCode(
          selectedUserForPassword.value.userId,
          {
            adminPassword: adminPasswordForm.adminPassword
          }
        )

        // 填充安全码数据
        Object.assign(securityCodeData, {
          securityCode: data.securityCode,
          targetUser: data.targetUser,
          validUntil: data.validUntil,
          warning: data.warning,
          securityNotice: data.securityNotice
        })

        // 进入下一步
        passwordStep.value = 1
        ElMessage.success('管理员密码验证成功，安全码已生成')
      } catch (error: any) {
        let errorMessage = '生成安全码失败'

        // request工具的错误处理
        if (error.response?.status) {
          switch (error.response.status) {
            case 400:
              errorMessage = error.response.data?.message || '管理员密码错误，无法生成安全码'
              break
            case 403:
              errorMessage = '权限不足，只有超级管理员可以操作'
              break
            case 404:
              errorMessage = '目标用户不存在'
              break
            case 500:
              errorMessage = '服务器错误，请稍后重试'
              break
            default:
              errorMessage = error.response.data?.message || '生成安全码失败'
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        ElMessage.error(errorMessage)
      } finally {
        passwordLoading.value = false
      }
    }
  })
}

// 重置管理员密码表单
const resetAdminPasswordForm = () => {
  adminPasswordForm.adminPassword = ''
  adminPasswordFormRef.value?.clearValidate()
}

// 提交密码重置
const handlePasswordSubmit = async () => {
  // 检查网络连接
  if (!networkStatus.isOnline.value) {
    ElMessage.error('网络连接已断开，无法重置密码')
    return
  }

  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordLoading.value = true
      loadingText.value = '正在重置密码...'

      try {
        // 调用重置密码接口（双重安全验证）
        const data = await UserManageService.resetUserPassword(passwordFormData.userId, {
          newPassword: passwordFormData.newPassword,
          confirmPassword: passwordFormData.confirmPassword,
          securityCode: passwordFormData.securityCode,
          adminPassword: passwordFormData.adminPassword
        })

        ElMessage.success(data.message || '密码重置成功')
        closePasswordDialog()
      } catch (error: any) {
        let errorMessage = '密码重置失败'

        // request工具的错误处理
        if (error.response?.status) {
          switch (error.response.status) {
            case 400:
              errorMessage = error.response.data?.message || '请求参数错误、验证失败或业务逻辑错误'
              break
            case 401:
              errorMessage = 'Token无效或已过期，请重新登录'
              break
            case 403:
              errorMessage = error.response.data?.message || '权限不足，只有超级管理员可以重置用户密码'
              break
            case 404:
              errorMessage = '目标用户不存在'
              break
            case 500:
              errorMessage = error.response.data?.message || '重置密码失败，服务器内部错误'
              break
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        ElMessage.error(errorMessage)
      } finally {
        passwordLoading.value = false
      }
    }
  })
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  // 清理输入定时器
  if (inputTimer) {
    clearTimeout(inputTimer)
    inputTimer = null
  }
})
</script>

<style lang="scss" scoped>
.page-content {
  .svg-icon {
    width: 1.8em;
    height: 1.8em;
    overflow: hidden;
    vertical-align: -8px;
    fill: currentcolor;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    width: 100%;

    :deep(.el-pagination) {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 5px;

      @media (max-width: 768px) {
        .el-pagination__sizes {
          margin-right: 0;
        }

        .el-pagination__jump {
          margin-left: 0;
        }

        .el-pager {
          margin: 0 5px;
        }
      }
    }
  }

  .el-col2 {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    justify-content: flex-start;

    @media (min-width: 576px) {
      margin-top: 0;
    }

    @media (min-width: 768px) and (max-width: 1199px) {
      gap: 6px;
      flex-wrap: nowrap;

      .el-button {
        padding: 8px 12px;
        font-size: 13px;
        white-space: nowrap;
        min-width: auto;
      }
    }

    @media (min-width: 1200px) {
      gap: 12px;
    }
  }

  .network-alert {
    margin: 16px 0;
  }

  .user {
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .user-name {
      font-size: 14px;
      font-weight: 500;
      margin: 0;
      line-height: 1.2;
    }

    .nickname {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin: 0;
      line-height: 1.2;
    }
  }

  .security-code-display {
    display: flex;
    align-items: center;
  }

  /* 操作列样式 */
  .operation-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;

    .status-switch {
      flex-shrink: 0;
    }

    .more-button {
      flex-shrink: 0;
      white-space: nowrap;
      min-width: auto;
      padding: 0 4px;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
      gap: 6px;

      .more-button {
        font-size: 12px;
        padding: 0 2px;
      }
    }
  }

  /* 创建时间列样式 */
  .created-time-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 确保创建时间列表头文字和排序图标不换行 */
  :deep(.created-time-column) {
    .el-table__cell {
      white-space: nowrap !important;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 表头样式 */
    .cell {
      white-space: nowrap !important;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    /* 排序图标样式 */
    .caret-wrapper {
      flex-shrink: 0;
      margin-left: 4px;
    }
  }
}

/* 移动端样式 */
@media screen and (max-width: 768px) {
  .user-management-container {
    padding-bottom: 100px;
    /* 为底部操作栏留出足够空间 */

    /* 移动端搜索栏样式 */
    .mobile-search-bar {
      margin-bottom: 16px;

      .search-input-wrapper {
        display: flex;
        gap: 8px;
        align-items: center;

        .search-input {
          flex: 1;

          :deep(.el-input__wrapper) {
            border-radius: 20px;
            padding: 0 16px;
          }

          :deep(.el-input__prefix) {
            color: #9ca3af;
          }
        }

        .search-button {
          width: 36px;
          height: 44px;
          min-width: 36px;
          border-radius: 50%;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          :deep(.el-icon) {
            margin: 0;
            font-size: 16px;
          }
        }
      }
    }

    /* 移动端卡片列表样式 */
    .mobile-card-list {
      margin-bottom: 120px;
      /* 为底部操作栏留出足够空间 */

      .mobile-skeleton {
        .mobile-skeleton-card {
          background: white;
          border-radius: 12px;
          padding: 16px;
          margin-bottom: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          .skeleton-header {
            display: flex;
            align-items: center;
            gap: 12px;

            .skeleton-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
        }
      }

      .card-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .user-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #f1f5f9;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .card-header-mobile {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;

            .user-avatar {
              width: 44px;
              height: 44px;
              border-radius: 50%;
              overflow: hidden;
              flex-shrink: 0;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .user-details {
              flex: 1;

              .user-name {
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 2px;
              }

              .user-nickname {
                font-size: 12px;
                color: #6b7280;
              }
            }
          }

          .user-status {
            flex-shrink: 0;
          }
        }

        .user-content {
          margin-bottom: 16px;

          .info-section {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #f8fafc;
              border-radius: 8px;

              .info-label {
                font-size: 14px;
                color: #6b7280;
                font-weight: 500;
              }

              .info-value {
                font-size: 14px;
                color: #374151;
                font-weight: 500;
              }
            }
          }
        }

        .card-footer {
          .action-buttons-mobile {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .switch-wrapper {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #f8fafc;
              border-radius: 8px;

              .switch-label {
                font-size: 14px;
                color: #6b7280;
                font-weight: 500;
              }
            }

            .action-buttons {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 8px;

              .el-button {
                font-size: 12px;
                padding: 8px 12px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .el-icon {
                  font-size: 14px;
                }

                span {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 12px;
          opacity: 0.5;
        }

        .empty-text {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 16px;
        }

        .empty-action {
          font-size: 14px;
        }
      }
    }

    /* 移动端底部操作栏样式 */
    .mobile-action-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
      padding: env(safe-area-inset-bottom, 0) 0 0 0;

      .action-bar-container {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 12px 20px;

        .action-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          padding: 8px 12px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          user-select: none;
          min-width: 60px;

          &:active {
            transform: scale(0.95);
          }

          .action-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &.add-icon {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              color: white;
              box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
            }

            &.refresh-icon {
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              color: #d97706;
              border: 1px solid #fbbf24;
            }
          }

          .action-label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
          }

          &.primary-action .action-label {
            color: #1e40af;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
