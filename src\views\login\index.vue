<template>
  <!-- No changes to template section -->
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref(null)
const loading = ref(false)

const handleLogin = async () => {
  try {
    await loginFormRef.value?.validate()
    loading.value = true

    // 调用登录API
    const res = await loginApi(loginForm)

    if (res.code === 0) {
      userStore.setToken(res.data.token, res.data.refreshToken)
      userStore.setUserInfo(res.data.userInfo)
      userStore.setLoginStatus(true)

      // 重置路由状态，确保重新加载路由
      resetRouterState(router)

      // 跳转到首页
      router.push('/')
      ElMessage.success('登录成功')
    } else {
      ElMessage.error(res.message || '登录失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('登录失败')
  } finally {
    loading.value = false
  }
}
</script>

<style>
/* No changes to style section */
</style>
