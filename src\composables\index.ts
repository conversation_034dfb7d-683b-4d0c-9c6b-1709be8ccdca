/**
 * Composables 统一导出
 * 提供项目中所有自定义Hooks的统一入口
 *
 * 注意：以下Hooks与VueUse有同名冲突，需要显式导入使用增强版本：
 * - usePermission (增强的权限管理)
 * - useLocalStorage (增强的本地存储)
 * - useSessionStorage (增强的会话存储)
 * - useDebounce (增强的防抖工具)
 * - useThrottle (增强的节流工具)
 * - useWindowSize (增强的窗口尺寸监听)
 *
 * 使用方式：
 * import { usePermission, useLocalStorage } from '@/composables'
 */

// 基础功能Hooks
export * from './useCommon'
export * from './useWindowSize'
export * from './useNetworkStatus'
export * from './useSystemInfo'
export * from './useTheme'

// 数据管理Hooks
export * from './useApi'
export * from './useTable'
export * from './useForm'
export * from './useStorage'

// 权限管理Hooks
export * from './usePermission'

// 工具类Hooks
export * from './useDebounce'

// 图表相关Hooks
export * from './useChart'

// 表格列管理Hooks
export * from './useCheckedColumns'

// 仪式感相关Hooks
export * from './useCeremony'

/**
 * 常用Hooks快捷导出
 * 提供最常用的Hooks的便捷访问
 */
export {
  // API相关
  useApi,
  useGet,
  usePost,
  usePut,
  useDelete
} from './useApi'

export {
  // 表格相关
  useTable
} from './useTable'

export {
  // 表单相关
  useForm,
  useSimpleForm
} from './useForm'

export {
  // 权限相关
  usePermission,
  useAuth as useAuthPermission,
  useButtonAuth
} from './usePermission'

export {
  // 存储相关
  useLocalStorage,
  useSessionStorage,
  StorageManager,
  useUserPreferences,
  useTempData,
  usePersistentData
} from './useStorage'

export {
  // 防抖节流相关
  useDebounce,
  useThrottle,
  useDebouncedValue,
  useThrottledValue,
  useSearchDebounce,
  useApiDebounce,
  useSubmitDebounce
} from './useDebounce'

/**
 * Hooks使用指南
 * 
 * 1. API相关Hooks:
 *    - useApi: 通用API请求Hook，提供加载状态、错误处理等
 *    - useGet/usePost/usePut/useDelete: 便捷的HTTP方法Hooks
 * 
 * 2. 表格相关Hooks:
 *    - useTable: 表格数据管理，包含分页、排序、搜索、删除等功能
 * 
 * 3. 表单相关Hooks:
 *    - useForm: 完整的表单管理，包含验证、提交、重置等
 *    - useSimpleForm: 简化的表单Hook，适用于基本表单操作
 * 
 * 4. 权限相关Hooks:
 *    - usePermission: 完整的权限管理系统
 *    - useAuth: 简化的权限检查
 *    - useButtonAuth: 按钮权限检查
 * 
 * 5. 存储相关Hooks:
 *    - useLocalStorage/useSessionStorage: 增强的存储Hooks
 *    - StorageManager: 存储管理器，提供统一的存储操作
 *    - useUserPreferences: 用户偏好设置
 * 
 * 6. 工具类Hooks:
 *    - useDebounce/useThrottle: 防抖和节流
 *    - useSearchDebounce: 搜索防抖
 *    - useWindowSize: 增强的窗口尺寸监听
 * 
 * 使用示例:
 * 
 * ```typescript
 * // API调用
 * const { data, loading, execute } = useGet('/api/users', {}, { immediate: true })
 * 
 * // 表格管理
 * const { tableData, loading, pagination, search } = useTable({
 *   api: (params) => UserService.getList(params),
 *   deleteApi: (id) => UserService.delete(id)
 * })
 * 
 * // 表单管理
 * const { formData, submit, validate } = useForm({
 *   submitApi: (data) => UserService.create(data),
 *   rules: { name: [{ required: true, message: '请输入姓名' }] }
 * })
 * 
 * // 权限检查
 * const { hasPermission } = usePermission()
 * const canEdit = hasPermission('user:edit')
 * 
 * // 防抖搜索
 * const { keyword, search } = useSearchDebounce(async (keyword) => {
 *   await searchUsers(keyword)
 * })
 * ```
 */
