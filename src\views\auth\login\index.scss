@use '@styles/variables.scss' as *;

$color: #2c3142;

.page-login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  position: relative;
  background-color: #fff;
  color: $color;
  overflow: hidden; // 隐藏滚动条，确保页面不会出现滚动条

  .bg {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 90%;
    pointer-events: none;
    transform: rotate(180deg) scaleY(-1);

    img {
      height: 100%;
      width: 100%;
    }
  }

  .copyright {
    position: absolute;
    bottom: 15px;
    left: 0;
    text-align: center;
    width: 100%;
    color: var(--el-color-info);
    font-size: 14px;
    user-select: none;
    text-decoration: none;
  }

  .box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh; // 使用视窗高度
    width: 50%;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 9;
    overflow: hidden; // 隐藏溢出内容，防止滚动条
    box-sizing: border-box; // 确保padding和border包含在尺寸内

    .top-right-wrap {
      position: fixed;
      top: 30px;
      right: 30px;
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px; // 调整间距，让按钮稍微挨近一点

      .btn {
        display: flex; // 使用flex布局确保内容居中
        align-items: center;
        justify-content: center;
        padding: 5px;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s;
        min-width: 28px; // 确保按钮有最小宽度
        min-height: 28px; // 确保按钮有最小高度

        i {
          font-size: 18px;
        }

        &:hover {
          color: var(--main-color) !important;
        }
      }
    }

    .logo {
      height: 50px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      user-select: none;

      .icon {
        border-radius: 8px;
        padding: 5px;
        margin-right: 10px;
        background-color: transparent;

        img {
          height: 36px;
        }
      }

      span {
        font-size: 38px;
        font-weight: bold;
        line-height: 1;
        letter-spacing: 3px;
      }
    }

    .desc {
      font-size: 15px;
      letter-spacing: 1px;
      margin-bottom: 50px;
      user-select: none;
      max-width: 80%;
      text-align: center;
    }

    .form {
      width: 300px;
      max-height: calc(100vh - 200px); // 限制表单最大高度，为logo和描述预留空间
      overflow: visible; // 允许验证提示显示

      :deep(.el-form) {
        .el-form-item {
          position: relative; // 为绝对定位的错误提示提供定位基准
          margin-bottom: 15px; // 减少间距，避免内容溢出
        }

        .el-form-item__label {
          color: var(--el-color-info);
          padding-left: 5px;
          user-select: none;
        }

        // 简洁的验证错误提示样式
        .el-form-item__error {
          position: absolute; // 使用绝对定位，不占用文档流空间
          top: 100%; // 定位在表单项底部
          left: 5px;
          z-index: 10;
          color: #f56565;
          font-size: 12px;
          line-height: 1.3;
          font-weight: 400;
          white-space: nowrap; // 防止文字换行

          // 丝滑进入动画
          animation: fadeInSlide 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          opacity: 0;
          transform: translateY(-8px);
        }

        .el-input {
          box-sizing: border-box;
          font-size: 15px;
          border: 0;
          border-radius: 0;
          background-color: #f8f8f8;
          padding: 0 5px;
          border-radius: 8px;
          position: relative;

          &__wrapper {
            box-shadow: none;
            background-color: transparent;
          }

          &__inner {
            height: 45px;
            color: #333;
          }

          &:-webkit-autofill {
            -webkit-box-shadow: 0 0 0 1000px #f8f8f8 inset;
            box-shadow: 0 0 0 1000px #f8f8f8 inset;
          }
        }
      }

      // 验证码样式 - 复刻cool-admin-vue-8.x
      :deep(.captcha-image) {
        position: absolute;
        right: -5px;
        top: 0;
      }
    }

    .op {
      display: flex;
      justify-content: center;
      margin-top: 40px;

      :deep(.el-button) {
        height: 45px;
        width: 100%;
        font-size: 16px;
        border-radius: 8px;
        letter-spacing: 1px;
      }
    }
  }

  // 响应式设计
  @media screen and (max-width: 1024px) {
    .box {
      width: 100%;
      overflow: hidden; // 移动端也隐藏滚动条

      // 移动端按钮位置调整，保持原始大小
      .top-right-wrap {
        top: 20px;
        right: 20px;
      }

      .form {
        max-height: calc(100vh - 150px); // 移动端调整表单最大高度
      }
    }

    .bg {
      display: none;
    }
  }

  // 小屏幕设备进一步优化位置
  @media screen and (max-width: 768px) {
    .box {
      .top-right-wrap {
        top: 15px;
        right: 15px;
      }

      .form {
        max-height: calc(100vh - 120px); // 小屏幕进一步减少表单高度
        width: 280px; // 小屏幕适当减少表单宽度
      }
    }
  }
}

// 验证错误提示的丝滑动画
@keyframes fadeInSlide {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
