import{u as e}from"./index-C5Q-N6Yp.js";/* empty css                  *//* empty css                */import{k as a,r as t,O as s,C as r,x as l,D as o,U as i,S as n,a6 as d,b7 as p,u,X as _,a3 as c}from"./vendor-9ydHGNSq.js";import{_ as f}from"./_plugin-vue_export-helper-BCo6x5W8.js";const g={class:"page-content"},h=f(a({__name:"index",setup(a){const f=e(),h=t("https://element-plus.org/images/element-plus-logo.svg"),m=()=>{e().setWatermarkVisible(!f.watermarkVisible)};return(e,a)=>{const t=i,v=p,b=c;return r(),s("div",g,[l(v,{class:"card",shadow:"never"},{header:o((()=>a[0]||(a[0]=[d("基础文字水印")]))),default:o((()=>[l(t,{content:"Art Design Pro",font:{color:"rgba(128, 128, 128, 0.2)"}},{default:o((()=>a[1]||(a[1]=[n("div",{style:{height:"200px"}},null,-1)]))),_:1,__:[1]})])),_:1}),l(v,{class:"card",shadow:"never"},{header:o((()=>a[2]||(a[2]=[d("多行文字水印")]))),default:o((()=>[l(t,{content:["Art Design Pro","专注用户体验，视觉设计"],font:{fontSize:16,color:"rgba(128, 128, 128, 0.2)"}},{default:o((()=>a[3]||(a[3]=[n("div",{style:{height:"200px"}},null,-1)]))),_:1,__:[3]})])),_:1}),l(v,{class:"card",shadow:"never"},{header:o((()=>a[4]||(a[4]=[d("图片水印")]))),default:o((()=>[l(t,{image:u(h),opacity:.2,width:80,height:20},{default:o((()=>a[5]||(a[5]=[n("div",{style:{height:"200px"}},null,-1)]))),_:1,__:[5]},8,["image"])])),_:1}),l(v,{class:"card",shadow:"never"},{header:o((()=>a[6]||(a[6]=[d("自定义样式水印")]))),default:o((()=>[l(t,{content:"Art Design Pro",font:{fontSize:20,fontFamily:"Arial",color:"rgba(255, 0, 0, 0.3)"},rotate:-22,gap:[100,100]},{default:o((()=>a[7]||(a[7]=[n("div",{style:{height:"200px"}},null,-1)]))),_:1,__:[7]})])),_:1}),l(b,{type:u(f).watermarkVisible?"danger":"primary",onClick:m},{default:o((()=>[d(_(u(f).watermarkVisible?"隐藏全局水印":"显示全局水印"),1)])),_:1},8,["type"])])}}}),[["__scopeId","data-v-f276f597"]]);export{h as default};
