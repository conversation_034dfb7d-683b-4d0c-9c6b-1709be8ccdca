import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-tovWoMSq.js";import{k as s,r as t,O as d,C as i,S as e,bp as v,x as l,u as r}from"./vendor-9ydHGNSq.js";import{_ as o}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const c={class:"custom-card art-custom-card target-vs-reality"},n={class:"custom-card-body"},p=o(s({__name:"TargetVsReality",setup(s){const o=t(["周一","周二","周三","周四","周五","周六","周日"]),p=t([{name:"线上销售",data:[12,13,5,15,10,15,18]}]);return(s,t)=>{const b=a;return i(),d("div",c,[t[0]||(t[0]=e("div",{class:"custom-card-header"},[e("span",{class:"title"},"目标与实际")],-1)),e("div",n,[l(b,{height:"10rem",data:r(p),xAxisData:r(o),showAxisLine:!1,barWidth:"28%"},null,8,["data","xAxisData"])]),t[1]||(t[1]=v('<div class="custom-card-footer" data-v-923291b8><div class="total-item" data-v-923291b8><div class="label" data-v-923291b8><i class="iconfont-sys" data-v-923291b8></i><div class="label-text" data-v-923291b8><span data-v-923291b8>实际销售额</span><span data-v-923291b8>全球</span></div></div><div class="value text-color-green" data-v-923291b8>8,823</div></div><div class="total-item" data-v-923291b8><div class="label" data-v-923291b8><i class="iconfont-sys" data-v-923291b8></i><div class="label-text" data-v-923291b8><span data-v-923291b8>目标销售额</span><span data-v-923291b8>商业</span></div></div><div class="value text-color-orange" data-v-923291b8>12,122</div></div></div>',1))])}}}),[["__scopeId","data-v-923291b8"]]);export{p as default};
