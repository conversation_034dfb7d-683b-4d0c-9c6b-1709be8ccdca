import api from '@/utils/http'

// 随心换选项接口类型定义
export interface ChangeOption {
  _id: string
  name: string
  description: string
  price: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  __v?: number
}

// 获取随心换选项列表的参数
export interface GetChangeOptionsParams {
  isActive?: boolean
  sortBy?: string
  order?: string
}

// API响应类型
export interface ChangeOptionsResponse {
  code: number
  message: string
  data: ChangeOption[]
}

/**
 * 随心换选项API服务
 */
export class ChangeOptionsService {
  /**
   * 获取随心换选项列表
   */
  static async getChangeOptions(params?: GetChangeOptionsParams): Promise<ChangeOption[]> {
    try {
      const response = await api.get<ChangeOption[]>({
        url: '/change-options',
        params
      })

      // 如果响应是数组，直接返回
      if (Array.isArray(response)) {
        return response
      }

      // 如果响应是对象且包含data字段，返回data
      if (response && typeof response === 'object' && 'data' in response) {
        return (response as any).data || []
      }

      return []
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取激活的随心换选项列表
   */
  static async getActiveChangeOptions(): Promise<ChangeOption[]> {
    return this.getChangeOptions({ isActive: true })
  }

  /**
   * 根据ID获取随心换选项详情
   */
  static async getChangeOptionById(id: string): Promise<ChangeOption | null> {
    try {
      const options = await this.getChangeOptions()
      return options.find(option => option._id === id) || null
    } catch (error) {
      throw error
    }
  }

  /**
   * 创建随心换选项
   */
  static async createChangeOption(data: Partial<ChangeOption>): Promise<ChangeOption> {
    try {
      const response = await api.post<ChangeOption>({
        url: '/change-options',
        data,
        showErrorMessage: true
      })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 更新随心换选项
   */
  static async updateChangeOption(id: string, data: Partial<ChangeOption>): Promise<ChangeOption> {
    try {
      const response = await api.put<ChangeOption>({
        url: `/change-options/${id}`,
        data,
        showErrorMessage: true
      })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 删除随心换选项
   */
  static async deleteChangeOption(id: string): Promise<void> {
    try {
      await api.del<void>({
        url: `/change-options/${id}`,
        showErrorMessage: true
      })
    } catch (error) {
      throw error
    }
  }
}

export default ChangeOptionsService
