import"./index-C5Q-N6Yp.js";/* empty css                     */import{_ as r}from"./ArtTable-CBEgSUvC.js";/* empty css                    *//* empty css                        *//* empty css                    *//* empty css                  */import{d as e,i as a,a as o,b as t}from"./icon4-mCHIfvQe.js";import{k as s,aa as c,d as p,O as i,C as l,S as m,x as n,D as g,aJ as d,X as u,W as b,bq as f,ah as w}from"./vendor-9ydHGNSq.js";import{_ as v}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-DiJiIjk5.js";const j=""+new URL("icon5-BXFopm_n.webp",import.meta.url).href,k=""+new URL("icon6-WLywVSyq.webp",import.meta.url).href,y={class:"card art-custom-card",style:{height:"27.8rem"}},h={class:"table"},_={style:{display:"flex","align-items":"center"}},x=["src"],L={class:"product-info"},q={class:"product-name"},S={class:"product-category"},T={class:"price"},X=v(s({__name:"HotProductsList",setup(s){const v=c([{name:"智能手表 Pro",category:"电子设备",price:1299,stock:156,sales:423,percentage:75,pro:0,color:"rgb(var(--art-primary)) !important",image:e},{name:"无线蓝牙耳机",category:"音频设备",price:499,stock:89,sales:652,percentage:85,pro:0,color:"rgb(var(--art-success)) !important",image:a},{name:"机械键盘",category:"电脑配件",price:399,stock:12,sales:238,percentage:45,pro:0,color:"rgb(var(--art-warning)) !important",image:o},{name:"超薄笔记本电脑",category:"电子设备",price:5999,stock:0,sales:126,percentage:30,pro:0,color:"rgb(var(--art-error)) !important",image:t},{name:"智能音箱",category:"智能家居",price:799,stock:45,sales:321,percentage:60,pro:0,color:"rgb(var(--art-info)) !important",image:j},{name:"游戏手柄",category:"游戏配件",price:299,stock:78,sales:489,percentage:70,pro:0,color:"rgb(var(--art-secondary)) !important",image:k}]),X=r=>0===r?"缺货":r<20?"低库存":r<50?"适中":"充足";p((()=>{C()}));const C=()=>{setTimeout((()=>{for(let r=0;r<v.length;r++){let e=v[r];v[r].pro=e.percentage}}),100)};return(e,a)=>{const o=d,t=f,s=r,c=w;return l(),i("div",y,[a[0]||(a[0]=m("div",{class:"card-header"},[m("p",{class:"title"},"热销产品"),m("p",{class:"subtitle"},"本月销售情况")],-1)),m("div",h,[n(c,{style:{height:"21.55rem"}},{default:g((()=>[n(s,{data:v,pagination:!1,style:{"margin-top":"0 !important"},size:"large",border:!1,stripe:!1,"show-header-background":!1},{default:g((()=>[n(o,{label:"产品",prop:"product",width:"220px"},{default:g((r=>[m("div",_,[m("img",{class:"product-image",src:r.row.image},null,8,x),m("div",L,[m("div",q,u(r.row.name),1),m("div",S,u(r.row.category),1)])])])),_:1}),n(o,{label:"价格",prop:"price"},{default:g((r=>[m("span",T,"¥"+u(r.row.price.toLocaleString()),1)])),_:1}),n(o,{label:"库存",prop:"stock"},{default:g((r=>{return[m("div",{class:b(["stock-badge",(e=r.row.stock,0===e?"out-of-stock":e<20?"low-stock":e<50?"medium-stock":"in-stock")])},u(X(r.row.stock)),3)];var e})),_:1}),n(o,{label:"销量",prop:"sales"}),n(o,{label:"销售趋势",width:"240"},{default:g((r=>[n(t,{percentage:r.row.pro,color:r.row.color,"stroke-width":4},null,8,["percentage","color"])])),_:1})])),_:1},8,["data"])])),_:1})])])}}}),[["__scopeId","data-v-f480c318"]]);export{X as default};
