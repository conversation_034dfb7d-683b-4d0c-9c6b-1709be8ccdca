import{_ as a}from"./index-Degvv3hI.js";import{k as s,c as t,O as e,C as i,S as o,x as r,u as d}from"./vendor-9ydHGNSq.js";import{_ as c}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./index-DEP0vMzR.js";import"./useChart-CoPw7zBp.js";const n={class:"custom-card art-custom-card customer-satisfaction"},m={class:"custom-card-body"},p=c(s({__name:"CustomerSatisfaction",setup(s){const c=t((()=>[{name:"上个月",data:[65,72,68,75,82,78,85],areaStyle:{startOpacity:.08,endOpacity:0}},{name:"本月",data:[78,85,82,88,92,89,95],areaStyle:{startOpacity:.08,endOpacity:0}}])),p=["1","2","3","4","5","6","7"];return(s,t)=>{const l=a;return i(),e("div",n,[t[0]||(t[0]=o("div",{class:"custom-card-header"},[o("span",{class:"title"},"客户满意度")],-1)),o("div",m,[r(l,{height:"100%",data:d(c),xAxisData:p,showLegend:!0,showAxisLabel:!0,showAxisLine:!1,showSplitLine:!0},null,8,["data"])])])}}}),[["__scopeId","data-v-dd9350d6"]]);export{p as default};
