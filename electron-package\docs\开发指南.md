# Electron开发指南

## 🛠️ 开发环境配置

### 1. 系统要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 pnpm >= 7.0.0
- Windows 10/11 (64位)

### 2. 依赖安装
```bash
# 安装Electron相关依赖
pnpm add electron electron-builder -D

# 或使用提供的脚本
./scripts/安装Electron依赖.bat
```

## 📝 配置文件说明

### main.js - 主进程配置
```javascript
// 关键配置项
const mainWindow = new BrowserWindow({
  width: 1200,           // 窗口宽度
  height: 800,           // 窗口高度
  webPreferences: {
    nodeIntegration: true,      // 允许渲染进程使用Node.js
    contextIsolation: false,    // 禁用上下文隔离
    enableRemoteModule: true,   // 启用remote模块
  },
})
```

### electron-builder.json - 打包配置
```json
{
  "productName": "点点管理系统",    // 产品名称
  "files": ["./main.js", "./preload.js", "./web"],  // 打包文件
  "directories": {
    "output": "electron-dist"    // 输出目录
  },
  "win": {
    "target": "portable",        // 便携版
    "arch": ["x64"],            // 64位架构
    "icon": "icons/icon.ico"    // 应用图标
  }
}
```

## 🔄 开发流程

### 1. 开发模式
```bash
# 启动Vue开发服务器
npm run dev

# 修改main.js中的isDebug为true
let isDebug = true;

# 启动Electron
npx electron config/main.js
```

### 2. 生产模式测试
```bash
# 构建Vue项目
npm run build

# 修改main.js中的isDebug为false
let isDebug = false;

# 测试Electron应用
npx electron config/main.js
```

### 3. 打包发布
```bash
# 完整打包流程
npm run build
npx pnpm exec electron-builder build --config config/electron-builder.json
```

## 🎨 自定义配置

### 修改窗口属性
在 `config/main.js` 中修改：
```javascript
const mainWindow = new BrowserWindow({
  width: 1400,           // 自定义宽度
  height: 900,           // 自定义高度
  minWidth: 800,         // 最小宽度
  minHeight: 600,        // 最小高度
  resizable: true,       // 是否可调整大小
  frame: true,           // 是否显示边框
  titleBarStyle: 'default', // 标题栏样式
})
```

### 修改应用图标
1. 准备256x256的ico格式图标
2. 替换 `assets/icons/icon.ico`
3. 重新打包

### 修改应用信息
在 `config/electron-builder.json` 中修改：
```json
{
  "productName": "您的应用名称",
  "appId": "com.yourcompany.yourapp",
  "version": "1.0.0",
  "description": "应用描述"
}
```

## 🐛 调试技巧

### 1. 开启开发者工具
```javascript
// 在main.js中添加
mainWindow.webContents.openDevTools()
```

### 2. 控制台输出
```javascript
// 主进程调试
console.log('主进程信息:', data)

// 渲染进程调试（在Vue组件中）
console.log('渲染进程信息:', data)
```

### 3. 错误处理
```javascript
// 监听应用错误
app.on('window-all-closed', () => {
  console.log('所有窗口已关闭')
})

// 监听页面加载错误
mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
  console.error('页面加载失败:', errorCode, errorDescription)
})
```

## 📚 常用API

### 主进程API
```javascript
const { app, BrowserWindow, Menu, dialog } = require('electron')

// 应用生命周期
app.whenReady()
app.on('window-all-closed', callback)
app.on('activate', callback)

// 窗口管理
new BrowserWindow(options)
mainWindow.loadFile(path)
mainWindow.loadURL(url)

// 菜单和对话框
Menu.setApplicationMenu(menu)
dialog.showMessageBox(options)
```

### 渲染进程API
```javascript
// 如果启用了nodeIntegration
const { ipcRenderer } = require('electron')

// 进程间通信
ipcRenderer.send('message', data)
ipcRenderer.on('reply', callback)
```

## 🔧 性能优化

### 1. 减小包体积
- 使用 `files` 配置只打包必要文件
- 启用 `asar` 压缩
- 排除不必要的依赖

### 2. 启动优化
- 延迟加载非关键模块
- 使用 `show: false` 然后在 `ready-to-show` 时显示
- 预加载关键资源

### 3. 内存优化
- 及时清理事件监听器
- 避免内存泄漏
- 合理使用缓存

## 📖 参考资源

- [Electron官方文档](https://www.electronjs.org/docs)
- [electron-builder文档](https://www.electron.build/)
- [Vue3官方文档](https://vuejs.org/)
- [Vite官方文档](https://vitejs.dev/)
