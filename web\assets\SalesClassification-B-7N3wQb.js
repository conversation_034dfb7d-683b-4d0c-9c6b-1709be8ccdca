import{_ as s}from"./index-pwV1nyK9.js";import{_ as i}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as a,C as e,S as t,x as d,bp as n}from"./vendor-9ydHGNSq.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const o={class:"card art-custom-card sales-card",style:{height:"26rem"}};const r=i({},[["render",function(i,r){const c=s;return e(),a("div",o,[r[0]||(r[0]=t("div",{class:"card-header"},[t("p",{class:"title"},"销售分类"),t("p",{class:"subtitle"},"按产品类别")],-1)),d(c,{data:[{value:30,name:"电子产品"},{value:55,name:"服装鞋包"},{value:36,name:"家居用品"}],color:["#4C87F3","#EDF2FF","#8BD8FC"],radius:["70%","80%"],height:"16.5rem",showLabel:!1,borderRadius:0,centerText:"¥300,458"}),r[1]||(r[1]=n('<div class="icon-text-widget"><div class="item"><div class="icon"><i class="iconfont-sys"></i></div><div class="content"><p>¥500,458</p><span>总收入</span></div></div><div class="item"><div class="icon"><i class="iconfont-sys"></i></div><div class="content"><p>¥130,580</p><span>净利润</span></div></div></div>',1))])}]]);export{r as default};
