import { router } from '@/router'
import { App, Directive } from 'vue'
import { usePermission } from '@/composables/usePermission'

/**
 * 权限指令（兼容新旧权限系统）
 * 用法：
 * <el-button v-auth="'add'">按钮</el-button>
 */
const authDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 使用新的权限系统
    const { hasPermission } = usePermission()

    // 先检查新的权限系统
    if (hasPermission(binding.value)) {
      return
    }

    // 兼容旧的权限系统
    const authList = (router.currentRoute.value.meta.authList as Array<{ auth_mark: string }>) || []
    const hasOldPermission = authList.some((item) => item.auth_mark === binding.value)

    if (!hasOldPermission) {
      el.parentNode?.removeChild(el)
    }
  }
}

export function setupPermissionDirective(app: App) {
  app.directive('auth', authDirective)
}
