var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,l=(r,a,o)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[a]=o;import{c as i,A as n,R as d}from"./index-C5Q-N6Yp.js";/* empty css                *//* empty css                  *//* empty css                    */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_ as p}from"./index-C015bGm8.js";import{_ as u}from"./LoginLeftView-khd5UpK1.js";import{k as m,r as c,aa as g,O as f,x as v,S as w,X as h,u as b,D as y,ab as _,M as j,A as P,V as x,C as V,ac as $,$ as O,a0 as E,aF as k,a6 as L,a2 as T,B as q,a3 as A,E as M}from"./vendor-9ydHGNSq.js";import{_ as R}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./login-bg-Dqmxv51G.js";const U={class:"login register"},B={class:"right-wrap"},I={class:"header"},C={class:"login-wrap"},D={class:"form"},F={class:"title"},S={class:"sub-title"},J={style:{"margin-top":"15px"}},K={class:"footer"},Q=m((X=((e,r)=>{for(var a in r||(r={}))t.call(r,a)&&l(e,a,r[a]);if(o)for(var a of o(r))s.call(r,a)&&l(e,a,r[a]);return e})({},{name:"Register"}),r(X,a({__name:"index",setup(e){const{t:r}=i(),a=j(),o=c(),t=n.systemInfo.name,s=c(!1),l=g({username:"",password:"",confirmPassword:"",agreement:!1}),m=g({username:[{required:!0,message:r("register.placeholder[0]"),trigger:"blur"},{min:3,max:20,message:r("register.rule[2]"),trigger:"blur"}],password:[{required:!0,validator:(e,a,t)=>{var s;""===a?t(new Error(r("register.placeholder[1]"))):(""!==l.confirmPassword&&(null==(s=o.value)||s.validateField("confirmPassword")),t())},trigger:"blur"},{min:6,message:r("register.rule[3]"),trigger:"blur"}],confirmPassword:[{required:!0,validator:(e,a,o)=>{""===a?o(new Error(r("register.rule[0]"))):a!==l.password?o(new Error(r("register.rule[1]"))):o()},trigger:"blur"}],agreement:[{validator:(e,a,o)=>{a?o():o(new Error(r("register.rule[4]")))},trigger:"change"}]}),R=()=>{return e=this,r=null,a=function*(){if(o.value)try{yield o.value.validate(),s.value=!0,setTimeout((()=>{s.value=!1,M.success("注册成功"),Q()}),1e3)}catch(e){}},new Promise(((o,t)=>{var s=e=>{try{i(a.next(e))}catch(r){t(r)}},l=e=>{try{i(a.throw(e))}catch(r){t(r)}},i=e=>e.done?o(e.value):Promise.resolve(e.value).then(s,l);i((a=a.apply(e,r)).next())}));var e,r,a},Q=()=>{setTimeout((()=>{a.push(d.Login)}),1e3)};return(e,r)=>{const a=u,i=p,n=O,c=$,g=P("router-link"),j=k,M=A,Q=_,X=x("ripple");return V(),f("div",U,[v(a),w("div",B,[w("div",I,[v(i,{class:"icon"}),w("h1",null,h(b(t)),1)]),w("div",C,[w("div",D,[w("h3",F,h(e.$t("register.title")),1),w("p",S,h(e.$t("register.subTitle")),1),v(Q,{ref_key:"formRef",ref:o,model:b(l),rules:b(m),"label-position":"top"},{default:y((()=>[v(c,{prop:"username"},{default:y((()=>[v(n,{modelValue:b(l).username,"onUpdate:modelValue":r[0]||(r[0]=e=>b(l).username=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[0]")},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"password"},{default:y((()=>[v(n,{modelValue:b(l).password,"onUpdate:modelValue":r[1]||(r[1]=e=>b(l).password=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[1]"),type:"password",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"confirmPassword"},{default:y((()=>[v(n,{modelValue:b(l).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=e=>b(l).confirmPassword=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[2]"),type:"password",autocomplete:"off",onKeyup:E(R,["enter"]),"show-password":""},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"agreement"},{default:y((()=>[v(j,{modelValue:b(l).agreement,"onUpdate:modelValue":r[3]||(r[3]=e=>b(l).agreement=e)},{default:y((()=>[L(h(e.$t("register.agreeText"))+" ",1),v(g,{style:{color:"var(--main-color)","text-decoration":"none"},to:"/privacy-policy"},{default:y((()=>[L(h(e.$t("register.privacyPolicy")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1}),w("div",J,[T((V(),q(M,{class:"register-btn",type:"primary",onClick:R,loading:b(s)},{default:y((()=>[L(h(e.$t("register.submitBtnText")),1)])),_:1},8,["loading"])),[[X]])]),w("div",K,[w("p",null,[L(h(e.$t("register.hasAccount"))+" ",1),v(g,{to:b(d).Login},{default:y((()=>[L(h(e.$t("register.toLogin")),1)])),_:1},8,["to"])])])])),_:1},8,["model","rules"])])])])])}}}))));var X;const z=R(Q,[["__scopeId","data-v-4d7b77e6"]]);export{z as default};
