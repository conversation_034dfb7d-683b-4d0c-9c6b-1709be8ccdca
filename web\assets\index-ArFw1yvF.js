var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,i=(t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a;import"./index-C5Q-N6Yp.js";/* empty css                     */import{k as o,c,O as n,C as p,S as d,x as m,X as u,ah as b,Q as f,u as v,D as y,by as _,F as j,Z as O,B as x,R as h,bz as g}from"./vendor-9ydHGNSq.js";import{_ as C}from"./_plugin-vue_export-helper-BCo6x5W8.js";const P={class:"timeline-list-card"},w={class:"art-card art-custom-card"},k={class:"card-header"},D={class:"card-title"},I={class:"card-subtitle"},S={class:"timeline-item"},q={class:"timeline-content"},z={class:"timeline-text"},A={key:0,class:"timeline-code"},B=o((E=((e,t)=>{for(var s in t||(t={}))r.call(t,s)&&i(e,s,t[s]);if(a)for(var s of a(t))l.call(t,s)&&i(e,s,t[s]);return e})({},{name:"ArtTimelineListCard"}),t(E,s({__name:"index",props:{list:{},title:{default:""},subtitle:{default:""},maxCount:{default:5}},setup(e){const t=e,s=c((()=>65*t.maxCount+"px"));return(e,t)=>{const a=g,r=_,l=b;return p(),n("div",P,[d("div",w,[d("div",k,[d("p",D,u(e.title),1),d("p",I,u(e.subtitle),1)]),m(l,{style:f({height:v(s)})},{default:y((()=>[m(r,null,{default:y((()=>[(p(!0),n(j,null,O(e.list,(e=>(p(),x(a,{key:e.time,timestamp:e.time,placement:"top",color:e.status,center:!0},{default:y((()=>[d("div",S,[d("div",q,[d("span",z,u(e.content),1),e.code?(p(),n("span",A," #"+u(e.code),1)):h("",!0)])])])),_:2},1032,["timestamp","color"])))),128))])),_:1})])),_:1},8,["style"])])])}}}))));var E;const F=C(B,[["__scopeId","data-v-3ecb9149"]]);export{F as _};
