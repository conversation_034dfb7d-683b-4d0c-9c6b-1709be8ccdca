import{s as e,m as a}from"./index-C5Q-N6Yp.js";/* empty css                             *//* empty css                  */import{b as l}from"./hb-DcPUfgty.js";import{k as s,r as t,p as i,V as r,O as d,C as o,S as n,x as u,a2 as _,B as c,D as m,a6 as p,u as v,bi as b,bh as f,a3 as g}from"./vendor-9ydHGNSq.js";import{_ as k}from"./_plugin-vue_export-helper-BCo6x5W8.js";const C={class:"page-content"},j={class:"action-buttons"},x=k(s({__name:"index",setup(s){const k=t(null),x=t(!1),h=()=>{a.emit("triggerFireworks")},w=e=>{((e,l)=>{k.value&&(clearInterval(k.value),k.value=null),x.value=!0;let s=0;k.value=setInterval((()=>{a.emit("triggerFireworks",l),s++,s>=e&&(clearInterval(k.value),k.value=null,x.value=!1)}),1e3)})(10,e)},I=e=>{a.emit("triggerFireworks",e)};return i((()=>{k.value&&(clearInterval(k.value),k.value=null)})),(a,s)=>{const t=g,i=b,k=f,F=r("ripple");return o(),d("div",C,[n("div",j,[_((o(),c(t,{disabled:v(x),onClick:h},{default:m((()=>s[4]||(s[4]=[p("✨ 放个小烟花")]))),_:1,__:[4]},8,["disabled"])),[[F]]),_((o(),c(t,{disabled:v(x),onClick:s[0]||(s[0]=e=>I(v(l)))},{default:m((()=>s[5]||(s[5]=[p("🎉 打开幸运红包")]))),_:1,__:[5]},8,["disabled"])),[[F]]),_((o(),c(t,{disabled:v(x),onClick:s[1]||(s[1]=e=>w(""))},{default:m((()=>s[6]||(s[6]=[p("🎆 璀璨烟火秀")]))),_:1,__:[6]},8,["disabled"])),[[F]]),_((o(),c(t,{disabled:v(x),onClick:s[2]||(s[2]=a=>I(v(e)))},{default:m((()=>s[7]||(s[7]=[p("❄️ 飘点小雪花")]))),_:1,__:[7]},8,["disabled"])),[[F]]),_((o(),c(t,{disabled:v(x),onClick:s[3]||(s[3]=a=>w(v(e)))},{default:m((()=>s[8]||(s[8]=[p("❄️ 浪漫暴风雪")]))),_:1,__:[8]},8,["disabled"])),[[F]])]),u(k,{title:"礼花组件说明",direction:"vertical",column:1,border:"",style:{"margin-top":"50px"}},{default:m((()=>[u(i,{label:"显示时机"},{default:m((()=>s[9]||(s[9]=[p(" 礼花效果组件全局注册了，在节假日的时候，会自动显示，你可以通过配置文件来控制显示时机 ")]))),_:1,__:[9]}),u(i,{label:"礼花样式"},{default:m((()=>s[10]||(s[10]=[p(" 默认显示几何图形，可以配置图片，图片需要提前在 components/Ceremony/Fireworks 文件预先定义 ")]))),_:1,__:[10]}),u(i,{label:"节日配置"},{default:m((()=>s[11]||(s[11]=[p(" 在 src/config/festival.ts 文件中，可以配置节日和对应的礼花样式 ")]))),_:1,__:[11]}),u(i,{label:"快捷键"},{default:m((()=>s[12]||(s[12]=[p(" command + shift + p 或者 ctrl + shift + p ")]))),_:1,__:[12]})])),_:1})])}}}),[["__scopeId","data-v-8c0e1d7a"]]);export{x as default};
