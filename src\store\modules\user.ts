import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { LanguageEnum } from '@/enums/appEnum'
import { router } from '@/router'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { useMenuStore } from './menu'
import { AppRouteRecord } from '@/types/router'
import { setPageTitle } from '@/router/utils/utils'
import { resetRouterState } from '@/router/guards/beforeEach'
import { RoutesAlias } from '@/router/routesAlias'
import { UserService } from '@/api/usersApi'

// 用户
export const useUserStore = defineStore(
  'userStore',
  () => {
    const language = ref(LanguageEnum.ZH)
    const isLogin = ref(false)
    const isLock = ref(false)
    const lockPassword = ref('')
    const info = ref<Partial<Api.User.UserInfo>>({
      roles: [],
      permissions: []
    })
    const searchHistory = ref<AppRouteRecord[]>([])
    const accessToken = ref('')
    const refreshToken = ref('')

    const getUserInfo = computed(() => info.value)
    const getSettingState = computed(() => useSettingStore().$state)
    const getWorktabState = computed(() => useWorktabStore().$state)

    const setUserInfo = (newInfo: Api.User.UserInfo) => {
      // 确保perms字段存在
      if (!newInfo.perms) {
        newInfo.perms = []
      }

      // 检查用户角色是否发生变化
      const oldRoles = info.value.roles || []
      const newRoles = newInfo.roles || []
      const rolesChanged = JSON.stringify(oldRoles.sort()) !== JSON.stringify(newRoles.sort())

      info.value = newInfo

      // 如果角色发生变化，清理菜单缓存以便重新加载
      if (rolesChanged && isLogin.value) {
        const menuStore = useMenuStore()
        menuStore.setMenuList([])

        // 重置路由注册状态，强制重新注册路由
        import('@/router/guards/beforeEach').then(({ isRouteRegistered }) => {
          isRouteRegistered.value = false
        })
      }
    }

    const setLoginStatus = (status: boolean) => {
      isLogin.value = status
    }

    const setLanguage = (lang: LanguageEnum) => {
      setPageTitle(router.currentRoute.value)
      language.value = lang
    }

    const setSearchHistory = (list: AppRouteRecord[]) => {
      searchHistory.value = list
    }

    const setLockStatus = (status: boolean) => {
      isLock.value = status
    }

    const setLockPassword = (password: string) => {
      lockPassword.value = password
    }

    const setToken = (newAccessToken: string, newRefreshToken?: string) => {
      accessToken.value = newAccessToken
      if (newRefreshToken) {
        refreshToken.value = newRefreshToken
      }
    }

    // 从后端更新用户信息
    const updateUserInfo = async () => {
      try {
        if (isLogin.value && accessToken.value) {
          const response = await UserService.getUserInfo()
          if (response && response.data) {
            setUserInfo(response.data)
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    }

    const logOut = () => {
      // 清理用户信息
      info.value = {
        roles: [],
        permissions: [],
        perms: []
      }
      isLogin.value = false
      isLock.value = false
      lockPassword.value = ''
      accessToken.value = ''
      refreshToken.value = ''

      // 清理工作标签
      useWorktabStore().opened = []

      // 清理会话存储
      sessionStorage.removeItem('iframeRoutes')

      // 清理菜单缓存
      const menuStore = useMenuStore()
      menuStore.setMenuList([])

      // 重置路由状态
      resetRouterState(router)

      // 跳转到登录页
      router.push(RoutesAlias.Login)
    }

    return {
      language,
      isLogin,
      isLock,
      lockPassword,
      info,
      searchHistory,
      accessToken,
      refreshToken,
      getUserInfo,
      getSettingState,
      getWorktabState,
      setUserInfo,
      setLoginStatus,
      setLanguage,
      setSearchHistory,
      setLockStatus,
      setLockPassword,
      setToken,
      updateUserInfo,
      logOut
    }
  },
  {
    persist: {
      key: 'user',
      storage: localStorage
    }
  }
)
