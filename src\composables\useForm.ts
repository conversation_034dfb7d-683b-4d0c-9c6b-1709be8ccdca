import { ref, reactive, computed, nextTick, type Ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useApi, type UseApiOptions } from './useApi'

export interface UseFormOptions<T = any> {
  /** 初始表单数据 */
  initialData?: Partial<T>
  /** 表单验证规则 */
  rules?: FormRules
  /** 提交API函数 */
  submitApi?: (data: T) => Promise<any>
  /** 获取详情API函数 */
  getDetailApi?: (id: string | number) => Promise<T>
  /** 成功回调 */
  onSuccess?: (data: T, response: any) => void
  /** 错误回调 */
  onError?: (error: any) => void
  /** 提交前的数据处理 */
  beforeSubmit?: (data: T) => T | Promise<T>
  /** 是否显示成功消息 */
  showSuccessMessage?: boolean | string
  /** API选项 */
  apiOptions?: UseApiOptions<any>
}

export interface UseFormReturn<T = any> {
  /** 表单引用 */
  formRef: Ref<FormInstance | undefined>
  /** 表单数据 */
  formData: T
  /** 表单验证规则 */
  rules: Ref<FormRules>
  /** 提交加载状态 */
  submitting: Ref<boolean>
  /** 获取详情加载状态 */
  loading: Ref<boolean>
  /** 表单模式 */
  mode: Ref<'create' | 'edit'>
  /** 是否为编辑模式 */
  isEdit: Ref<boolean>
  /** 提交表单 */
  submit: () => Promise<void>
  /** 验证表单 */
  validate: () => Promise<boolean>
  /** 验证指定字段 */
  validateField: (prop: string) => Promise<boolean>
  /** 重置表单 */
  resetForm: () => void
  /** 清除验证 */
  clearValidate: (props?: string | string[]) => void
  /** 设置表单数据 */
  setFormData: (data: Partial<T>) => void
  /** 获取详情数据 */
  getDetail: (id: string | number) => Promise<void>
  /** 设置为创建模式 */
  setCreateMode: () => void
  /** 设置为编辑模式 */
  setEditMode: (id?: string | number) => Promise<void>
}

/**
 * 表单管理Hook
 * 提供表单验证、提交、重置等功能
 */
export function useForm<T extends Record<string, any> = any>(
  options: UseFormOptions<T> = {}
): UseFormReturn<T> {
  const {
    initialData = {} as Partial<T>,
    rules: formRules = {},
    submitApi,
    getDetailApi,
    onSuccess,
    onError,
    beforeSubmit,
    showSuccessMessage = true,
    apiOptions = {}
  } = options

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive<T>({ ...initialData } as T)

  // 表单验证规则
  const rules = ref<FormRules>(formRules)

  // 表单模式
  const mode = ref<'create' | 'edit'>('create')

  // 是否为编辑模式
  const isEdit = computed(() => mode.value === 'edit')

  // 提交API Hook
  const {
    loading: submitting,
    execute: executeSubmit
  } = useApi(
    async (data: T) => {
      if (!submitApi) {
        throw new Error('未配置提交API')
      }
      return submitApi(data)
    },
    {
      immediate: false,
      onSuccess: (response) => {
        const message = typeof showSuccessMessage === 'string' 
          ? showSuccessMessage 
          : (isEdit.value ? '更新成功' : '创建成功')
        
        if (showSuccessMessage) {
          ElMessage.success(message)
        }

        if (onSuccess) {
          onSuccess(formData, response)
        }
      },
      onError: (error) => {
        if (onError) {
          onError(error)
        }
      },
      ...apiOptions
    }
  )

  // 获取详情API Hook
  const {
    loading,
    execute: executeGetDetail
  } = useApi(
    async (id: string | number) => {
      if (!getDetailApi) {
        throw new Error('未配置获取详情API')
      }
      return getDetailApi(id)
    },
    {
      immediate: false,
      onSuccess: (response) => {
        setFormData(response)
      }
    }
  )

  // 验证表单
  const validate = async (): Promise<boolean> => {
    if (!formRef.value) return false

    try {
      await formRef.value.validate()
      return true
    } catch {
      return false
    }
  }

  // 验证指定字段
  const validateField = async (prop: string): Promise<boolean> => {
    if (!formRef.value) return false

    try {
      await formRef.value.validateField(prop)
      return true
    } catch {
      return false
    }
  }

  // 提交表单
  const submit = async () => {
    const isValid = await validate()
    if (!isValid) return

    try {
      let submitData = { ...formData }

      // 提交前的数据处理
      if (beforeSubmit) {
        submitData = await beforeSubmit(submitData)
      }

      await executeSubmit(submitData)
    } catch (error) {
      // 错误已在useApi中处理
    }
  }

  // 重置表单
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    
    // 重置为初始数据
    Object.keys(formData).forEach(key => {
      delete formData[key]
    })
    Object.assign(formData, { ...initialData })
  }

  // 清除验证
  const clearValidate = (props?: string | string[]) => {
    if (formRef.value) {
      formRef.value.clearValidate(props)
    }
  }

  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    Object.assign(formData, data)
  }

  // 获取详情数据
  const getDetail = async (id: string | number) => {
    await executeGetDetail(id)
  }

  // 设置为创建模式
  const setCreateMode = () => {
    mode.value = 'create'
    resetForm()
  }

  // 设置为编辑模式
  const setEditMode = async (id?: string | number) => {
    mode.value = 'edit'
    
    if (id && getDetailApi) {
      await getDetail(id)
    }
  }

  return {
    formRef,
    formData,
    rules,
    submitting,
    loading,
    mode,
    isEdit,
    submit,
    validate,
    validateField,
    resetForm,
    clearValidate,
    setFormData,
    getDetail,
    setCreateMode,
    setEditMode
  }
}

/**
 * 简化的表单Hook，用于基本的表单操作
 */
export function useSimpleForm<T extends Record<string, any> = any>(
  initialData: Partial<T> = {},
  rules: FormRules = {}
) {
  const formRef = ref<FormInstance>()
  const formData = reactive<T>({ ...initialData } as T)

  const validate = async (): Promise<boolean> => {
    if (!formRef.value) return false
    try {
      await formRef.value.validate()
      return true
    } catch {
      return false
    }
  }

  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    Object.keys(formData).forEach(key => {
      delete formData[key]
    })
    Object.assign(formData, { ...initialData })
  }

  const clearValidate = (props?: string | string[]) => {
    if (formRef.value) {
      formRef.value.clearValidate(props)
    }
  }

  return {
    formRef,
    formData,
    rules: ref(rules),
    validate,
    resetForm,
    clearValidate
  }
}
