<template>
  <div class="package-type-manager">
    <!-- PC端操作栏 -->
    <div class="action-bar" v-if="!isMobile">
      <el-button type="primary" size="default" @click="handleAdd" v-ripple class="add-button">
        <el-icon class="el-icon--left">
          <Plus />
        </el-icon><span class="btn-text">新增套餐</span>
      </el-button>
      <el-button class="refresh-button" :class="{ refreshing: isRefreshing }" @click="handleRefresh"
        :disabled="isRefreshing" circle v-ripple>
        <el-icon>
          <Refresh />
        </el-icon>
      </el-button>
    </div>

    <!-- 移动端顶部统计 -->
    <div v-if="isMobile" class="mobile-header">
      <div class="mobile-stats-card">
        <div class="stats-icon">
          <el-icon>
            <Camera />
          </el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ tableData.length }}</div>
          <div class="stats-label">套餐配置</div>
        </div>
      </div>
    </div>

    <!-- PC端表格 -->
    <ArtTable v-if="!isMobile" v-loading="loading" :data="tableData" style="margin-top: 10px">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="name" label="套餐名称" min-width="120" />
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
      <el-table-column prop="price" label="价格" width="100" align="center">
        <template #default="{ row }">
          <span class="price-text">¥{{ row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isActive" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isActive ? 'success' : 'danger'">
            {{ row.isActive ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)" v-ripple>
            编辑
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)" v-ripple>
            删除
          </el-button>
        </template>
      </el-table-column>
    </ArtTable>

    <!-- 移动端卡片列表 -->
    <div v-if="isMobile" class="mobile-card-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="mobile-skeleton">
        <div v-for="i in 3" :key="i" class="mobile-skeleton-card">
          <el-skeleton animated>
            <template #template>
              <div class="skeleton-header">
                <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
                <div class="skeleton-content">
                  <el-skeleton-item variant="text" style="width: 60%; height: 16px;" />
                  <el-skeleton-item variant="text" style="width: 40%; height: 14px;" />
                </div>
              </div>
              <el-skeleton-item variant="rect" style="width: 100%; height: 60px; margin-top: 12px;" />
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- 套餐卡片列表 -->
      <transition-group name="card-list" tag="div" class="card-container" v-if="!loading">
        <div v-for="(item, index) in tableData" :key="item.id" class="package-card">
          <!-- 卡片头部 -->
          <div class="card-header-mobile">
            <div class="package-info">
              <div class="package-icon">
                <el-icon>
                  <Camera />
                </el-icon>
              </div>
              <div class="package-details">
                <div class="package-name">{{ item.name }}</div>
                <div class="package-index">序号: {{ index + 1 }}</div>
              </div>
            </div>
            <div class="package-status">
              <el-tag :type="item.isActive ? 'success' : 'danger'" size="small">
                {{ item.isActive ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>

          <!-- 套餐信息 -->
          <div class="package-content">
            <div class="info-section">
              <div class="info-item">
                <span class="info-label">价格</span>
                <span class="info-value price-value">¥{{ item.price }}</span>
              </div>
              <div class="info-item description-item" v-if="item.description">
                <span class="info-label">描述</span>
                <span class="info-value description-text">{{ item.description }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="card-footer">
            <div class="action-buttons-mobile">
              <el-button size="small" type="primary" plain @click="handleEdit(item)">
                <el-icon>
                  <Edit />
                </el-icon>
                <span>编辑</span>
              </el-button>
              <el-button size="small" type="danger" plain @click="handleDelete(item)">
                <el-icon>
                  <Delete />
                </el-icon>
                <span>删除</span>
              </el-button>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- 空状态 -->
      <div v-if="!loading && tableData.length === 0" class="empty-state">
        <div class="empty-icon">📦</div>
        <div class="empty-text">暂无套餐配置</div>
        <el-button type="primary" @click="handleAdd" class="empty-action">
          <el-icon>
            <Plus />
          </el-icon>
          <span>新增套餐</span>
        </el-button>
      </div>
    </div>

    <!-- 移动端底部操作栏 -->
    <div v-if="isMobile" class="mobile-action-bar">
      <div class="action-bar-container">
        <!-- 新增按钮 -->
        <div class="action-item primary-action" @click="handleAdd">
          <div class="action-icon add-icon">
            <el-icon :size="24">
              <Plus />
            </el-icon>
          </div>
          <span class="action-label">新增</span>
        </div>

        <!-- 刷新按钮 -->
        <div class="action-item" @click="handleRefresh" :class="{ refreshing: isRefreshing }">
          <div class="action-icon refresh-icon">
            <el-icon :size="20">
              <Refresh />
            </el-icon>
          </div>
          <span class="action-label">刷新</span>
        </div>
      </div>
    </div>

    <!-- 表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑套餐类型' : '新增套餐类型'" width="500px"
      :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" class="config-form">
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入套餐名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入套餐描述" />
        </el-form-item>

        <el-form-item label="价格" prop="price">
          <el-input-number v-model="formData.price" :min="0" :precision="2" placeholder="请输入价格" style="width: 100%" />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-radio-group v-model="formData.isActive">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting" v-ripple>
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Plus, Refresh, Camera, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PackageTypesService, { type PackageType } from '@/api/packageTypesApi'
// 显式导入增强版本的Hooks
import { useTable, useForm, useDebounce } from '@/composables'

// 定义事件
const emit = defineEmits<{
  'data-changed': []
}>()

// 移动端检测
const isMobile = ref(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 刷新动画状态
const isRefreshing = ref(false)

// 处理刷新点击
const handleRefresh = async () => {
  if (isRefreshing.value) return

  isRefreshing.value = true
  try {
    await refresh()
    emit('data-changed')
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    // 确保动画至少显示1秒
    setTimeout(() => {
      isRefreshing.value = false
    }, 1000)
  }
}

// 创建防抖的刷新函数，避免频繁请求
const debouncedRefresh = useDebounce(async () => {
  try {
    await refresh()
    emit('data-changed')
  } catch (error) {
    // 静默处理刷新错误，避免影响用户体验
    console.warn('数据刷新被限制，但操作已成功完成')
  }
}, { delay: 1500 }) // 增加到1.5秒防抖

// 使用新的Hooks
const {
  tableData,
  loading,
  refresh
} = useTable({
  api: async () => {
    const data = await PackageTypesService.getPackageTypes()
    return { data, total: data.length }
  },
  deleteApi: (id: string) => PackageTypesService.deletePackageType(id),
  immediate: true,
  apiOptions: {
    noThrottle: true // 禁用节流，允许频繁刷新
  }
})

// 表单管理
const {
  formRef,
  formData,
  rules,
  submitting,
  submit,
  resetForm,
  setCreateMode,
  setEditMode
} = useForm<PackageType>({
  initialData: {
    name: '',
    description: '',
    price: 0,
    isActive: true
  },
  rules: {
    name: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
    description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    price: [{ required: true, message: '请输入价格', trigger: 'blur' }]
  },
  submitApi: (data) => {
    return formData._id
      ? PackageTypesService.updatePackageType(formData._id, data)
      : PackageTypesService.createPackageType(data)
  },
  onSuccess: () => {
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    debouncedRefresh()
  },
  showSuccessMessage: false
})

// 对话框状态
const dialogVisible = ref(false)
const isEdit = computed(() => !!formData._id)

// 操作处理
const handleAdd = () => {
  setCreateMode()
  dialogVisible.value = true
}

const handleEdit = (row: PackageType) => {
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row: PackageType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除套餐类型"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await PackageTypesService.deletePackageType(row._id)
    ElMessage.success('删除成功')
    debouncedRefresh()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  await submit()
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style lang="scss" scoped>
.package-type-manager {
  .action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .add-button {
      // 新增按钮保持自然宽度
    }

    .refresh-button {
      width: 36px;
      height: 36px;
      padding: 0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background: #f5f7fa;
      border: 1px solid #dcdfe6;
      color: #606266;
      transition: all 0.3s ease;

      .el-icon {
        font-size: 18px;
        transition: transform 0.6s ease;
      }

      &:hover {
        background: #ecf5ff;
        border-color: #b3d8ff;
        color: #409eff;
      }

      &.refreshing {
        .el-icon {
          animation: spin 1s linear infinite;
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
}

.price-text {
  color: #f56c6c;
  font-weight: 500;
}

.config-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 移动端样式 */
@media screen and (max-width: 768px) {
  .package-type-manager {
    padding-bottom: 100px;
    /* 为底部操作栏留出足够空间 */

    /* 移动端头部统计样式 */
    .mobile-header {
      margin-bottom: 16px;

      .mobile-stats-card {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 12px;
        border: 1px solid #bae6fd;

        .stats-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 18px;
        }

        .stats-content {
          flex: 1;

          .stats-number {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2px;
          }

          .stats-label {
            font-size: 13px;
            color: #6b7280;
            font-weight: 500;
          }
        }
      }
    }

    /* 移动端卡片列表样式 */
    .mobile-card-list {
      /* 确保最后一个卡片不被底部操作栏遮挡 */
      margin-bottom: 20px;
      padding-bottom: 100px;
      /* 为底部操作栏留出足够空间 */

      .mobile-skeleton {
        .mobile-skeleton-card {
          background: white;
          border-radius: 12px;
          padding: 16px;
          margin-bottom: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          .skeleton-header {
            display: flex;
            align-items: center;
            gap: 12px;

            .skeleton-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
        }
      }

      .card-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .package-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #f1f5f9;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .card-header-mobile {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .package-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;

            .package-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 18px;
            }

            .package-details {
              flex: 1;

              .package-name {
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 2px;
              }

              .package-index {
                font-size: 12px;
                color: #6b7280;
              }
            }
          }

          .package-status {
            flex-shrink: 0;
          }
        }

        .package-content {
          margin-bottom: 16px;

          .info-section {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #f8fafc;
              border-radius: 8px;

              .info-label {
                font-size: 14px;
                color: #6b7280;
                font-weight: 500;
              }

              .info-value {
                font-size: 14px;
                color: #374151;
                font-weight: 500;

                &.price-value {
                  color: #059669;
                  font-weight: 600;
                }
              }

              &.description-item {
                align-items: flex-start;

                .description-text {
                  text-align: right;
                  max-width: 60%;
                  word-break: break-all;
                  line-height: 1.4;
                }
              }
            }
          }
        }

        .card-footer {
          .action-buttons-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;

            .el-button {
              font-size: 12px;
              padding: 8px 12px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 4px;

              .el-icon {
                font-size: 14px;
              }

              span {
                font-size: 12px;
              }
            }
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 12px;
          opacity: 0.5;
        }

        .empty-text {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 16px;
        }

        .empty-action {
          font-size: 14px;
        }
      }
    }

    /* 移动端底部操作栏样式 */
    .mobile-action-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
      padding: env(safe-area-inset-bottom, 0) 0 0 0;

      .action-bar-container {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 12px 20px;

        .action-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          padding: 8px 12px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          user-select: none;
          min-width: 60px;

          &:active {
            transform: scale(0.95);
          }

          .action-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &.add-icon {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              color: white;
              box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
            }

            &.refresh-icon {
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              color: #d97706;
              border: 1px solid #fbbf24;
            }
          }

          .action-label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
          }

          &.primary-action .action-label {
            color: #1e40af;
            font-weight: 600;
          }

          &.refreshing {
            .action-icon.refresh-icon {
              animation: spin 1s linear infinite;
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
