<template>
  <div class="page-login-left">
    <!-- 背景SVG -->
    <div class="bg">
      <img src="@/assets/images/login-bg.svg" alt="background" />
    </div>

    <!-- 版权信息 -->
    <a href="https://cool-js.com" class="copyright"> Copyright © COOL </a>
  </div>
</template>

<script setup lang="ts">
// 移除未使用的导入
</script>

<style lang="scss" scoped>
$color: #2c3142;

.page-login-left {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
  background-color: #fff;
  color: $color;

  .bg {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 90%;
    pointer-events: none;
    transform: rotate(180deg) scaleY(-1);

    img {
      height: 100%;
      width: 100%;
    }
  }

  .copyright {
    position: absolute;
    bottom: 15px;
    left: 0;
    text-align: center;
    width: 100%;
    color: var(--el-color-info);
    font-size: 14px;
    user-select: none;
  }

  @media only screen and (max-width: $device-ipad-pro) {
    display: none;
  }
}
</style>
