/**
 * 日期格式化工具函数
 */

/**
 * 格式化日期时间
 * @param date 日期对象或日期字符串
 * @param format 格式化模板，默认为 YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = date instanceof Date ? date : new Date(date)

  if (isNaN(d.getTime())) {
    return '无效日期'
  }

  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hours = d.getHours()
  const minutes = d.getMinutes()
  const seconds = d.getSeconds()

  const o = {
    YYYY: year.toString(),
    YY: year.toString().slice(-2),
    MM: month < 10 ? `0${month}` : month.toString(),
    M: month.toString(),
    DD: day < 10 ? `0${day}` : day.toString(),
    D: day.toString(),
    HH: hours < 10 ? `0${hours}` : hours.toString(),
    H: hours.toString(),
    mm: minutes < 10 ? `0${minutes}` : minutes.toString(),
    m: minutes.toString(),
    ss: seconds < 10 ? `0${seconds}` : seconds.toString(),
    s: seconds.toString()
  }

  return format.replace(/YYYY|YY|MM|M|DD|D|HH|H|mm|m|ss|s/g, (match) => o[match as keyof typeof o])
}

/**
 * 获取相对时间描述（如：刚刚、1分钟前、1小时前、1天前等）
 * @param date 日期对象或日期字符串
 * @returns 相对时间描述
 */
export function getRelativeTime(date: Date | string | number): string {
  const now = new Date().getTime()
  const d = date instanceof Date ? date : new Date(date)
  const diff = now - d.getTime()

  if (isNaN(d.getTime())) {
    return '无效日期'
  }

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }

  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 小于12个月
  if (diff < 12 * 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (30 * 24 * 60 * 60 * 1000))}个月前`
  }

  // 大于等于12个月
  return `${Math.floor(diff / (12 * 30 * 24 * 60 * 60 * 1000))}年前`
}

/**
 * 获取指定日期的开始时间（00:00:00）
 * @param date 日期对象或日期字符串
 * @returns 日期对象
 */
export function getStartOfDay(date: Date | string | number): Date {
  const d = date instanceof Date ? date : new Date(date)
  d.setHours(0, 0, 0, 0)
  return d
}

/**
 * 获取指定日期的结束时间（23:59:59）
 * @param date 日期对象或日期字符串
 * @returns 日期对象
 */
export function getEndOfDay(date: Date | string | number): Date {
  const d = date instanceof Date ? date : new Date(date)
  d.setHours(23, 59, 59, 999)
  return d
}
