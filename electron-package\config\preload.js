// 它的主要作用是在渲染进程（即网页）加载之前，预先加载一些必要的代码和资源
const { ipc<PERSON><PERSON><PERSON> } = require('electron')

window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector)
    if (element) element.innerText = text
  }

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency])
  }

  // 添加右键菜单功能
  window.addEventListener('contextmenu', (e) => {
    e.preventDefault()

    // 创建右键菜单
    const contextMenu = document.createElement('div')
    contextMenu.style.cssText = `
        position: fixed;
        top: ${e.clientY}px;
        left: ${e.clientX}px;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 10000;
        padding: 5px 0;
        font-family: Arial, sans-serif;
        font-size: 14px;
      `

    // 菜单项
    const menuItems = [
      { text: '🔄 强制刷新', action: () => location.reload(true) },
      { text: '↻ 普通刷新', action: () => location.reload() },
      { text: '🔍 开发者工具', action: () => ipcRenderer.send('toggle-devtools') }
    ]

    menuItems.forEach(item => {
      const menuItem = document.createElement('div')
      menuItem.textContent = item.text
      menuItem.style.cssText = `
          padding: 8px 16px;
          cursor: pointer;
          transition: background-color 0.2s;
        `

      menuItem.addEventListener('mouseenter', () => {
        menuItem.style.backgroundColor = '#f0f0f0'
      })

      menuItem.addEventListener('mouseleave', () => {
        menuItem.style.backgroundColor = 'transparent'
      })

      menuItem.addEventListener('click', () => {
        item.action()
        document.body.removeChild(contextMenu)
      })

      contextMenu.appendChild(menuItem)
    })

    document.body.appendChild(contextMenu)

    // 点击其他地方关闭菜单
    const closeMenu = (event) => {
      if (!contextMenu.contains(event.target)) {
        document.body.removeChild(contextMenu)
        document.removeEventListener('click', closeMenu)
      }
    }

    setTimeout(() => {
      document.addEventListener('click', closeMenu)
    }, 100)
  })

  // 添加键盘快捷键
  window.addEventListener('keydown', (e) => {
    // Ctrl+Shift+R 强制刷新
    if (e.ctrlKey && e.shiftKey && e.key === 'R') {
      e.preventDefault()
      location.reload(true)
    }
    // Ctrl+R 普通刷新
    else if (e.ctrlKey && e.key === 'r') {
      e.preventDefault()
      location.reload()
    }
    // F5 刷新
    else if (e.key === 'F5') {
      e.preventDefault()
      location.reload()
    }
  })
})
