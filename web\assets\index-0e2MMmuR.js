const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ArtExcelImport-DR7eTh7j.js","./index-C5Q-N6Yp.js","./vendor-9ydHGNSq.js","./index-C3d_MO4K.css","./_plugin-vue_export-helper-BCo6x5W8.js","./ArtExcelImport-DK7utK1z.css","./el-button-CDqfIFiK.css","./el-upload-q8uObtwj.css","./el-progress-Dw9yTa91.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,i=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,c=(e,a)=>{for(var l in a||(a={}))n.call(a,l)&&i(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&i(e,l,a[l]);return e},s=(e,t)=>a(e,l(t)),d=(e,a,l)=>new Promise(((t,n)=>{var o=e=>{try{c(l.next(e))}catch(a){n(a)}},i=e=>{try{c(l.throw(e))}catch(a){n(a)}},c=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,i);c((l=l.apply(e,a)).next())}));import{q as m,_ as r,w as u}from"./index-C5Q-N6Yp.js";/* empty css                   *//* empty css                             *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                    *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                *//* empty css                         */import{_ as p}from"./ArtTable-CBEgSUvC.js";/* empty css                        */import{_ as v,a as f}from"./ArtExcelImport-DR7eTh7j.js";/* empty css                        *//* empty css                         */import{k as g,aa as h,r as y,c as _,d as b,p as k,E as w,V as C,O as x,C as I,x as V,R as O,D as z,S as T,W as A,a$ as j,Y as D,u as S,b0 as P,X as N,a3 as R,a6 as $,aP as U,B as E,aV as B,aI as F,aH as L,aL as Y,F as q,Z as M,ap as H,aJ as J,a1 as G,aK as W,aT as Z,aS as X,b1 as K,b2 as Q,aO as ee,aM as ae,aN as le,aF as te,b3 as ne,a2 as oe,aB as ie,aw as ce,az as se,aA as de,b4 as me,b5 as re,b6 as ue,b7 as pe,b8 as ve,b9 as fe,ba as ge,bb as he,ab as ye,ac as _e,$ as be,al as ke,am as we,bc as Ce,aU as xe,ai as Ie,bd as Ve,be as Oe,bf as ze,bg as Te,aG as Ae,ae as je,bh as De,bi as Se,bj as Pe,n as Ne,aD as Re}from"./vendor-9ydHGNSq.js";import{_ as $e}from"./index-DlVzy1cz.js";import{A as Ue}from"./appointmentApi-CAeIur-8.js";import{_ as Ee}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                      */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-DiJiIjk5.js";/* empty css                  *//* empty css                    *//* empty css               *//* empty css               *//* empty css                       */class Be{static getCustomerDetail(e){return d(this,null,(function*(){return m.get({url:`/customers/${e}`})}))}static createCustomer(e){return d(this,null,(function*(){return m.post({url:"/customers",data:e})}))}static updateCustomer(e,a){return d(this,null,(function*(){return m.put({url:`/customers/${e}`,data:a})}))}static deleteCustomer(e,a=!1){return d(this,null,(function*(){return m.del({url:`/customers/${e}${a?"?deleteRelated=true":""}`})}))}static deleteCustomers(e,a=!1){return d(this,null,(function*(){return m.post({url:"/customers/batch-delete",data:{ids:e,deleteRelated:a}})}))}}const Fe={pending:"待确认",confirmed:"已确认",completed:"已完成",cancelled:"已取消",created:"已创建"},Le=[{code:"110000",name:"北京市"},{code:"120000",name:"天津市"},{code:"130000",name:"河北省"},{code:"140000",name:"山西省"},{code:"150000",name:"内蒙古自治区"},{code:"210000",name:"辽宁省"},{code:"220000",name:"吉林省"},{code:"230000",name:"黑龙江省"},{code:"310000",name:"上海市"},{code:"320000",name:"江苏省"},{code:"330000",name:"浙江省"},{code:"340000",name:"安徽省"},{code:"350000",name:"福建省"},{code:"360000",name:"江西省"},{code:"370000",name:"山东省"},{code:"410000",name:"河南省"},{code:"420000",name:"湖北省"},{code:"430000",name:"湖南省"},{code:"440000",name:"广东省"},{code:"450000",name:"广西壮族自治区"},{code:"460000",name:"海南省"},{code:"500000",name:"重庆市"},{code:"510000",name:"四川省"},{code:"520000",name:"贵州省"},{code:"530000",name:"云南省"},{code:"540000",name:"西藏自治区"},{code:"610000",name:"陕西省"},{code:"620000",name:"甘肃省"},{code:"630000",name:"青海省"},{code:"640000",name:"宁夏回族自治区"},{code:"650000",name:"新疆维吾尔自治区"},{code:"710000",name:"台湾省"},{code:"810000",name:"香港特别行政区"},{code:"820000",name:"澳门特别行政区"}],Ye={11e4:[{code:"110100",name:"北京市"}],12e4:[{code:"120100",name:"天津市"}],31e4:[{code:"310100",name:"上海市"}],5e5:[{code:"500100",name:"重庆市"}],35e4:[{code:"350100",name:"福州市"},{code:"350200",name:"厦门市"},{code:"350300",name:"莆田市"},{code:"350400",name:"三明市"},{code:"350500",name:"泉州市"},{code:"350600",name:"漳州市"},{code:"350700",name:"南平市"},{code:"350800",name:"龙岩市"},{code:"350900",name:"宁德市"}],44e4:[{code:"440100",name:"广州市"},{code:"440200",name:"韶关市"},{code:"440300",name:"深圳市"},{code:"440400",name:"珠海市"},{code:"440500",name:"汕头市"},{code:"440600",name:"佛山市"},{code:"440700",name:"江门市"},{code:"440800",name:"湛江市"},{code:"440900",name:"茂名市"},{code:"441200",name:"肇庆市"},{code:"441300",name:"惠州市"},{code:"441400",name:"梅州市"},{code:"441500",name:"汕尾市"},{code:"441600",name:"河源市"},{code:"441700",name:"阳江市"},{code:"441800",name:"清远市"},{code:"441900",name:"东莞市"},{code:"442000",name:"中山市"},{code:"445100",name:"潮州市"},{code:"445200",name:"揭阳市"},{code:"445300",name:"云浮市"}],13e4:[{code:"130100",name:"石家庄市"},{code:"130200",name:"唐山市"},{code:"130300",name:"秦皇岛市"},{code:"130400",name:"邯郸市"},{code:"130500",name:"邢台市"},{code:"130600",name:"保定市"},{code:"130700",name:"张家口市"},{code:"130800",name:"承德市"},{code:"130900",name:"沧州市"},{code:"131000",name:"廊坊市"},{code:"131100",name:"衡水市"}],14e4:[{code:"140100",name:"太原市"},{code:"140200",name:"大同市"},{code:"140300",name:"阳泉市"},{code:"140400",name:"长治市"},{code:"140500",name:"晋城市"},{code:"140600",name:"朔州市"},{code:"140700",name:"晋中市"},{code:"140800",name:"运城市"},{code:"140900",name:"忻州市"},{code:"141000",name:"临汾市"},{code:"141100",name:"吕梁市"}],15e4:[{code:"150100",name:"呼和浩特市"},{code:"150200",name:"包头市"},{code:"150300",name:"乌海市"},{code:"150400",name:"赤峰市"},{code:"150500",name:"通辽市"},{code:"150600",name:"鄂尔多斯市"},{code:"150700",name:"呼伦贝尔市"},{code:"150800",name:"巴彦淖尔市"},{code:"150900",name:"乌兰察布市"}],21e4:[{code:"210100",name:"沈阳市"},{code:"210200",name:"大连市"},{code:"210300",name:"鞍山市"},{code:"210400",name:"抚顺市"},{code:"210500",name:"本溪市"},{code:"210600",name:"丹东市"},{code:"210700",name:"锦州市"},{code:"210800",name:"营口市"},{code:"210900",name:"阜新市"},{code:"211000",name:"辽阳市"},{code:"211100",name:"盘锦市"},{code:"211200",name:"铁岭市"},{code:"211300",name:"朝阳市"},{code:"211400",name:"葫芦岛市"}],22e4:[{code:"220100",name:"长春市"},{code:"220200",name:"吉林市"},{code:"220300",name:"四平市"},{code:"220400",name:"辽源市"},{code:"220500",name:"通化市"},{code:"220600",name:"白山市"},{code:"220700",name:"松原市"},{code:"220800",name:"白城市"}],23e4:[{code:"230100",name:"哈尔滨市"},{code:"230200",name:"齐齐哈尔市"},{code:"230300",name:"鸡西市"},{code:"230400",name:"鹤岗市"},{code:"230500",name:"双鸭山市"},{code:"230600",name:"大庆市"},{code:"230700",name:"伊春市"},{code:"230800",name:"佳木斯市"},{code:"230900",name:"七台河市"},{code:"231000",name:"牡丹江市"},{code:"231100",name:"黑河市"},{code:"231200",name:"绥化市"}],32e4:[{code:"320100",name:"南京市"},{code:"320200",name:"无锡市"},{code:"320300",name:"徐州市"},{code:"320400",name:"常州市"},{code:"320500",name:"苏州市"},{code:"320600",name:"南通市"},{code:"320700",name:"连云港市"},{code:"320800",name:"淮安市"},{code:"320900",name:"盐城市"},{code:"321000",name:"扬州市"},{code:"321100",name:"镇江市"},{code:"321200",name:"泰州市"},{code:"321300",name:"宿迁市"}],33e4:[{code:"330100",name:"杭州市"},{code:"330200",name:"宁波市"},{code:"330300",name:"温州市"},{code:"330400",name:"嘉兴市"},{code:"330500",name:"湖州市"},{code:"330600",name:"绍兴市"},{code:"330700",name:"金华市"},{code:"330800",name:"衢州市"},{code:"330900",name:"舟山市"},{code:"331000",name:"台州市"},{code:"331100",name:"丽水市"}],34e4:[{code:"340100",name:"合肥市"},{code:"340200",name:"芜湖市"},{code:"340300",name:"蚌埠市"},{code:"340400",name:"淮南市"},{code:"340500",name:"马鞍山市"},{code:"340600",name:"淮北市"},{code:"340700",name:"铜陵市"},{code:"340800",name:"安庆市"},{code:"341000",name:"黄山市"},{code:"341100",name:"滁州市"},{code:"341200",name:"阜阳市"},{code:"341300",name:"宿州市"},{code:"341500",name:"六安市"},{code:"341600",name:"亳州市"},{code:"341700",name:"池州市"},{code:"341800",name:"宣城市"}],36e4:[{code:"360100",name:"南昌市"},{code:"360200",name:"景德镇市"},{code:"360300",name:"萍乡市"},{code:"360400",name:"九江市"},{code:"360500",name:"新余市"},{code:"360600",name:"鹰潭市"},{code:"360700",name:"赣州市"},{code:"360800",name:"吉安市"},{code:"360900",name:"宜春市"},{code:"361000",name:"抚州市"},{code:"361100",name:"上饶市"}],37e4:[{code:"370100",name:"济南市"},{code:"370200",name:"青岛市"},{code:"370300",name:"淄博市"},{code:"370400",name:"枣庄市"},{code:"370500",name:"东营市"},{code:"370600",name:"烟台市"},{code:"370700",name:"潍坊市"},{code:"370800",name:"济宁市"},{code:"370900",name:"泰安市"},{code:"371000",name:"威海市"},{code:"371100",name:"日照市"},{code:"371300",name:"临沂市"},{code:"371400",name:"德州市"},{code:"371500",name:"聊城市"},{code:"371600",name:"滨州市"},{code:"371700",name:"菏泽市"}],41e4:[{code:"410100",name:"郑州市"},{code:"410200",name:"开封市"},{code:"410300",name:"洛阳市"},{code:"410400",name:"平顶山市"},{code:"410500",name:"安阳市"},{code:"410600",name:"鹤壁市"},{code:"410700",name:"新乡市"},{code:"410800",name:"焦作市"},{code:"410900",name:"濮阳市"},{code:"411000",name:"许昌市"},{code:"411100",name:"漯河市"},{code:"411200",name:"三门峡市"},{code:"411300",name:"南阳市"},{code:"411400",name:"商丘市"},{code:"411500",name:"信阳市"},{code:"411600",name:"周口市"},{code:"411700",name:"驻马店市"}],42e4:[{code:"420100",name:"武汉市"},{code:"420200",name:"黄石市"},{code:"420300",name:"十堰市"},{code:"420500",name:"宜昌市"},{code:"420600",name:"襄阳市"},{code:"420700",name:"鄂州市"},{code:"420800",name:"荆门市"},{code:"420900",name:"孝感市"},{code:"421000",name:"荆州市"},{code:"421100",name:"黄冈市"},{code:"421200",name:"咸宁市"},{code:"421300",name:"随州市"},{code:"422800",name:"恩施土家族苗族自治州"}],43e4:[{code:"430100",name:"长沙市"},{code:"430200",name:"株洲市"},{code:"430300",name:"湘潭市"},{code:"430400",name:"衡阳市"},{code:"430500",name:"邵阳市"},{code:"430600",name:"岳阳市"},{code:"430700",name:"常德市"},{code:"430800",name:"张家界市"},{code:"430900",name:"益阳市"},{code:"431000",name:"郴州市"},{code:"431100",name:"永州市"},{code:"431200",name:"怀化市"},{code:"431300",name:"娄底市"},{code:"433100",name:"湘西土家族苗族自治州"}],45e4:[{code:"450100",name:"南宁市"},{code:"450200",name:"柳州市"},{code:"450300",name:"桂林市"},{code:"450400",name:"梧州市"},{code:"450500",name:"北海市"},{code:"450600",name:"防城港市"},{code:"450700",name:"钦州市"},{code:"450800",name:"贵港市"},{code:"450900",name:"玉林市"},{code:"451000",name:"百色市"},{code:"451100",name:"贺州市"},{code:"451200",name:"河池市"},{code:"451300",name:"来宾市"},{code:"451400",name:"崇左市"}],46e4:[{code:"460100",name:"海口市"},{code:"460200",name:"三亚市"},{code:"460300",name:"三沙市"},{code:"460400",name:"儋州市"}],51e4:[{code:"510100",name:"成都市"},{code:"510300",name:"自贡市"},{code:"510400",name:"攀枝花市"},{code:"510500",name:"泸州市"},{code:"510600",name:"德阳市"},{code:"510700",name:"绵阳市"},{code:"510800",name:"广元市"},{code:"510900",name:"遂宁市"},{code:"511000",name:"内江市"},{code:"511100",name:"乐山市"},{code:"511300",name:"南充市"},{code:"511400",name:"眉山市"},{code:"511500",name:"宜宾市"},{code:"511600",name:"广安市"},{code:"511700",name:"达州市"},{code:"511800",name:"雅安市"},{code:"511900",name:"巴中市"},{code:"512000",name:"资阳市"},{code:"513200",name:"阿坝藏族羌族自治州"},{code:"513300",name:"甘孜藏族自治州"},{code:"513400",name:"凉山彝族自治州"}],52e4:[{code:"520100",name:"贵阳市"},{code:"520200",name:"六盘水市"},{code:"520300",name:"遵义市"},{code:"520400",name:"安顺市"},{code:"520500",name:"毕节市"},{code:"520600",name:"铜仁市"},{code:"522300",name:"黔西南布依族苗族自治州"},{code:"522600",name:"黔东南苗族侗族自治州"},{code:"522700",name:"黔南布依族苗族自治州"}],53e4:[{code:"530100",name:"昆明市"},{code:"530300",name:"曲靖市"},{code:"530400",name:"玉溪市"},{code:"530500",name:"保山市"},{code:"530600",name:"昭通市"},{code:"530700",name:"丽江市"},{code:"530800",name:"普洱市"},{code:"530900",name:"临沧市"},{code:"532300",name:"楚雄彝族自治州"},{code:"532500",name:"红河哈尼族彝族自治州"},{code:"532600",name:"文山壮族苗族自治州"},{code:"532800",name:"西双版纳傣族自治州"},{code:"532900",name:"大理白族自治州"},{code:"533100",name:"德宏傣族景颇族自治州"},{code:"533300",name:"怒江傈僳族自治州"},{code:"533400",name:"迪庆藏族自治州"}],54e4:[{code:"540100",name:"拉萨市"},{code:"540200",name:"日喀则市"},{code:"540300",name:"昌都市"},{code:"540400",name:"林芝市"},{code:"540500",name:"山南市"},{code:"540600",name:"那曲市"},{code:"542500",name:"阿里地区"}],61e4:[{code:"610100",name:"西安市"},{code:"610200",name:"铜川市"},{code:"610300",name:"宝鸡市"},{code:"610400",name:"咸阳市"},{code:"610500",name:"渭南市"},{code:"610600",name:"延安市"},{code:"610700",name:"汉中市"},{code:"610800",name:"榆林市"},{code:"610900",name:"安康市"},{code:"611000",name:"商洛市"}],62e4:[{code:"620100",name:"兰州市"},{code:"620200",name:"嘉峪关市"},{code:"620300",name:"金昌市"},{code:"620400",name:"白银市"},{code:"620500",name:"天水市"},{code:"620600",name:"武威市"},{code:"620700",name:"张掖市"},{code:"620800",name:"平凉市"},{code:"620900",name:"酒泉市"},{code:"621000",name:"庆阳市"},{code:"621100",name:"定西市"},{code:"621200",name:"陇南市"},{code:"622900",name:"临夏回族自治州"},{code:"623000",name:"甘南藏族自治州"}],63e4:[{code:"630100",name:"西宁市"},{code:"630200",name:"海东市"},{code:"632200",name:"海北藏族自治州"},{code:"632300",name:"黄南藏族自治州"},{code:"632500",name:"海南藏族自治州"},{code:"632600",name:"果洛藏族自治州"},{code:"632700",name:"玉树藏族自治州"},{code:"632800",name:"海西蒙古族藏族自治州"}],64e4:[{code:"640100",name:"银川市"},{code:"640200",name:"石嘴山市"},{code:"640300",name:"吴忠市"},{code:"640400",name:"固原市"},{code:"640500",name:"中卫市"}],65e4:[{code:"650100",name:"乌鲁木齐市"},{code:"650200",name:"克拉玛依市"},{code:"650400",name:"吐鲁番市"},{code:"650500",name:"哈密市"},{code:"652300",name:"昌吉回族自治州"},{code:"652700",name:"博尔塔拉蒙古自治州"},{code:"652800",name:"巴音郭楞蒙古自治州"},{code:"652900",name:"阿克苏地区"},{code:"653000",name:"克孜勒苏柯尔克孜自治州"},{code:"653100",name:"喀什地区"},{code:"653200",name:"和田地区"},{code:"654000",name:"伊犁哈萨克自治州"},{code:"654200",name:"塔城地区"},{code:"654300",name:"阿勒泰地区"}],71e4:[{code:"710100",name:"台北市"},{code:"710200",name:"高雄市"},{code:"710300",name:"台南市"},{code:"710400",name:"台中市"},{code:"710500",name:"基隆市"},{code:"710600",name:"新竹市"},{code:"710700",name:"嘉义市"}],81e4:[{code:"810100",name:"香港特别行政区"}],82e4:[{code:"820100",name:"澳门特别行政区"}]},qe={110100:[{code:"110101",name:"东城区"},{code:"110102",name:"西城区"},{code:"110105",name:"朝阳区"},{code:"110106",name:"丰台区"},{code:"110107",name:"石景山区"},{code:"110108",name:"海淀区"},{code:"110109",name:"门头沟区"},{code:"110111",name:"房山区"},{code:"110112",name:"通州区"},{code:"110113",name:"顺义区"},{code:"110114",name:"昌平区"},{code:"110115",name:"大兴区"},{code:"110116",name:"怀柔区"},{code:"110117",name:"平谷区"},{code:"110118",name:"密云区"},{code:"110119",name:"延庆区"}],120100:[{code:"120101",name:"和平区"},{code:"120102",name:"河东区"},{code:"120103",name:"河西区"},{code:"120104",name:"南开区"},{code:"120105",name:"河北区"},{code:"120106",name:"红桥区"},{code:"120110",name:"东丽区"},{code:"120111",name:"西青区"},{code:"120112",name:"津南区"},{code:"120113",name:"北辰区"},{code:"120114",name:"武清区"},{code:"120115",name:"宝坻区"},{code:"120116",name:"滨海新区"},{code:"120117",name:"宁河区"},{code:"120118",name:"静海区"},{code:"120119",name:"蓟州区"}],310100:[{code:"310101",name:"黄浦区"},{code:"310104",name:"徐汇区"},{code:"310105",name:"长宁区"},{code:"310106",name:"静安区"},{code:"310107",name:"普陀区"},{code:"310109",name:"虹口区"},{code:"310110",name:"杨浦区"},{code:"310112",name:"闵行区"},{code:"310113",name:"宝山区"},{code:"310114",name:"嘉定区"},{code:"310115",name:"浦东新区"},{code:"310116",name:"金山区"},{code:"310117",name:"松江区"},{code:"310118",name:"青浦区"},{code:"310120",name:"奉贤区"},{code:"310151",name:"崇明区"}],500100:[{code:"500101",name:"万州区"},{code:"500102",name:"涪陵区"},{code:"500103",name:"渝中区"},{code:"500104",name:"大渡口区"},{code:"500105",name:"江北区"},{code:"500106",name:"沙坪坝区"},{code:"500107",name:"九龙坡区"},{code:"500108",name:"南岸区"},{code:"500109",name:"北碚区"},{code:"500110",name:"綦江区"},{code:"500111",name:"大足区"},{code:"500112",name:"渝北区"},{code:"500113",name:"巴南区"},{code:"500114",name:"黔江区"},{code:"500115",name:"长寿区"},{code:"500116",name:"江津区"},{code:"500117",name:"合川区"},{code:"500118",name:"永川区"},{code:"500119",name:"南川区"},{code:"500120",name:"璧山区"},{code:"500151",name:"铜梁区"},{code:"500152",name:"潼南区"},{code:"500153",name:"荣昌区"},{code:"500154",name:"开州区"},{code:"500155",name:"梁平区"},{code:"500156",name:"武隆区"}],350100:[{code:"350102",name:"鼓楼区"},{code:"350103",name:"台江区"},{code:"350104",name:"仓山区"},{code:"350105",name:"马尾区"},{code:"350111",name:"晋安区"},{code:"350112",name:"长乐区"},{code:"350121",name:"闽侯县"},{code:"350122",name:"连江县"},{code:"350123",name:"罗源县"},{code:"350124",name:"闽清县"},{code:"350125",name:"永泰县"},{code:"350128",name:"平潭县"},{code:"350181",name:"福清市"}],350200:[{code:"350203",name:"思明区"},{code:"350205",name:"海沧区"},{code:"350206",name:"湖里区"},{code:"350211",name:"集美区"},{code:"350212",name:"同安区"},{code:"350213",name:"翔安区"}],350900:[{code:"350902",name:"蕉城区"},{code:"350921",name:"霞浦县"},{code:"350922",name:"古田县"},{code:"350923",name:"屏南县"},{code:"350924",name:"寿宁县"},{code:"350925",name:"周宁县"},{code:"350926",name:"柘荣县"},{code:"350981",name:"福安市"},{code:"350982",name:"福鼎市"}],440100:[{code:"440103",name:"荔湾区"},{code:"440104",name:"越秀区"},{code:"440105",name:"海珠区"},{code:"440106",name:"天河区"},{code:"440111",name:"白云区"},{code:"440112",name:"黄埔区"},{code:"440113",name:"番禺区"},{code:"440114",name:"花都区"},{code:"440115",name:"南沙区"},{code:"440117",name:"从化区"},{code:"440118",name:"增城区"}],440300:[{code:"440303",name:"罗湖区"},{code:"440304",name:"福田区"},{code:"440305",name:"南山区"},{code:"440306",name:"宝安区"},{code:"440307",name:"龙岗区"},{code:"440308",name:"盐田区"},{code:"440309",name:"龙华区"},{code:"440310",name:"坪山区"},{code:"440311",name:"光明区"}]};class Me{static getProvinces(){return Le}static getCitiesByProvince(e){return Ye[e]||[]}static getDistrictsByCity(e){if(qe[e])return qe[e];let a="";for(const l in Ye){const t=Ye[l].find((a=>a.code===e));if(t){a=t.name;break}}return function(e,a){if(qe[e])return qe[e];const l=e.substring(0,4),t=a.replace(/市|县|区|自治州|地区/g,"");return[{code:`${l}01`,name:`${t}中心区`},{code:`${l}02`,name:`${t}城区`},{code:`${l}21`,name:`${t}县`},{code:`${l}22`,name:`${t}新区`},{code:`${l}23`,name:`${t}开发区`}]}(e,a)}static getNameByCode(e){const a=Le.find((a=>a.code===e));if(a)return a.name;for(const l of Object.values(Ye)){const a=l.find((a=>a.code===e));if(a)return a.name}if(e&&6===e.length){const a=e.substring(0,4)+"00";for(const l of Object.values(Ye)){const t=l.find((e=>e.code.substring(0,4)+"00"===a));if(t){const a=this.getDistrictsByCity(t.code).find((a=>a.code===e));if(a)return a.name}}}return""}static getFullAddress(e){if(!e||0===e.length)return"";return e.map((e=>this.getNameByCode(e))).filter(Boolean).join("")}static toCascaderFormat(){return Le.map((e=>({value:e.code,label:e.name,children:(Ye[e.code]||[]).map((e=>{const a=this.getDistrictsByCity(e.code);return{value:e.code,label:e.name,children:a.map((e=>({value:e.code,label:e.name})))}}))})))}static toOriginalFormat(){const e={};for(const a in Ye){Ye[a].forEach((a=>{const l=this.getDistrictsByCity(a.code);e[a.code]=l.map((e=>({value:e.code,label:e.name})))}))}return{provinceData:Le.map((e=>({value:e.code,label:e.name}))),cityData:Object.fromEntries(Object.entries(Ye).map((([e,a])=>[e,a.map((e=>({value:e.code,label:e.name})))]))),districtData:e}}}Me.toOriginalFormat(),Me.toCascaderFormat();const He=Me,Je={class:"appointment-list-container"},Ge={class:"card-header"},We={class:"left-section"},Ze={class:"right-section"},Xe={class:"dropdown-item-content"},Ke={class:"dropdown-item-content"},Qe={class:"dropdown-item-content"},ea={key:1,class:"mobile-header"},aa={class:"mobile-stats-section"},la={class:"stats-card"},ta={class:"stats-number"},na={class:"batch-info"},oa={class:"table-container"},ia={key:0,class:"empty-state"},ca={class:"empty-illustration"},sa={class:"empty-content"},da={class:"empty-description"},ma={class:"empty-actions"},ra={key:1,class:"table-skeleton"},ua={class:"skeleton-header"},pa={class:"skeleton-rows"},va=["onClick"],fa={class:"tag-list"},ga={key:0},ha={class:"package-config"},ya={key:0,class:"config-item"},_a={class:"config-icon package-icon"},ba={class:"config-content"},ka={class:"config-value"},wa={key:1,class:"config-item"},Ca={class:"config-icon change-icon"},xa={class:"config-content"},Ia={class:"config-value"},Va={key:2,class:"config-item"},Oa={class:"config-icon memory-icon"},za={class:"config-content"},Ta={class:"config-value"},Aa={key:3,class:"no-config"},ja={key:3,class:"mobile-card-list"},Da={key:0,class:"mobile-skeleton"},Sa={class:"skeleton-header"},Pa={class:"skeleton-content"},Na=["onClick"],Ra={class:"card-header-mobile"},$a={class:"customer-info"},Ua={class:"customer-avatar"},Ea={class:"avatar-text"},Ba={class:"customer-details"},Fa={class:"customer-name"},La={class:"customer-id"},Ya={class:"card-actions"},qa={class:"contact-info"},Ma={class:"info-item"},Ha={class:"info-text"},Ja={key:0,class:"tags-section"},Ga={class:"tag-list-mobile"},Wa={class:"package-section"},Za={class:"package-config-mobile"},Xa={key:0,class:"config-item-mobile"},Ka={class:"config-icon-mobile package-icon"},Qa={class:"config-content-mobile"},el={class:"config-value-mobile"},al={key:1,class:"config-item-mobile"},ll={class:"config-icon-mobile change-icon"},tl={class:"config-content-mobile"},nl={class:"config-value-mobile"},ol={key:2,class:"config-item-mobile"},il={class:"config-icon-mobile memory-icon"},cl={class:"config-content-mobile"},sl={class:"config-value-mobile"},dl={key:3,class:"no-config-mobile"},ml={class:"card-footer"},rl={class:"steps-container"},ul={class:"form-row"},pl={class:"form-row"},vl={class:"package-option"},fl={class:"package-price"},gl={class:"package-option"},hl={class:"package-price"},yl={class:"package-option"},_l={class:"package-price"},bl={class:"form-row"},kl={class:"form-row"},wl={class:"package-option"},Cl={class:"package-price"},xl={class:"package-option"},Il={class:"package-price"},Vl={class:"package-option"},Ol={class:"package-price"},zl={class:"status-option"},Tl={class:"status-option"},Al={class:"status-option"},jl={class:"status-option"},Dl={class:"dialog-footer"},Sl={class:"confirm-content"},Pl={class:"confirm-header"},Nl={class:"confirm-title"},Rl={key:0,class:"confirm-selection-preview"},$l={class:"confirm-preview-title"},Ul={class:"confirm-preview-items"},El={key:0,class:"confirm-preview-item"},Bl={class:"confirm-item-tag"},Fl={class:"confirm-item-content"},Ll={class:"confirm-item-name"},Yl={class:"confirm-item-price"},ql={key:1,class:"confirm-preview-item"},Ml={class:"confirm-item-tag"},Hl={class:"confirm-item-content"},Jl={class:"confirm-item-name"},Gl={class:"confirm-item-price"},Wl={key:2,class:"confirm-preview-item"},Zl={class:"confirm-item-tag"},Xl={class:"confirm-item-content"},Kl={class:"confirm-item-name"},Ql={class:"confirm-item-price"},et={class:"confirm-total"},at={class:"confirm-total-price"},lt={key:0,class:"confirm-preview-item"},tt={class:"confirm-item-tag"},nt={class:"confirm-item-content"},ot={class:"confirm-item-name"},it={class:"confirm-item-price"},ct={key:1,class:"confirm-preview-item"},st={class:"confirm-item-tag"},dt={class:"confirm-item-content"},mt={class:"confirm-item-name"},rt={class:"confirm-item-price"},ut={key:2,class:"confirm-preview-item"},pt={class:"confirm-item-tag"},vt={class:"confirm-item-content"},ft={class:"confirm-item-name"},gt={class:"confirm-item-price"},ht={class:"confirm-total"},yt={class:"confirm-total-price"},_t={class:"confirm-footer"},bt={key:0,class:"customer-detail"},kt={class:"tag-list detail-tag-list"},wt={key:0},Ct={key:1,class:"mobile-swiper-container"},xt={class:"swiper-indicators"},It=["onClick"],Vt={class:"mobile-card basic-info-card","data-card-index":"0"},Ot={class:"card-header-mobile"},zt={class:"customer-avatar-large"},Tt={class:"avatar-text-large"},At={class:"customer-main-info"},jt={class:"customer-name-large"},Dt={class:"card-content-mobile"},St={class:"info-grid"},Pt={class:"info-item"},Nt={class:"info-text"},Rt={class:"info-value"},$t={class:"info-item"},Ut={class:"info-text"},Et={class:"info-value"},Bt={class:"info-item"},Ft={class:"info-text"},Lt={class:"info-value"},Yt={class:"mobile-card status-card","data-card-index":"1"},qt={class:"card-title-mobile"},Mt={class:"card-content-mobile"},Ht={class:"status-grid"},Jt={class:"status-item"},Gt={class:"status-item"},Wt={class:"status-value"},Zt={class:"status-item"},Xt={class:"status-value"},Kt={class:"status-item"},Qt={class:"status-value"},en={class:"mobile-card tags-remark-card","data-card-index":"2"},an={class:"card-title-mobile"},ln={class:"card-content-mobile"},tn={key:0,class:"tags-section"},nn={class:"mobile-tags"},on={key:1,class:"remark-section"},cn={class:"remark-text"},sn={key:2,class:"empty-state"},dn={key:2,class:"latest-appointment"},mn={class:"appointment-layout"},rn={class:"appointment-left"},un={class:"appointment-items"},pn={key:0,class:"appointment-item"},vn={class:"item-tag"},fn={class:"item-content"},gn={class:"item-details"},hn={class:"item-name"},yn={class:"item-price"},_n={key:1,class:"appointment-item"},bn={class:"item-tag"},kn={class:"item-content"},wn={class:"item-details"},Cn={class:"item-name"},xn={class:"item-price"},In={key:2,class:"appointment-item"},Vn={class:"item-tag"},On={class:"item-content"},zn={class:"item-details"},Tn={class:"item-name"},An={class:"item-price"},jn={class:"appointment-total"},Dn={class:"total-price"},Sn={class:"appointment-right"},Pn={class:"appointment-remark"},Nn={class:"remark-content"},Rn={key:0,class:"detail-card appointment-card"},$n={class:"card-title"},Un={class:"card-content"},En={key:0,class:"appointment-item-mobile"},Bn={class:"item-header"},Fn={class:"item-price"},Ln={class:"item-name"},Yn={key:1,class:"appointment-item-mobile"},qn={class:"item-header"},Mn={class:"item-price"},Hn={class:"item-name"},Jn={key:2,class:"appointment-item-mobile"},Gn={class:"item-header"},Wn={class:"item-price"},Zn={class:"item-name"},Xn={class:"total-price-mobile"},Kn={class:"total-amount"},Qn={key:1,class:"detail-card appointment-remark-card"},eo={class:"card-title"},ao={class:"card-content"},lo={class:"remark-text"},to={class:"dialog-footer"},no={key:0,class:"mobile-action-bar"},oo={class:"action-bar-container"},io={class:"action-item"},co={class:"action-icon excel-icon"},so={class:"action-icon add-icon"},mo={class:"action-icon refresh-icon"},ro="appointment_table_column_widths",uo="appointment_customer_cache",po=3e5,vo=Ee(g({__name:"index",setup(e){const a=h({keyword:"",tags:"",appointmentStatus:""}),l=y([{label:"关键词",prop:"keyword",type:"input",placeholder:"客户姓名/电话/邮箱/订单号",config:{clearable:!0}},{label:"标签",prop:"tags",type:"select",placeholder:"请选择标签",config:{clearable:!0},options:[{label:"全部",value:""},{label:"VIP客户",value:"VIP"},{label:"新客户",value:"新客户"},{label:"老客户",value:"老客户"},{label:"商业客户",value:"商业客户"}]},{label:"预约状态",prop:"appointmentStatus",type:"select",placeholder:"请选择预约状态",config:{clearable:!0},options:[{label:"全部",value:""},{label:"待确认",value:"pending"},{label:"已确认",value:"confirmed"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"}]}]),t=y(!1),n=y([]),o=y([]),i=y(1),m=y(10),g=y(0),Ee=y(!1),Le=y("add"),Ye=y(),qe=h({name:"",phone:"",email:"",region:[],addressDetail:"",address:"",tags:[],remark:"",packageType:"",changeOption:"",memoryCard:"",appointmentTime:"",appointmentRemark:"",status:"pending"}),Me=y(!1),vo=y(null);let fo={};const go={name:[{required:!0,message:"请输入客户姓名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!1,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的11位手机号码",trigger:"blur"}],email:[{pattern:/^[\w.-]+@[\w.-]+\.\w+$/,message:"请输入正确的邮箱格式",trigger:"blur"}],region:[{required:!1,message:"请选择所在地区",trigger:"change"}],addressDetail:[{required:!1,message:"请输入详细地址",trigger:"blur"}],packageType:[{required:!0,message:"请选择拍摄套餐",trigger:"change"}],changeOption:[{required:!0,message:"请选择随心换",trigger:"change"}],memoryCard:[{required:!0,message:"请选择内存卡",trigger:"change"}]},ho=y(!1),yo=y(u()),_o=()=>{ho.value=window.innerWidth<=768},bo=y({}),ko=(e,a)=>bo.value[e]||a,wo=(e,a)=>{bo.value[e]=a,(()=>{try{localStorage.setItem(ro,JSON.stringify(bo.value))}catch(e){}})()},Co=y(),xo=e=>{var a;return(e=>{if(!Co.value)return!1;const a=Date.now()-Co.value.timestamp>po,l=Co.value.searchParams===e;return!a&&l})(e)?(null==(a=Co.value)?void 0:a.data)||[]:null},Io=_((()=>!!(a.keyword||a.tags||a.appointmentStatus))),Vo=()=>{a.keyword="",a.tags="",a.appointmentStatus="",Jo()},Oo=()=>{ho.value&&Ne((()=>{const e=document.querySelector(".step-content-scrollable");e&&e.scrollTo({top:e.scrollHeight-200,behavior:"smooth"})}))},zo=e=>{if("Enter"===e.key){const e=document.activeElement;e&&e.blur&&e.blur(),Jo()}};b((()=>{_o(),window.addEventListener("resize",_o),document.addEventListener("keyup",zo),(()=>{try{const e=localStorage.getItem(ro);e&&(bo.value=JSON.parse(e))}catch(e){}})(),(()=>{try{const e=localStorage.getItem(uo);if(e){const a=JSON.parse(e);Date.now()-a.timestamp<po?Co.value=a:localStorage.removeItem(uo)}}catch(e){localStorage.removeItem(uo)}})(),Yo()})),k((()=>{window.removeEventListener("resize",_o),document.removeEventListener("keyup",zo)}));const To=e=>{switch(e){case"confirmed":return"success";case"pending":return"warning";case"completed":return"primary";case"cancelled":return"danger";default:return"info"}},Ao=e=>Fe[e]||e,jo=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).replace(/\//g,"-")},Do=e=>{if(!e||e.length<6)return e;return`DJIND-${e.substring(0,3)}${e.substring(e.length-3)}`},So=e=>{const a=Do(e),l=document.createElement("input");l.value=a,document.body.appendChild(l),l.select(),document.execCommand("copy"),document.body.removeChild(l),w.success(`已复制: ${a}`)},Po=e=>{var a,l,t;return(null==(a=e.packageInfo)?void 0:a.name)||e.packageTypeName||(null==(l=e.changeOptionInfo)?void 0:l.name)||e.changeOptionName||(null==(t=e.memoryCardInfo)?void 0:t.size)||e.memoryCardName},No=y(0),Ro=y(),$o=_((()=>[{id:"basic",title:"基本信息"},{id:"status",title:"状态信息"},{id:"tags-remark",title:"标签备注"}])),Uo=()=>{if(!Ro.value)return;const e=Ro.value.scrollLeft,a=Ro.value.clientWidth,l=Math.round(e/a);l!==No.value&&(No.value=l)},Eo=e=>{var a,l,t,n;if(!e)return 0;if(null==(a=e.latestAppointment)?void 0:a.totalPrice)return e.latestAppointment.totalPrice;let o=0;return(null==(l=e.packageInfo)?void 0:l.price)&&(o+=e.packageInfo.price),(null==(t=e.changeOptionInfo)?void 0:t.price)&&(o+=e.changeOptionInfo.price),(null==(n=e.memoryCardInfo)?void 0:n.price)&&(o+=e.memoryCardInfo.price),o},Bo=_((()=>n.value.map((e=>{var a,l,t,n,o,i;return{customerId:Do(e._id),name:e.name||"",phone:e.phone||"",email:e.email||"",address:e.address||"",tags:e.tags?e.tags.join(", "):"",packageName:(null==(a=e.packageInfo)?void 0:a.name)||e.packageTypeName||"",packagePrice:(null==(l=e.packageInfo)?void 0:l.price)||0,changeOptionName:(null==(t=e.changeOptionInfo)?void 0:t.name)||e.changeOptionName||"",changeOptionPrice:(null==(n=e.changeOptionInfo)?void 0:n.price)||0,memoryCardName:(null==(o=e.memoryCardInfo)?void 0:o.size)||e.memoryCardName||"",memoryCardPrice:(null==(i=e.memoryCardInfo)?void 0:i.price)||0,totalPrice:e.totalPrice||0,appointmentTime:e.appointmentTime?jo(e.appointmentTime):"",appointmentRemark:e.appointmentRemark||"",status:Ao(e.status),remark:e.remark||"",createdAt:e.createdAt?jo(e.createdAt):"",updatedAt:e.updatedAt?jo(e.updatedAt):""}})))),Fo={customerId:"客户ID",name:"客户姓名",phone:"联系电话",email:"电子邮箱",address:"客户地址",tags:"客户标签",packageName:"套餐名称",packagePrice:"套餐价格",changeOptionName:"随心换名称",changeOptionPrice:"随心换价格",memoryCardName:"内存卡规格",memoryCardPrice:"内存卡价格",totalPrice:"总价格",appointmentTime:"预约时间",appointmentRemark:"预约备注",status:"预约状态",remark:"客户备注",createdAt:"创建时间",updatedAt:"更新时间"},Lo=()=>{if(!a.keyword&&!a.tags&&!a.appointmentStatus)return n.value=o.value,void(g.value=o.value.length);let e=[...o.value];if(a.keyword){const l=a.keyword.toLowerCase();if(/^DJIND-[a-zA-Z0-9]{6}$/i.test(l)){const a=l.split("-")[1].toLowerCase();e=e.filter((e=>{if(!e._id)return!1;return(e._id.substring(0,3).toLowerCase()+e._id.substring(e._id.length-3).toLowerCase()).toLowerCase()===a}))}else e=e.filter((e=>e.name&&e.name.toLowerCase().includes(l)||e.phone&&e.phone.includes(l)||e.email&&e.email.toLowerCase().includes(l)))}if(a.tags&&(e=e.filter((e=>e.tags&&e.tags.includes(a.tags)))),a.appointmentStatus&&(e=e.filter((e=>e.status===a.appointmentStatus))),ho.value)n.value=e,g.value=e.length;else{const a=(i.value-1)*m.value,l=a+m.value;n.value=e.slice(a,l),g.value=e.length}},Yo=(e=!0)=>d(this,null,(function*(){t.value=!0;try{const a={page:1,limit:1e3},l=(e=>JSON.stringify(e))(a);if(e){const e=xo(l);if(e)return o.value=e,Lo(),void(t.value=!1)}const n=yield Ue.getCustomerList(a);n&&n.items&&Array.isArray(n.items)?(o.value=n.items,((e,a)=>{Co.value={data:[...e],timestamp:Date.now(),searchParams:a};try{localStorage.setItem(uo,JSON.stringify(Co.value))}catch(l){}})(n.items,l)):o.value=[],Lo()}catch(a){w.error("获取客户列表失败"),n.value=[],g.value=0}finally{t.value=!1}})),qo=y(!1),Mo=()=>d(this,null,(function*(){if(!qo.value)if(ho.value)Ho();else{qo.value=!0;try{Ho()}catch(e){}finally{setTimeout((()=>{qo.value=!1}),1e3)}}})),Ho=()=>{Co.value=void 0,localStorage.removeItem(uo),o.value=[],Yo(!1)},Jo=()=>{i.value=1,Lo()},Go=()=>{a.keyword="",a.tags="",a.appointmentStatus="",i.value=1,Lo()},Wo=e=>{try{if(!e||0===e.length)return void w.warning("导入的文件为空");const a=e.filter((e=>e["客户姓名"]&&e["客户姓名"].trim()));if(0===a.length)return void w.error('导入数据格式不正确，请确保包含"客户姓名"列');const l=a.map(((e,a)=>{const l=e["客户标签"]?e["客户标签"].toString().split(",").map((e=>e.trim())).filter((e=>e)):[];let t="pending";if(e["预约状态"]){const a=e["预约状态"].toString(),l=Object.entries(Fe).find((([e,l])=>l===a));l&&(t=l[0])}return{_id:`import_${Date.now()}_${a}`,name:e["客户姓名"].toString().trim(),phone:e["联系电话"]?e["联系电话"].toString().trim():"",email:e["电子邮箱"]?e["电子邮箱"].toString().trim():"",address:e["客户地址"]?e["客户地址"].toString().trim():"",tags:l,packageTypeName:e["套餐名称"]?e["套餐名称"].toString().trim():"",changeOptionName:e["随心换名称"]?e["随心换名称"].toString().trim():"",memoryCardName:e["内存卡规格"]?e["内存卡规格"].toString().trim():"",totalPrice:e["总价格"]&&Number(e["总价格"])||0,appointmentTime:e["预约时间"]?e["预约时间"].toString():"",appointmentRemark:e["预约备注"]?e["预约备注"].toString():"",status:t,remark:e["客户备注"]?e["客户备注"].toString():"",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),appointmentCount:0}}));o.value=[...o.value,...l],Lo(),w.success(`成功导入 ${l.length} 条客户数据`),Re.confirm("导入的数据仅在当前页面有效，刷新页面后将丢失。是否需要手动保存重要数据？","提示",{confirmButtonText:"我知道了",cancelButtonText:"取消",type:"warning"}).catch((()=>{}))}catch(a){w.error("处理导入数据失败，请检查文件格式")}},Zo=e=>{w.error(`导入失败: ${e.message}`)},Xo=()=>{w.success("导出成功")},Ko=e=>{w.error(`导出失败: ${e.message}`)},Qo=y(),ei=y(),ai=e=>{var a,l,t,n;switch(e){case"import":const e=null==(l=null==(a=Qo.value)?void 0:a.$el)?void 0:l.querySelector('input[type="file"]');e&&e.click();break;case"export":if(0===Bo.value.length)return void w.warning("暂无数据可导出");null==(n=null==(t=ei.value)?void 0:t.$el)||n.click();break;case"template":li()}},li=()=>{const e=[{"客户姓名":"张三","联系电话":"13800138000","电子邮箱":"<EMAIL>","客户地址":"北京市朝阳区xxx街道xxx号","客户标签":"VIP客户,新客户","套餐名称":"标准套餐","随心换名称":"标准随心换","内存卡规格":"32GB","总价格":2999,"预约时间":"2024-01-15 14:30:00","预约备注":"客户要求下午拍摄","预约状态":"待确认","客户备注":"重要客户，需要特别关注"},{"客户姓名":"李四","联系电话":"13900139000","电子邮箱":"<EMAIL>","客户地址":"上海市浦东新区xxx路xxx号","客户标签":"老客户","套餐名称":"高级套餐","随心换名称":"高级随心换","内存卡规格":"64GB","总价格":3999,"预约时间":"2024-01-16 10:00:00","预约备注":"客户要求上午拍摄","预约状态":"已确认","客户备注":"回头客，服务态度要好"}];try{r((()=>import("./ArtExcelImport-DR7eTh7j.js").then((e=>e.x))),__vite__mapDeps([0,1,2,3,4,5,6,7,8]),import.meta.url).then((a=>{const l=a.utils.book_new(),t=a.utils.json_to_sheet(e),n=Object.keys(e[0]).map((()=>({wch:20})));t["!cols"]=n,a.utils.book_append_sheet(l,t,"客户数据模板");const o=a.write(l,{bookType:"xlsx",type:"array"}),i=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});r((()=>import("./ArtExcelImport-DR7eTh7j.js").then((e=>e.F))),__vite__mapDeps([0,1,2,3,4,5,6,7,8]),import.meta.url).then((e=>{e.saveAs(i,"预约客户导入模板.xlsx"),w.success("模板下载成功")}))}))}catch(a){w.error("下载模板失败")}},ti=(e,a)=>{"selection"!==a.type&&oi(e)},ni=()=>{Le.value="add",Object.assign(qe,{name:"",phone:"",email:"",region:[],addressDetail:"",address:"",tags:[],remark:"",packageType:"",changeOption:"",memoryCard:"",appointmentTime:"",appointmentRemark:"",status:"pending"}),Object.assign(fi,{packageType:"",changeOption:"",memoryCard:"",totalPrice:"",appointmentTime:"",remark:"",status:"pending"}),ui.value=0,Ee.value=!0},oi=e=>{e&&(vo.value=e,Me.value=!0)},ii=y(!1),ci=y(!1),si=e=>d(this,null,(function*(){Le.value="edit",Ee.value=!0,ii.value=!0;try{yield Promise.all([Ci().catch((e=>{hi.value=[{_id:"test1",name:"标准套餐",price:3499,isActive:!0,description:""},{_id:"test2",name:"全能套餐",price:4499,isActive:!0,description:""}]})),xi().catch((e=>{yi.value=[{_id:"change1",name:"随心换",price:219,isActive:!0,description:""},{_id:"change2",name:"随心换",price:349,isActive:!0,description:""}]})),Ii().catch((e=>{_i.value=[{_id:"mem1",size:"128GB",price:130,isActive:!0,description:""},{_id:"mem2",size:"256GB",price:220,isActive:!0,description:""}]}))]);const a=yield Be.getCustomerDetail(e._id);a.address;let l=[],t="";fo=JSON.parse(JSON.stringify(a)),Object.assign(qe,{_id:a._id,name:a.name,phone:a.phone,email:a.email,region:l,addressDetail:t,address:a.address,tags:a.tags,remark:a.remark,packageType:a.packageType||"",changeOption:a.changeOption||"",memoryCard:a.memoryCard||"",appointmentTime:a.appointmentTime||"",appointmentRemark:a.appointmentRemark||"",status:a.status||"pending"})}catch(a){w.error("获取客户详情失败")}finally{ii.value=!1}})),di=()=>{const e=hi.value.find((e=>e._id===qe.packageType));e&&(qe.packageInfo={name:e.name,price:e.price})},mi=()=>{const e=yi.value.find((e=>e._id===qe.changeOption));e&&(qe.changeOptionInfo={name:e.name,price:e.price})},ri=()=>{const e=_i.value.find((e=>e._id===qe.memoryCard));e&&(qe.memoryCardInfo={size:e.size,price:e.price})},ui=y(0),pi=y(!1),vi=y(),fi=h({packageType:"",changeOption:"",memoryCard:"",totalPrice:"",appointmentTime:"",remark:"",status:"pending"}),gi={packageType:[{required:!0,message:"请选择拍摄套餐",trigger:"change"}],changeOption:[{required:!0,message:"请选择随心换",trigger:"change"}],memoryCard:[{required:!0,message:"请选择内存卡",trigger:"change"}],appointmentTime:[{required:!0,message:"请选择预约时间",trigger:"change"}],status:[{required:!0,message:"请选择预约状态",trigger:"change"}],remark:[{required:!1,message:"请输入预约备注",trigger:"blur"}]},hi=y([]),yi=y([]),_i=y([]);y(!1);const bi=y("forward"),ki=()=>d(this,null,(function*(){Ye.value&&(yield Ye.value.validate((e=>d(this,null,(function*(){if(e){pi.value=!0;try{yield Promise.all([Ci().catch((e=>{hi.value=[{_id:"test1",name:"标准套餐",price:1999,isActive:!0,description:""},{_id:"test2",name:"高级套餐",price:2999,isActive:!0,description:""}]})),xi().catch((e=>{yi.value=[{_id:"change1",name:"标准随心换",price:299,isActive:!0,description:""},{_id:"change2",name:"高级随心换",price:599,isActive:!0,description:""}]})),Ii().catch((e=>{_i.value=[{_id:"mem1",size:"32GB",price:199,isActive:!0,description:""},{_id:"mem2",size:"64GB",price:399,isActive:!0,description:""}]}))]),bi.value="forward",setTimeout((()=>{ui.value=1}),300)}catch(a){w.error("加载预约选项数据失败，但您仍可以继续操作"),bi.value="forward",setTimeout((()=>{ui.value=1}),300)}finally{pi.value=!1}}})))))})),wi=()=>{bi.value="backward",setTimeout((()=>{ui.value=0}),300)},Ci=()=>d(this,null,(function*(){try{const e=yield Ue.getPackageTypes();if(Array.isArray(e))hi.value=e.map((e=>{var a;return s(c({},e),{isActive:null==(a=e.isActive)||a})}));else if(e&&"object"==typeof e){const a=e.data;Array.isArray(a)?hi.value=a.map((e=>{var a;return s(c({},e),{isActive:null==(a=e.isActive)||a})})):hi.value=[]}else hi.value=[]}catch(e){throw hi.value=[],e}})),xi=()=>d(this,null,(function*(){try{const e=yield Ue.getChangeOptions();if(Array.isArray(e))yi.value=e.map((e=>{var a;return s(c({},e),{isActive:null==(a=e.isActive)||a})}));else if(e&&"object"==typeof e){const a=e.data;Array.isArray(a)?yi.value=a.map((e=>{var a;return s(c({},e),{isActive:null==(a=e.isActive)||a})})):yi.value=[]}else yi.value=[]}catch(e){throw yi.value=[],e}})),Ii=()=>d(this,null,(function*(){try{const e=yield Ue.getMemoryCards();if(Array.isArray(e))_i.value=e.map((e=>{var a;return s(c({},e),{isActive:null==(a=e.isActive)||a})}));else if(e&&"object"==typeof e){const a=e.data;Array.isArray(a)?_i.value=a.map((e=>{var a;return s(c({},e),{isActive:null==(a=e.isActive)||a})})):_i.value=[]}else _i.value=[]}catch(e){throw _i.value=[],e}})),Vi=()=>{hi.value.find((e=>e._id===fi.packageType))&&Ti()},Oi=()=>{Ti()},zi=()=>{Ti()},Ti=()=>{let e=0;const a=hi.value.find((e=>e._id===fi.packageType));a&&(e+=a.price);const l=yi.value.find((e=>e._id===fi.changeOption));l&&(e+=l.price);const t=_i.value.find((e=>e._id===fi.memoryCard));t&&(e+=t.price),fi.totalPrice=e},Ai=_((()=>hi.value.find((e=>e._id===fi.packageType)))),ji=_((()=>yi.value.find((e=>e._id===fi.changeOption)))),Di=_((()=>_i.value.find((e=>e._id===fi.memoryCard)))),Si=_((()=>void 0!==Ai.value&&null!==Ai.value||void 0!==ji.value&&null!==ji.value||void 0!==Di.value&&null!==Di.value));_((()=>Si.value));const Pi=_((()=>"edit"===Le.value&&(qe.packageInfo||qe.changeOptionInfo||qe.memoryCardInfo))),Ni=()=>d(this,null,(function*(){if("edit"===Le.value){if(!Ui())return w.info("未做任何修改，无需更新"),void(Ee.value=!1);if(!Ye.value)return;yield Ye.value.validate((e=>d(this,null,(function*(){e&&(Ri.value=!0)}))))}else if("add"===Le.value&&1===ui.value){if(!vi.value)return;yield vi.value.validate((e=>d(this,null,(function*(){e&&(Ri.value=!0)}))))}else if("add"===Le.value&&0===ui.value){if(!Ye.value)return;yield Ye.value.validate((e=>d(this,null,(function*(){e&&(Ri.value=!0)}))))}})),Ri=y(!1),$i=e=>{e&&(fi.appointmentTime=e)},Ui=()=>{if("add"===Le.value)return!0;const e={name:qe.name,phone:qe.phone,email:qe.email,address:qe.address,tags:qe.tags,remark:qe.remark,packageType:qe.packageType,changeOption:qe.changeOption,memoryCard:qe.memoryCard,appointmentTime:qe.appointmentTime,appointmentRemark:qe.appointmentRemark,status:qe.status},a=fo;for(const l in e)if(Object.prototype.hasOwnProperty.call(e,l))if(Array.isArray(e[l])){if(!a[l]||a[l].length!==e[l].length)return!0;const t=a[l],n=e[l];for(let e=0;e<n.length;e++)if(!t.includes(n[e]))return!0}else if(e[l]!==a[l])return!0;return!1},Ei=()=>d(this,null,(function*(){var e;ci.value=!0;try{if("add"===Le.value){const a=s(c({},qe),{appointment:1===ui.value?{packageType:fi.packageType||null,packageInfo:Ai.value?{_id:Ai.value._id,name:Ai.value.name,price:Ai.value.price,isActive:!0}:null,changeOption:fi.changeOption||null,changeOptionInfo:ji.value?{_id:ji.value._id,name:ji.value.name,price:ji.value.price,isActive:!0}:null,memoryCard:fi.memoryCard||null,memoryCardInfo:Di.value?{_id:Di.value._id,size:Di.value.size,price:Di.value.price,isActive:!0}:null,totalPrice:fi.totalPrice||0,appointmentTime:fi.appointmentTime||"",remark:fi.remark||"",status:fi.status||"pending"}:null});0!==ui.value&&(null==(e=a.appointment)?void 0:e.packageType)||delete a.appointment;yield Be.createCustomer(a);w.success(a.appointment?"新增客户及预约成功":"新增客户成功")}else{const e=s(c({},qe),{packageType:qe.packageType||void 0,changeOption:qe.changeOption||void 0,memoryCard:qe.memoryCard||void 0,appointmentTime:qe.appointmentTime||void 0,appointmentRemark:qe.appointmentRemark||void 0,status:qe.status||void 0});yield Be.updateCustomer(qe._id,e),w.success("更新客户成功")}Ri.value=!1,Ee.value=!1,ui.value=0,Object.assign(fi,{packageType:"",changeOption:"",memoryCard:"",totalPrice:"",appointmentTime:"",remark:""}),Ho()}catch(a){w.error("add"===Le.value?"新增失败":"更新失败")}finally{ci.value=!1}})),Bi=e=>{Re.confirm(`确定要删除客户 "${e.name}" 吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>d(this,null,(function*(){var a;try{t.value=!0,yield Be.deleteCustomer(e._id),w.success("删除客户成功"),Ho()}catch(l){if(null==(a=l.response)?void 0:a.data){const a=l.response.data;if("无法删除客户，该客户有关联的预约记录"===a.message)return void Re.confirm(`该客户有 ${a.relatedAppointments||"多个"} 条关联的预约记录。\n${a.solution||""}`,"无法删除客户",{confirmButtonText:"同时删除关联预约",cancelButtonText:"取消",type:"warning",distinguishCancelAndClose:!0}).then((()=>d(this,null,(function*(){var a,l;try{t.value=!0,yield Be.deleteCustomer(e._id,!0),w.success("已删除客户及其关联的预约记录"),Ho()}catch(n){w.error((null==(l=null==(a=n.response)?void 0:a.data)?void 0:l.message)||"删除客户及关联预约失败")}finally{t.value=!1}})))).catch((()=>{}));w.error(a.message||"删除客户失败")}else w.error("删除客户失败")}finally{t.value=!1}})))).catch((()=>{}))},Fi=_((()=>"edit"===Le.value||"add"===Le.value&&1===ui.value||"add"===Le.value&&0===ui.value)),Li=y(He.toCascaderFormat()),Yi=e=>{if(e&&e.length>0){const a=He.getFullAddress(e),l=qe.addressDetail||"";qe.address=a+(l?" "+l:"")}else qe.address=qe.addressDetail||""},qi=()=>{qe.region&&qe.region.length>0?Yi(qe.region):qe.address=qe.addressDetail||""},Mi=()=>{let e=0;return qe.packageInfo&&(e+=qe.packageInfo.price||0),qe.changeOptionInfo&&(e+=qe.changeOptionInfo.price||0),qe.memoryCardInfo&&(e+=qe.memoryCardInfo.price||0),e},Hi=y("basic"),Ji=y([]),Gi=()=>{Re.confirm(`确定要删除选中的 ${Ji.value.length} 位客户吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>d(this,null,(function*(){var e,a,l,n,o,i,c,s;try{t.value=!0;try{const t=yield Be.deleteCustomers(Ji.value.map((e=>e._id)));if((null==(e=t.invalidIds)?void 0:e.length)||(null==(a=t.notFoundIds)?void 0:a.length)){let e=`成功删除 ${t.deletedCount} 个客户`;(null==(l=t.invalidIds)?void 0:l.length)&&(e+=`，${t.invalidIds.length} 个无效ID`),(null==(n=t.notFoundIds)?void 0:n.length)&&(e+=`，${t.notFoundIds.length} 个客户未找到`),w.warning(e)}else w.success(t.message||`成功删除 ${t.deletedCount} 个客户`)}catch(m){"无法删除客户，部分客户有关联的预约记录"===(null==(i=null==(o=m.response)?void 0:o.data)?void 0:i.message)?Re.confirm("部分客户有关联的预约记录。是否同时删除这些预约记录？","无法删除客户",{confirmButtonText:"同时删除关联预约",cancelButtonText:"取消",type:"warning",distinguishCancelAndClose:!0}).then((()=>d(this,null,(function*(){var e,a;try{const e=yield Be.deleteCustomers(Ji.value.map((e=>e._id)),!0);w.success(e.message||`成功删除 ${e.deletedCount} 个客户及其关联的预约记录`)}catch(l){w.error((null==(a=null==(e=l.response)?void 0:e.data)?void 0:a.message)||"删除客户及关联预约失败")}})))).catch((()=>{})):w.error((null==(s=null==(c=m.response)?void 0:c.data)?void 0:s.message)||"批量删除客户失败")}Ho()}finally{t.value=!1}})))).catch((()=>{}))},Wi=e=>{Ji.value=e},Zi=_((()=>Ji.value.length===n.value.length&&n.value.length>0)),Xi=y(),Ki=()=>{var e,a;Zi.value?null==(e=Xi.value)||e.clearSelection():null==(a=Xi.value)||a.toggleAllSelection()};return(e,o)=>{const c=W,s=D,d=R,r=de,u=se,h=ie,y=v,_=f,b=j,k=Y,w=J,Ne=p,Re=ae,Ue=te,Be=pe,Fe=fe,He=ve,ro=be,uo=_e,po=we,fo=ke,_o=Ce,bo=ye,Co=he,xo=xe,zo=ge,Lo=je,Yo=Se,Ho=De,li=C("ripple"),Ci=Ae;return I(),x("div",Je,[V($e,{filter:a,"onUpdate:filter":o[0]||(o[0]=e=>a=e),items:l.value,onSearch:Jo,onReset:Go},null,8,["filter","items"]),V(Be,{class:"list-card art-custom-card"},{header:z((()=>[T("div",Ge,[ho.value?O("",!0):(I(),x(q,{key:0},[T("div",We,[o[47]||(o[47]=T("span",{class:"title"},"客户列表",-1)),g.value?(I(),E(c,{key:0,type:"info",effect:"plain",class:"data-count"},{default:z((()=>[$(N(g.value)+" 条数据 ",1)])),_:1})):O("",!0)]),T("div",Ze,[V(h,{onCommand:ai,trigger:"click",placement:"bottom-end",class:"excel-dropdown"},{dropdown:z((()=>[V(u,{class:"excel-dropdown-menu"},{default:z((()=>[V(r,{command:"import",class:"excel-dropdown-item"},{default:z((()=>[T("div",Xe,[V(s,{class:"dropdown-icon upload-icon"},{default:z((()=>[V(S(me))])),_:1}),o[49]||(o[49]=T("span",{class:"dropdown-title"},"导入Excel",-1))])])),_:1}),V(r,{command:"export",class:"excel-dropdown-item"},{default:z((()=>[T("div",Ke,[V(s,{class:"dropdown-icon download-icon"},{default:z((()=>[V(S(re))])),_:1}),o[50]||(o[50]=T("span",{class:"dropdown-title"},"导出Excel",-1))])])),_:1}),V(r,{command:"template",class:"excel-dropdown-item",divided:""},{default:z((()=>[T("div",Qe,[V(s,{class:"dropdown-icon template-icon"},{default:z((()=>[V(S(ue))])),_:1}),o[51]||(o[51]=T("span",{class:"dropdown-title"},"下载模板",-1))])])),_:1})])),_:1})])),default:z((()=>[oe((I(),E(d,{size:"default",type:"default",plain:!0,class:"excel-button"},{default:z((()=>[V(s,{size:14},{default:z((()=>[V(S(B))])),_:1}),o[48]||(o[48]=T("span",{style:{"margin-left":"4px"}},"Excel",-1)),V(s,{class:"el-icon--right",size:12},{default:z((()=>[V(S(ce))])),_:1})])),_:1,__:[48]})),[[li]])])),_:1}),V(d,{type:"primary",onClick:ni,size:"default",class:"add-button"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(L))])),_:1}),o[52]||(o[52]=T("span",null,"新增客户",-1))])),_:1,__:[52]}),oe((I(),E(d,{class:A(["refresh-button",{refreshing:qo.value}]),onClick:Mo,disabled:qo.value,circle:""},{default:z((()=>[V(s,null,{default:z((()=>[V(S(F))])),_:1})])),_:1},8,["class","disabled"])),[[li]])])],64)),ho.value?(I(),x("div",ea,[o[54]||(o[54]=T("div",{class:"mobile-title-section"},[T("h2",{class:"mobile-title"},"客户列表"),T("div",{class:"mobile-subtitle"},"管理您的客户信息")],-1)),T("div",aa,[T("div",la,[T("div",ta,N(g.value||0),1),o[53]||(o[53]=T("div",{class:"stats-label"},"条数据",-1))])])])):O("",!0),V(y,{ref_key:"excelImportRef",ref:Qo,onImportSuccess:Wo,onImportError:Zo,style:{display:"none"}},null,512),V(_,{ref_key:"excelExportRef",ref:ei,data:Bo.value,filename:"预约客户列表",sheetName:"客户数据",type:"success",headers:Fo,"auto-index":!0,onExportSuccess:Xo,onExportError:Ko,style:{display:"none"}},null,8,["data"])])])),default:z((()=>[Ji.value.length>0?(I(),x("div",{key:0,class:A(["batch-operation",{"ios-batch-operation":yo.value}])},[V(b,{type:"info",closable:!1},{default:z((()=>[T("div",{class:A(["batch-operation-content",{"ios-batch-content":yo.value}])},[T("div",na,[V(s,null,{default:z((()=>[V(S(P))])),_:1}),T("span",null,"已选择 "+N(Ji.value.length)+" 项",1)]),T("div",{class:A(["batch-actions",{"ios-batch-actions":yo.value}])},[V(d,{type:"info",size:(yo.value,"small"),onClick:Ki,class:"batch-btn"},{default:z((()=>[$(N(Zi.value?"取消全选":"全选"),1)])),_:1},8,["size"]),V(d,{type:"danger",size:(yo.value,"small"),onClick:Gi,class:"batch-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(U))])),_:1}),o[55]||(o[55]=$("批量删除 "))])),_:1,__:[55]},8,["size"])],2)],2)])),_:1})],2)):O("",!0),T("div",oa,[t.value||0!==n.value.length?O("",!0):(I(),x("div",ia,[T("div",ca,[V(s,{class:"empty-icon"},{default:z((()=>[V(S(B))])),_:1})]),T("div",sa,[o[58]||(o[58]=T("h3",{class:"empty-title"},"暂无客户数据",-1)),T("p",da,N(Io.value?"当前筛选条件下没有找到匹配的客户":"还没有客户信息，快来添加第一个客户吧！"),1),T("div",ma,[Io.value?(I(),E(d,{key:0,type:"primary",onClick:Vo},{default:z((()=>[V(s,null,{default:z((()=>[V(S(F))])),_:1}),o[56]||(o[56]=$(" 清除筛选 "))])),_:1,__:[56]})):(I(),E(d,{key:1,type:"primary",onClick:ni},{default:z((()=>[V(s,null,{default:z((()=>[V(S(L))])),_:1}),o[57]||(o[57]=$(" 添加客户 "))])),_:1,__:[57]}))])])])),t.value&&0===n.value.length?(I(),x("div",ra,[T("div",ua,[V(k,{variant:"rect",style:{width:"100%",height:"40px"}})]),T("div",pa,[(I(!0),x(q,null,M(ho.value?5:8,(e=>(I(),x("div",{key:e,class:"skeleton-row"},[V(k,{variant:"rect",style:{width:"40px",height:"20px"}}),V(k,{variant:"text",style:{width:"80px"}}),V(k,{variant:"text",style:{width:"100px"}}),V(k,{variant:"text",style:{width:"120px"}}),V(k,{variant:"rect",style:{width:"60px",height:"24px"}}),V(k,{variant:"rect",style:{width:"200px",height:"60px"}}),V(k,{variant:"circle",style:{width:"32px",height:"32px"}}),V(k,{variant:"circle",style:{width:"32px",height:"32px"}}),V(k,{variant:"circle",style:{width:"32px",height:"32px"}})])))),128))])])):O("",!0),ho.value?O("",!0):(I(),E(H,{key:2,name:"table-fade",appear:""},{default:z((()=>[!t.value||n.value.length>0?(I(),E(Ne,{key:0,data:n.value,loading:!1,total:g.value,"current-page":i.value,"onUpdate:currentPage":o[7]||(o[7]=e=>i.value=e),"page-size":m.value,"onUpdate:pageSize":o[8]||(o[8]=e=>m.value=e),"page-sizes":[10,20,50,100],border:!0,stripe:!0,"show-header-background":!0,size:"default","pagination-size":"default","pagination-layout":"total, sizes, prev, pager, next, jumper","header-cell-style":{background:"var(--el-table-row-hover-bg-color)",color:"var(--el-text-color-primary)","text-align":"center","font-weight":"600"},"cell-style":{"text-align":"center"},"virtual-scroll":g.value>100,"item-height":70,"table-layout":"auto",width:"100%",onRowClick:ti,onSelectionChange:Wi,ref_key:"tableRef",ref:Xi,class:"animated-table full-width-table"},{default:z((()=>[V(w,{type:"selection",width:yo.value&&ho.value?45:55,"class-name":yo.value?"ios-selection-column":""},null,8,["width","class-name"]),V(w,{prop:"_id",label:"客户ID","min-width":100,width:ho.value?100:ko("customerId",120),resizable:"",onResizeChange:o[1]||(o[1]=e=>wo("customerId",e))},{default:z((e=>[T("span",{class:"customer-id",onClick:G((a=>So(e.row._id)),["stop"])},N(Do(e.row._id)),9,va)])),_:1},8,["width"]),V(w,{prop:"name",label:"客户姓名","min-width":120,width:ho.value?80:void 0,resizable:"",onResizeChange:o[2]||(o[2]=e=>wo("name",e))},null,8,["width"]),V(w,{prop:"phone",label:"联系电话","min-width":140,width:ho.value?120:ko("phone",160),resizable:"",onResizeChange:o[3]||(o[3]=e=>wo("phone",e))},null,8,["width"]),V(w,{label:"标签","min-width":160,width:ho.value?140:void 0,resizable:"",onResizeChange:o[4]||(o[4]=e=>wo("tags",e))},{default:z((e=>[T("div",fa,[(I(!0),x(q,null,M(e.row.tags,(e=>(I(),E(c,{key:e,size:"small",class:"tag-item",effect:"light"},{default:z((()=>[$(N(e),1)])),_:2},1024)))),128)),e.row.tags&&0!==e.row.tags.length?O("",!0):(I(),x("span",ga,"-"))])])),_:1},8,["width"]),V(w,{label:"套餐配置","min-width":280,width:ho.value?260:void 0,resizable:"",onResizeChange:o[5]||(o[5]=e=>wo("packageConfig",e))},{default:z((e=>{var a,l,t,n,i,c;return[T("div",ha,[(null==(a=e.row.packageInfo)?void 0:a.name)||e.row.packageTypeName?(I(),x("div",ya,[T("div",_a,[V(s,null,{default:z((()=>[V(S(Z))])),_:1})]),T("div",ba,[o[59]||(o[59]=T("span",{class:"config-label"},"套餐",-1)),T("span",ka,N((null==(l=e.row.packageInfo)?void 0:l.name)||e.row.packageTypeName),1)])])):O("",!0),(null==(t=e.row.changeOptionInfo)?void 0:t.name)||e.row.changeOptionName?(I(),x("div",wa,[T("div",Ca,[V(s,null,{default:z((()=>[V(S(F))])),_:1})]),T("div",xa,[o[60]||(o[60]=T("span",{class:"config-label"},"随心换",-1)),T("span",Ia,N((null==(n=e.row.changeOptionInfo)?void 0:n.name)||e.row.changeOptionName),1)])])):O("",!0),(null==(i=e.row.memoryCardInfo)?void 0:i.size)||e.row.memoryCardName?(I(),x("div",Va,[T("div",Oa,[V(s,null,{default:z((()=>[V(S(X))])),_:1})]),T("div",za,[o[61]||(o[61]=T("span",{class:"config-label"},"内存卡",-1)),T("span",Ta,N((null==(c=e.row.memoryCardInfo)?void 0:c.size)||e.row.memoryCardName),1)])])):O("",!0),Po(e.row)?O("",!0):(I(),x("div",Aa,[V(s,{class:"no-config-icon"},{default:z((()=>[V(S(K))])),_:1}),o[62]||(o[62]=T("span",{class:"no-config-text"},"暂无配置",-1))]))])]})),_:1},8,["width"]),V(w,{label:"操作",fixed:"right","min-width":ho.value?120:200,width:ho.value?120:ko("actions",220),resizable:"",onResizeChange:o[6]||(o[6]=e=>wo("actions",e))},{default:z((e=>[T("div",{class:A(["operation-buttons",{"mobile-buttons":ho.value,"ios-buttons":yo.value}])},[V(d,{type:"primary",size:ho.value?"small":"default",circle:"",onClick:G((a=>oi(e.row)),["stop"]),class:"action-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(Q))])),_:1})])),_:2},1032,["size","onClick"]),V(d,{type:"warning",size:ho.value?"small":"default",circle:"",onClick:G((a=>si(e.row)),["stop"]),class:"action-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(ee))])),_:1})])),_:2},1032,["size","onClick"]),V(d,{type:"danger",size:ho.value?"small":"default",circle:"",onClick:G((a=>Bi(e.row)),["stop"]),class:"action-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(U))])),_:1})])),_:2},1032,["size","onClick"])],2)])),_:1},8,["min-width","width"])])),_:1},8,["data","total","current-page","page-size","virtual-scroll"])):O("",!0)])),_:1})),ho.value?(I(),x("div",ja,[t.value&&0===n.value.length?(I(),x("div",Da,[(I(),x(q,null,M(5,(e=>T("div",{key:e,class:"mobile-skeleton-card"},[V(Re,{animated:""},{template:z((()=>[T("div",Sa,[V(k,{variant:"circle",style:{width:"40px",height:"40px"}}),T("div",Pa,[V(k,{variant:"text",style:{width:"60%",height:"16px"}}),V(k,{variant:"text",style:{width:"40%",height:"14px"}})])]),V(k,{variant:"rect",style:{width:"100%",height:"60px","margin-top":"12px"}})])),_:1})]))),64))])):O("",!0),!t.value||n.value.length>0?(I(),E(le,{key:1,name:"card-list",tag:"div",class:"card-container"},{default:z((()=>[(I(!0),x(q,null,M(n.value,((e,a)=>{var l,t,n,i,m,r,u;return I(),x("div",{key:e._id,class:A(["customer-card",{selected:Ji.value.includes(e)}]),onClick:a=>ti(e)},[T("div",Ra,[T("div",$a,[T("div",Ua,[T("span",Ea,N((null==(l=e.name)?void 0:l.charAt(0))||"客"),1)]),T("div",Ba,[T("div",Fa,N(e.name||"未知客户"),1),T("div",La,"ID: "+N(Do(e._id)),1)])]),T("div",Ya,[V(Ue,{modelValue:e.selected,"onUpdate:modelValue":a=>e.selected=a,onChange:a=>(e=>{const a=Ji.value.findIndex((a=>a._id===e._id));e.selected?-1===a&&Ji.value.push(e):a>-1&&Ji.value.splice(a,1)})(e),onClick:o[9]||(o[9]=G((()=>{}),["stop"])),class:"card-checkbox"},null,8,["modelValue","onUpdate:modelValue","onChange"])])]),T("div",qa,[T("div",Ma,[V(s,{class:"info-icon phone-icon"},{default:z((()=>[V(S(ne))])),_:1}),T("span",Ha,N(e.phone||"暂无电话"),1)])]),e.tags&&e.tags.length>0?(I(),x("div",Ja,[T("div",Ga,[(I(!0),x(q,null,M(e.tags,(e=>(I(),E(c,{key:e,size:"small",class:"tag-item-mobile",effect:"light"},{default:z((()=>[$(N(e),1)])),_:2},1024)))),128))])])):O("",!0),T("div",Wa,[o[67]||(o[67]=T("div",{class:"section-title"},"套餐配置",-1)),T("div",Za,[(null==(t=e.packageInfo)?void 0:t.name)||e.packageTypeName?(I(),x("div",Xa,[T("div",Ka,[V(s,null,{default:z((()=>[V(S(Z))])),_:1})]),T("div",Qa,[o[63]||(o[63]=T("span",{class:"config-label-mobile"},"套餐",-1)),T("span",el,N((null==(n=e.packageInfo)?void 0:n.name)||e.packageTypeName),1)])])):O("",!0),(null==(i=e.changeOptionInfo)?void 0:i.name)||e.changeOptionName?(I(),x("div",al,[T("div",ll,[V(s,null,{default:z((()=>[V(S(F))])),_:1})]),T("div",tl,[o[64]||(o[64]=T("span",{class:"config-label-mobile"},"随心换",-1)),T("span",nl,N((null==(m=e.changeOptionInfo)?void 0:m.name)||e.changeOptionName),1)])])):O("",!0),(null==(r=e.memoryCardInfo)?void 0:r.size)||e.memoryCardName?(I(),x("div",ol,[T("div",il,[V(s,null,{default:z((()=>[V(S(X))])),_:1})]),T("div",cl,[o[65]||(o[65]=T("span",{class:"config-label-mobile"},"内存卡",-1)),T("span",sl,N((null==(u=e.memoryCardInfo)?void 0:u.size)||e.memoryCardName),1)])])):O("",!0),Po(e)?O("",!0):(I(),x("div",dl,[V(s,{class:"no-config-icon-mobile"},{default:z((()=>[V(S(K))])),_:1}),o[66]||(o[66]=T("span",{class:"no-config-text-mobile"},"暂无配置",-1))]))])]),T("div",ml,[T("div",{class:A(["action-buttons-mobile",{"ios-mobile-buttons":yo.value}])},[V(d,{size:"small",type:"primary",plain:"",onClick:G((a=>oi(e)),["stop"]),class:"mobile-action-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(Q))])),_:1}),o[68]||(o[68]=T("span",null,"查看",-1))])),_:2,__:[68]},1032,["onClick"]),V(d,{size:"small",type:"success",plain:"",onClick:G((a=>si(e)),["stop"]),class:"mobile-action-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(ee))])),_:1}),o[69]||(o[69]=T("span",null,"编辑",-1))])),_:2,__:[69]},1032,["onClick"]),V(d,{size:"small",type:"danger",plain:"",onClick:G((a=>Bi(e)),["stop"]),class:"mobile-action-btn"},{default:z((()=>[V(s,null,{default:z((()=>[V(S(U))])),_:1}),o[70]||(o[70]=T("span",null,"删除",-1))])),_:2,__:[70]},1032,["onClick"])],2)])],10,Na)})),128))])),_:1})):O("",!0)])):O("",!0)])])),_:1}),V(Lo,{modelValue:Ee.value,"onUpdate:modelValue":o[40]||(o[40]=e=>Ee.value=e),title:"add"===Le.value?"新增客户":"编辑客户",width:ho.value?"90%":"700px","destroy-on-close":"","close-on-click-modal":!1},{footer:z((()=>[T("span",Dl,[1===ui.value&&"add"===Le.value?(I(),E(d,{key:0,onClick:wi},{default:z((()=>o[75]||(o[75]=[$("上一步")]))),_:1,__:[75]})):O("",!0),V(d,{onClick:o[39]||(o[39]=e=>Ee.value=!1)},{default:z((()=>o[76]||(o[76]=[$("取消")]))),_:1,__:[76]}),0===ui.value&&"add"===Le.value?(I(),E(d,{key:1,type:"primary",onClick:ki,loading:pi.value},{default:z((()=>o[77]||(o[77]=[$("下一步")]))),_:1,__:[77]},8,["loading"])):O("",!0),Fi.value?(I(),E(d,{key:2,type:"primary",onClick:Ni,loading:ci.value},{default:z((()=>o[78]||(o[78]=[$("提交")]))),_:1,__:[78]},8,["loading"])):O("",!0)])])),default:z((()=>["add"===Le.value?(I(),E(He,{key:0,active:ui.value,"finish-status":"success",simple:"",style:{"margin-bottom":"20px"}},{default:z((()=>[V(Fe,{title:"客户信息"}),V(Fe,{title:"预约信息"})])),_:1},8,["active"])):O("",!0),oe((I(),x("div",{"element-loading-text":"正在加载数据","element-loading-background":"rgba(255, 255, 255, 0.8)",class:A({"mobile-form-container":ho.value})},[T("div",rl,[oe(T("div",{class:A(["step-content",{animate__animated:"add"===Le.value},{animate__fadeInLeft:0===ui.value&&"backward"===bi.value},{animate__fadeOutLeft:1===ui.value&&"forward"===bi.value}])},["edit"===Le.value?(I(),E(zo,{key:0,modelValue:Hi.value,"onUpdate:modelValue":o[24]||(o[24]=e=>Hi.value=e),type:"card",class:"edit-tabs"},{default:z((()=>[V(Co,{label:"客户信息",name:"basic"},{default:z((()=>[V(bo,{ref_key:"formRef",ref:Ye,model:qe,rules:go,"label-width":"120px","label-position":"right"},{default:z((()=>[T("div",ul,[V(uo,{label:"客户姓名",prop:"name"},{default:z((()=>[V(ro,{modelValue:qe.name,"onUpdate:modelValue":o[10]||(o[10]=e=>qe.name=e),placeholder:"请输入客户姓名"},null,8,["modelValue"])])),_:1}),V(uo,{label:"联系电话",prop:"phone"},{default:z((()=>[V(ro,{modelValue:qe.phone,"onUpdate:modelValue":o[11]||(o[11]=e=>qe.phone=e),placeholder:"请输入联系电话",maxlength:"11","show-word-limit":""},null,8,["modelValue"])])),_:1})]),T("div",pl,[V(uo,{label:"电子邮箱",prop:"email"},{default:z((()=>[V(ro,{modelValue:qe.email,"onUpdate:modelValue":o[12]||(o[12]=e=>qe.email=e),placeholder:"请输入电子邮箱"},null,8,["modelValue"])])),_:1}),V(uo,{label:"客户标签",prop:"tags"},{default:z((()=>[V(fo,{modelValue:qe.tags,"onUpdate:modelValue":o[13]||(o[13]=e=>qe.tags=e),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或创建标签",style:{width:"100%"}},{default:z((()=>[V(po,{label:"VIP客户",value:"VIP"}),V(po,{label:"新客户",value:"新客户"}),V(po,{label:"老客户",value:"老客户"}),V(po,{label:"商业客户",value:"商业客户"})])),_:1},8,["modelValue"])])),_:1})]),V(uo,{label:"所在地区",prop:"region"},{default:z((()=>[V(_o,{modelValue:qe.region,"onUpdate:modelValue":o[14]||(o[14]=e=>qe.region=e),options:Li.value,props:{expandTrigger:"click",checkStrictly:!1,emitPath:!0},placeholder:"请选择所在地区",style:{width:"100%"},onChange:Yi},null,8,["modelValue","options"])])),_:1}),V(uo,{label:"详细地址",prop:"addressDetail"},{default:z((()=>[V(ro,{modelValue:qe.addressDetail,"onUpdate:modelValue":o[15]||(o[15]=e=>qe.addressDetail=e),placeholder:"请输入详细地址",onInput:o[16]||(o[16]=e=>{qe.addressDetail=e,qi()})},null,8,["modelValue"])])),_:1}),V(uo,{label:"备注",prop:"remark"},{default:z((()=>[V(ro,{modelValue:qe.remark,"onUpdate:modelValue":o[17]||(o[17]=e=>qe.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:2,resize:"none"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1}),V(Co,{label:"预约信息",name:"appointment"},{default:z((()=>[V(bo,{ref_key:"formRef",ref:Ye,model:qe,rules:go,"label-width":"120px","label-position":"right"},{default:z((()=>[V(uo,{label:"型号",prop:"packageType"},{default:z((()=>[V(fo,{modelValue:qe.packageType,"onUpdate:modelValue":o[18]||(o[18]=e=>qe.packageType=e),placeholder:"请选择型号",style:{width:"100%"},onChange:di},{default:z((()=>[(I(!0),x(q,null,M(hi.value,(e=>(I(),E(po,{key:e._id,label:e.name,value:e._id},{default:z((()=>[T("div",vl,[T("span",null,N(e.name),1),T("span",fl,"¥"+N(e.price),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"随心换",prop:"changeOption"},{default:z((()=>[V(fo,{modelValue:qe.changeOption,"onUpdate:modelValue":o[19]||(o[19]=e=>qe.changeOption=e),placeholder:"请选择随心换",style:{width:"100%"},onChange:mi},{default:z((()=>[(I(!0),x(q,null,M(yi.value,(e=>(I(),E(po,{key:e._id,label:e.name,value:e._id},{default:z((()=>[T("div",gl,[T("span",null,N(e.name),1),T("span",hl,"¥"+N(e.price),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"内存卡",prop:"memoryCard"},{default:z((()=>[V(fo,{modelValue:qe.memoryCard,"onUpdate:modelValue":o[20]||(o[20]=e=>qe.memoryCard=e),placeholder:"请选择内存卡",style:{width:"100%"},onChange:ri},{default:z((()=>[(I(!0),x(q,null,M(_i.value,(e=>(I(),E(po,{key:e._id,label:e.size,value:e._id},{default:z((()=>[T("div",yl,[T("span",null,N(e.size),1),T("span",_l,"¥"+N(e.price),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"预约时间",prop:"appointmentTime",class:A({"mobile-date-item":ho.value})},{default:z((()=>[V(xo,{modelValue:qe.appointmentTime,"onUpdate:modelValue":o[21]||(o[21]=e=>qe.appointmentTime=e),type:"datetime",placeholder:"选择预约时间",style:{width:"100%"},"value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss","default-time":new Date(2e3,1,1,0,0,0),teleported:!ho.value,"popper-class":ho.value?"mobile-date-picker":"pc-date-picker",onFocus:Oo},null,8,["modelValue","default-time","teleported","popper-class"])])),_:1},8,["class"]),V(uo,{label:"预约备注",prop:"appointmentRemark"},{default:z((()=>[V(ro,{modelValue:qe.appointmentRemark,"onUpdate:modelValue":o[22]||(o[22]=e=>qe.appointmentRemark=e),type:"textarea",placeholder:"请输入预约备注",rows:2,resize:"none"},null,8,["modelValue"])])),_:1}),V(uo,{label:"预约状态",prop:"status"},{default:z((()=>[V(fo,{modelValue:qe.status,"onUpdate:modelValue":o[23]||(o[23]=e=>qe.status=e),placeholder:"请选择预约状态",style:{width:"100%"}},{default:z((()=>[V(po,{label:"待确认",value:"pending"}),V(po,{label:"已确认",value:"confirmed"}),V(po,{label:"已完成",value:"completed"}),V(po,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1})])),_:1},8,["modelValue"])):(I(),E(bo,{key:1,ref_key:"formRef",ref:Ye,model:qe,rules:go,"label-width":"120px","label-position":"right"},{default:z((()=>[T("div",bl,[V(uo,{label:"客户姓名",prop:"name"},{default:z((()=>[V(ro,{modelValue:qe.name,"onUpdate:modelValue":o[25]||(o[25]=e=>qe.name=e),placeholder:"请输入客户姓名"},null,8,["modelValue"])])),_:1}),V(uo,{label:"联系电话",prop:"phone"},{default:z((()=>[V(ro,{modelValue:qe.phone,"onUpdate:modelValue":o[26]||(o[26]=e=>qe.phone=e),placeholder:"请输入联系电话",maxlength:"11","show-word-limit":""},null,8,["modelValue"])])),_:1})]),T("div",kl,[V(uo,{label:"电子邮箱",prop:"email"},{default:z((()=>[V(ro,{modelValue:qe.email,"onUpdate:modelValue":o[27]||(o[27]=e=>qe.email=e),placeholder:"请输入电子邮箱"},null,8,["modelValue"])])),_:1}),V(uo,{label:"客户标签",prop:"tags"},{default:z((()=>[V(fo,{modelValue:qe.tags,"onUpdate:modelValue":o[28]||(o[28]=e=>qe.tags=e),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或创建标签",style:{width:"100%"}},{default:z((()=>[V(po,{label:"VIP客户",value:"VIP"}),V(po,{label:"新客户",value:"新客户"}),V(po,{label:"老客户",value:"老客户"}),V(po,{label:"商业客户",value:"商业客户"})])),_:1},8,["modelValue"])])),_:1})]),V(uo,{label:"所在地区",prop:"region"},{default:z((()=>[V(_o,{modelValue:qe.region,"onUpdate:modelValue":o[29]||(o[29]=e=>qe.region=e),options:Li.value,props:{expandTrigger:"click",checkStrictly:!1,emitPath:!0},placeholder:"请选择所在地区",style:{width:"100%"},onChange:Yi},null,8,["modelValue","options"])])),_:1}),V(uo,{label:"详细地址",prop:"addressDetail"},{default:z((()=>[V(ro,{modelValue:qe.addressDetail,"onUpdate:modelValue":o[30]||(o[30]=e=>qe.addressDetail=e),placeholder:"请输入详细地址",onInput:o[31]||(o[31]=e=>{qe.addressDetail=e,qi()})},null,8,["modelValue"])])),_:1}),V(uo,{label:"备注",prop:"remark"},{default:z((()=>[V(ro,{modelValue:qe.remark,"onUpdate:modelValue":o[32]||(o[32]=e=>qe.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:2,resize:"none"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]))],2),[[Ie,0===ui.value||"edit"===Le.value]]),oe(T("div",{class:A(["step-content step-content-scrollable",{animate__animated:"add"===Le.value},{animate__fadeInRight:1===ui.value&&"forward"===bi.value},{animate__fadeOutRight:0===ui.value&&"backward"===bi.value}])},[V(bo,{ref_key:"appointmentFormRef",ref:vi,model:fi,rules:gi,"label-width":"120px","label-position":"right"},{default:z((()=>[V(uo,{label:"型号",prop:"packageType"},{default:z((()=>[V(fo,{modelValue:fi.packageType,"onUpdate:modelValue":o[33]||(o[33]=e=>fi.packageType=e),placeholder:"请选择型号",style:{width:"100%"},onChange:Vi},{default:z((()=>[(I(!0),x(q,null,M(hi.value,(e=>(I(),E(po,{key:e._id,label:e.name,value:e._id},{default:z((()=>[T("div",wl,[T("span",null,N(e.name),1),T("span",Cl,"¥"+N(e.price),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"随心换",prop:"changeOption"},{default:z((()=>[V(fo,{modelValue:fi.changeOption,"onUpdate:modelValue":o[34]||(o[34]=e=>fi.changeOption=e),placeholder:"请选择随心换",style:{width:"100%"},onChange:Oi},{default:z((()=>[(I(!0),x(q,null,M(yi.value,(e=>(I(),E(po,{key:e._id,label:e.name,value:e._id},{default:z((()=>[T("div",xl,[T("span",null,N(e.name),1),T("span",Il,"¥"+N(e.price),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"内存卡",prop:"memoryCard"},{default:z((()=>[V(fo,{modelValue:fi.memoryCard,"onUpdate:modelValue":o[35]||(o[35]=e=>fi.memoryCard=e),placeholder:"请选择内存卡",style:{width:"100%"},onChange:zi},{default:z((()=>[(I(!0),x(q,null,M(_i.value,(e=>(I(),E(po,{key:e._id,label:e.size,value:e._id},{default:z((()=>[T("div",Vl,[T("span",null,N(e.size),1),T("span",Ol,"¥"+N(e.price),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"预约时间",prop:"appointmentTime",class:A({"mobile-date-item":ho.value})},{default:z((()=>[V(xo,{modelValue:fi.appointmentTime,"onUpdate:modelValue":o[36]||(o[36]=e=>fi.appointmentTime=e),type:"datetime",placeholder:"选择预约时间",style:{width:"100%"},"value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss","default-time":new Date(2e3,1,1,0,0,0),onChange:$i,teleported:!ho.value,"popper-class":ho.value?"mobile-date-picker":"pc-date-picker",onFocus:Oo},null,8,["modelValue","default-time","teleported","popper-class"])])),_:1},8,["class"]),V(uo,{label:"预约状态",prop:"status"},{default:z((()=>[V(fo,{modelValue:fi.status,"onUpdate:modelValue":o[37]||(o[37]=e=>fi.status=e),placeholder:"请选择预约状态",style:{width:"100%"}},{default:z((()=>[V(po,{label:"待确认",value:"pending"},{default:z((()=>[T("div",zl,[V(s,{color:"#E6A23C"},{default:z((()=>[V(S(Ve))])),_:1}),o[71]||(o[71]=T("span",null,"待确认",-1))])])),_:1}),V(po,{label:"已确认",value:"confirmed"},{default:z((()=>[T("div",Tl,[V(s,{color:"#67C23A"},{default:z((()=>[V(S(Oe))])),_:1}),o[72]||(o[72]=T("span",null,"已确认",-1))])])),_:1}),V(po,{label:"已完成",value:"completed"},{default:z((()=>[T("div",Al,[V(s,{color:"#409EFF"},{default:z((()=>[V(S(ze))])),_:1}),o[73]||(o[73]=T("span",null,"已完成",-1))])])),_:1}),V(po,{label:"已取消",value:"cancelled"},{default:z((()=>[T("div",jl,[V(s,{color:"#F56C6C"},{default:z((()=>[V(S(Te))])),_:1}),o[74]||(o[74]=T("span",null,"已取消",-1))])])),_:1})])),_:1},8,["modelValue"])])),_:1}),V(uo,{label:"预约备注",prop:"remark"},{default:z((()=>[V(ro,{modelValue:fi.remark,"onUpdate:modelValue":o[38]||(o[38]=e=>fi.remark=e),type:"textarea",placeholder:"请输入预约备注",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])],2),[[Ie,1===ui.value&&"add"===Le.value]])])],2)),[[Ci,ii.value]])])),_:1},8,["modelValue","title","width"]),V(Lo,{modelValue:Ri.value,"onUpdate:modelValue":o[42]||(o[42]=e=>Ri.value=e),title:"确认提交",width:"500px","append-to-body":"",center:""},{footer:z((()=>[T("div",_t,[V(d,{onClick:o[41]||(o[41]=e=>Ri.value=!1),size:"large"},{default:z((()=>o[88]||(o[88]=[$("取消")]))),_:1,__:[88]}),V(d,{type:"primary",onClick:Ei,loading:ci.value,size:"large"},{default:z((()=>o[89]||(o[89]=[$("确认提交")]))),_:1,__:[89]},8,["loading"])])])),default:z((()=>[T("div",Sl,[T("div",Pl,[V(s,{class:"confirm-icon",color:"#E6A23C"},{default:z((()=>[V(S(K))])),_:1}),T("p",Nl,N("edit"===Le.value?"确认更新客户信息":"确认提交预约信息"),1)]),Si.value||Pi.value?(I(),x("div",Rl,[T("div",$l,N("edit"===Le.value?"预约信息":"已选择项目"),1),T("div",Ul,["add"===Le.value?(I(),x(q,{key:0},[Ai.value?(I(),x("div",El,[T("div",Bl,[V(c,{size:"small",type:"primary",effect:"light"},{default:z((()=>o[79]||(o[79]=[$("型号")]))),_:1,__:[79]})]),T("div",Fl,[T("span",Ll,N(Ai.value.name),1),T("span",Yl,"¥"+N(Ai.value.price),1)])])):O("",!0),ji.value?(I(),x("div",ql,[T("div",Ml,[V(c,{size:"small",type:"success",effect:"light"},{default:z((()=>o[80]||(o[80]=[$("随心换")]))),_:1,__:[80]})]),T("div",Hl,[T("span",Jl,N(ji.value.name),1),T("span",Gl,"¥"+N(ji.value.price),1)])])):O("",!0),Di.value?(I(),x("div",Wl,[T("div",Zl,[V(c,{size:"small",type:"warning",effect:"light"},{default:z((()=>o[81]||(o[81]=[$("内存卡")]))),_:1,__:[81]})]),T("div",Xl,[T("span",Kl,N(Di.value.size),1),T("span",Ql,"¥"+N(Di.value.price),1)])])):O("",!0),T("div",et,[o[82]||(o[82]=T("span",{class:"confirm-total-label"},"总价",-1)),T("span",at,"¥"+N(fi.totalPrice||0),1)])],64)):O("",!0),"edit"===Le.value?(I(),x(q,{key:1},[qe.packageInfo?(I(),x("div",lt,[T("div",tt,[V(c,{size:"small",type:"primary",effect:"light"},{default:z((()=>o[83]||(o[83]=[$("型号")]))),_:1,__:[83]})]),T("div",nt,[T("span",ot,N(qe.packageInfo.name),1),T("span",it,"¥"+N(qe.packageInfo.price),1)])])):O("",!0),qe.changeOptionInfo?(I(),x("div",ct,[T("div",st,[V(c,{size:"small",type:"success",effect:"light"},{default:z((()=>o[84]||(o[84]=[$("随心换")]))),_:1,__:[84]})]),T("div",dt,[T("span",mt,N(qe.changeOptionInfo.name),1),T("span",rt,"¥"+N(qe.changeOptionInfo.price),1)])])):O("",!0),qe.memoryCardInfo?(I(),x("div",ut,[T("div",pt,[V(c,{size:"small",type:"warning",effect:"light"},{default:z((()=>o[85]||(o[85]=[$("内存卡")]))),_:1,__:[85]})]),T("div",vt,[T("span",ft,N(qe.memoryCardInfo.size),1),T("span",gt,"¥"+N(qe.memoryCardInfo.price),1)])])):O("",!0),T("div",ht,[o[86]||(o[86]=T("span",{class:"confirm-total-label"},"总价",-1)),T("span",yt,"¥"+N(Mi()),1)])],64)):O("",!0)])])):O("",!0),o[87]||(o[87]=T("div",{class:"confirm-message"},[T("p",null,"请确认以上信息无误后提交")],-1))])])),_:1},8,["modelValue"]),V(Lo,{modelValue:Me.value,"onUpdate:modelValue":o[46]||(o[46]=e=>Me.value=e),title:"客户详情",width:ho.value?"95%":"1000px","destroy-on-close":"",class:A(["customer-detail-dialog",{"mobile-detail-dialog":ho.value}])},{footer:z((()=>[T("span",to,[V(d,{onClick:o[45]||(o[45]=e=>Me.value=!1)},{default:z((()=>o[117]||(o[117]=[$("关闭")]))),_:1,__:[117]})])])),default:z((()=>{var e,a,l,t,n,i,d,m,r,u,p,v,f;return[vo.value?(I(),x("div",bt,[ho.value?O("",!0):(I(),E(Ho,{key:0,column:2,border:""},{default:z((()=>[V(Yo,{label:"客户ID"},{default:z((()=>[T("span",{class:"customer-id",onClick:o[43]||(o[43]=G((e=>So(vo.value._id)),["stop"]))},N(Do(vo.value._id)),1)])),_:1}),V(Yo,{label:"客户姓名"},{default:z((()=>[$(N(vo.value.name),1)])),_:1}),V(Yo,{label:"联系电话"},{default:z((()=>[$(N(vo.value.phone||"-"),1)])),_:1}),V(Yo,{label:"电子邮箱"},{default:z((()=>[$(N(vo.value.email||"-"),1)])),_:1}),V(Yo,{label:"客户地址",span:2},{default:z((()=>[$(N(vo.value.address||"-"),1)])),_:1}),V(Yo,{label:"标签"},{default:z((()=>[T("div",kt,[(I(!0),x(q,null,M(vo.value.tags,(e=>(I(),E(c,{key:e,size:"small",class:"tag-item",effect:"light"},{default:z((()=>[$(N(e),1)])),_:2},1024)))),128)),vo.value.tags&&0!==vo.value.tags.length?O("",!0):(I(),x("span",wt,"-"))])])),_:1}),V(Yo,{label:"备注",span:2},{default:z((()=>[$(N(vo.value.remark||"-"),1)])),_:1}),V(Yo,{label:"预约状态"},{default:z((()=>[V(c,{type:To(vo.value.status)},{default:z((()=>[$(N(Ao(vo.value.status)),1)])),_:1},8,["type"])])),_:1}),V(Yo,{label:"预约时间"},{default:z((()=>[$(N(jo(vo.value.appointmentTime)),1)])),_:1}),V(Yo,{label:"创建时间"},{default:z((()=>[$(N(jo(vo.value.createdAt)),1)])),_:1}),V(Yo,{label:"更新时间"},{default:z((()=>[$(N(jo(vo.value.updatedAt)),1)])),_:1})])),_:1})),ho.value?(I(),x("div",Ct,[T("div",xt,[(I(!0),x(q,null,M($o.value,((e,a)=>(I(),x("div",{key:a,class:A(["indicator-dot",{active:No.value===a}]),onClick:e=>(e=>{if(!Ro.value)return;const a=e*Ro.value.clientWidth;Ro.value.scrollTo({left:a,behavior:"smooth"}),No.value=e})(a)},null,10,It)))),128))]),T("div",{class:"mobile-cards-wrapper",ref_key:"cardsWrapper",ref:Ro,onScroll:Uo},[T("div",Vt,[T("div",Ot,[T("div",zt,[T("span",Tt,N((null==(e=vo.value.name)?void 0:e.charAt(0))||"客"),1)]),T("div",At,[T("h3",jt,N(vo.value.name||"未知客户"),1),T("div",{class:"customer-id-large",onClick:o[44]||(o[44]=G((e=>So(vo.value._id)),["stop"]))}," ID: "+N(Do(vo.value._id)),1)])]),T("div",Dt,[T("div",St,[T("div",Pt,[o[91]||(o[91]=T("div",{class:"info-icon"},"📞",-1)),T("div",Nt,[o[90]||(o[90]=T("div",{class:"info-label"},"电话",-1)),T("div",Rt,N(vo.value.phone||"暂无"),1)])]),T("div",$t,[o[93]||(o[93]=T("div",{class:"info-icon"},"📧",-1)),T("div",Ut,[o[92]||(o[92]=T("div",{class:"info-label"},"邮箱",-1)),T("div",Et,N(vo.value.email||"暂无"),1)])]),T("div",Bt,[o[95]||(o[95]=T("div",{class:"info-icon"},"📍",-1)),T("div",Ft,[o[94]||(o[94]=T("div",{class:"info-label"},"地址",-1)),T("div",Lt,N(vo.value.address||"暂无"),1)])])])])]),T("div",Yt,[T("div",qt,[V(s,{class:"title-icon"},{default:z((()=>[V(S(P))])),_:1}),o[96]||(o[96]=T("span",null,"状态信息",-1))]),T("div",Mt,[T("div",Ht,[T("div",Jt,[o[97]||(o[97]=T("div",{class:"status-label"},"预约状态",-1)),V(c,{type:To(vo.value.status),size:"small",class:"status-tag"},{default:z((()=>[$(N(Ao(vo.value.status)),1)])),_:1},8,["type"])]),T("div",Gt,[o[98]||(o[98]=T("div",{class:"status-label"},"预约时间",-1)),T("div",Wt,N(jo(vo.value.appointmentTime)),1)]),T("div",Zt,[o[99]||(o[99]=T("div",{class:"status-label"},"创建时间",-1)),T("div",Xt,N(jo(vo.value.createdAt)),1)]),T("div",Kt,[o[100]||(o[100]=T("div",{class:"status-label"},"更新时间",-1)),T("div",Qt,N(jo(vo.value.updatedAt)),1)])])])]),T("div",en,[T("div",an,[V(s,{class:"title-icon"},{default:z((()=>[V(S(Pe))])),_:1}),o[101]||(o[101]=T("span",null,"标签备注",-1))]),T("div",ln,[vo.value.tags&&vo.value.tags.length>0?(I(),x("div",tn,[o[102]||(o[102]=T("div",{class:"section-label"},"客户标签",-1)),T("div",nn,[(I(!0),x(q,null,M(vo.value.tags,(e=>(I(),E(c,{key:e,size:"small",effect:"light",class:"mobile-tag"},{default:z((()=>[$(N(e),1)])),_:2},1024)))),128))])])):O("",!0),vo.value.remark?(I(),x("div",on,[o[103]||(o[103]=T("div",{class:"section-label"},"客户备注",-1)),T("div",cn,N(vo.value.remark),1)])):O("",!0),vo.value.tags&&0!==vo.value.tags.length||vo.value.remark?O("",!0):(I(),x("div",sn,o[104]||(o[104]=[T("div",{class:"empty-icon"},"📝",-1),T("div",{class:"empty-text"},"暂无标签和备注信息",-1)])))])])],544)])):O("",!0),ho.value?O("",!0):(I(),x("div",dn,[T("div",mn,[T("div",rn,[o[109]||(o[109]=T("h3",null,"预约项目",-1)),T("div",un,[vo.value.packageInfo||vo.value.packageType?(I(),x("div",pn,[T("div",vn,[V(c,{size:"small",type:"primary",effect:"light"},{default:z((()=>o[105]||(o[105]=[$("型号")]))),_:1,__:[105]})]),T("div",fn,[T("div",gn,[T("span",hn,N((null==(a=vo.value.packageInfo)?void 0:a.name)||vo.value.packageType),1),T("span",yn,"¥"+N((null==(l=vo.value.packageInfo)?void 0:l.price)||0),1)])])])):O("",!0),vo.value.changeOptionInfo||vo.value.changeOption?(I(),x("div",_n,[T("div",bn,[V(c,{size:"small",type:"success",effect:"light"},{default:z((()=>o[106]||(o[106]=[$("随心换")]))),_:1,__:[106]})]),T("div",kn,[T("div",wn,[T("span",Cn,N((null==(t=vo.value.changeOptionInfo)?void 0:t.name)||vo.value.changeOption),1),T("span",xn,"¥"+N((null==(n=vo.value.changeOptionInfo)?void 0:n.price)||0),1)])])])):O("",!0),vo.value.memoryCardInfo||vo.value.memoryCard?(I(),x("div",In,[T("div",Vn,[V(c,{size:"small",type:"warning",effect:"light"},{default:z((()=>o[107]||(o[107]=[$("内存卡")]))),_:1,__:[107]})]),T("div",On,[T("div",zn,[T("span",Tn,N((null==(i=vo.value.memoryCardInfo)?void 0:i.size)||vo.value.memoryCard),1),T("span",An,"¥"+N((null==(d=vo.value.memoryCardInfo)?void 0:d.price)||0),1)])])])):O("",!0),T("div",jn,[o[108]||(o[108]=T("span",null,"总价：",-1)),T("span",Dn,"¥"+N(Eo(vo.value)),1)])])]),T("div",Sn,[o[110]||(o[110]=T("h3",null,"预约备注",-1)),T("div",Pn,[T("div",Nn,N(vo.value.appointmentRemark||"暂无备注"),1)])])])])),ho.value?(I(),x(q,{key:3},[vo.value.packageInfo||vo.value.changeOptionInfo||vo.value.memoryCardInfo?(I(),x("div",Rn,[T("div",$n,[V(s,{class:"title-icon"},{default:z((()=>[V(S(Z))])),_:1}),o[111]||(o[111]=T("span",null,"预约项目",-1))]),T("div",Un,[vo.value.packageInfo||vo.value.packageType?(I(),x("div",En,[T("div",Bn,[V(c,{size:"small",type:"primary",effect:"light"},{default:z((()=>o[112]||(o[112]=[$("套餐")]))),_:1,__:[112]}),T("span",Fn,"¥"+N((null==(m=vo.value.packageInfo)?void 0:m.price)||0),1)]),T("div",Ln,N((null==(r=vo.value.packageInfo)?void 0:r.name)||vo.value.packageType),1)])):O("",!0),vo.value.changeOptionInfo||vo.value.changeOption?(I(),x("div",Yn,[T("div",qn,[V(c,{size:"small",type:"success",effect:"light"},{default:z((()=>o[113]||(o[113]=[$("随心换")]))),_:1,__:[113]}),T("span",Mn,"¥"+N((null==(u=vo.value.changeOptionInfo)?void 0:u.price)||0),1)]),T("div",Hn,N((null==(p=vo.value.changeOptionInfo)?void 0:p.name)||vo.value.changeOption),1)])):O("",!0),vo.value.memoryCardInfo||vo.value.memoryCard?(I(),x("div",Jn,[T("div",Gn,[V(c,{size:"small",type:"warning",effect:"light"},{default:z((()=>o[114]||(o[114]=[$("内存卡")]))),_:1,__:[114]}),T("span",Wn,"¥"+N((null==(v=vo.value.memoryCardInfo)?void 0:v.price)||0),1)]),T("div",Zn,N((null==(f=vo.value.memoryCardInfo)?void 0:f.size)||vo.value.memoryCard),1)])):O("",!0),T("div",Xn,[o[115]||(o[115]=T("span",{class:"total-label"},"总价",-1)),T("span",Kn,"¥"+N(Eo(vo.value)),1)])])])):O("",!0),vo.value.appointmentRemark?(I(),x("div",Qn,[T("div",eo,[V(s,{class:"title-icon"},{default:z((()=>[V(S(ee))])),_:1}),o[116]||(o[116]=T("span",null,"预约备注",-1))]),T("div",ao,[T("div",lo,N(vo.value.appointmentRemark),1)])])):O("",!0)],64)):O("",!0)])):O("",!0)]})),_:1},8,["modelValue","width","class"]),ho.value?(I(),x("div",no,[T("div",oo,[V(h,{onCommand:ai,trigger:"click",placement:"top",class:"mobile-excel-dropdown"},{dropdown:z((()=>[V(u,{class:"mobile-excel-menu"},{default:z((()=>[V(r,{command:"import",class:"mobile-excel-item"},{default:z((()=>[V(s,{class:"menu-icon"},{default:z((()=>[V(S(me))])),_:1}),o[119]||(o[119]=T("span",null,"导入数据",-1))])),_:1,__:[119]}),V(r,{command:"export",class:"mobile-excel-item"},{default:z((()=>[V(s,{class:"menu-icon"},{default:z((()=>[V(S(re))])),_:1}),o[120]||(o[120]=T("span",null,"导出数据",-1))])),_:1,__:[120]}),V(r,{command:"template",class:"mobile-excel-item",divided:""},{default:z((()=>[V(s,{class:"menu-icon"},{default:z((()=>[V(S(ue))])),_:1}),o[121]||(o[121]=T("span",null,"下载模板",-1))])),_:1,__:[121]})])),_:1})])),default:z((()=>[T("div",io,[T("div",co,[V(s,{size:20},{default:z((()=>[V(S(B))])),_:1})]),o[118]||(o[118]=T("span",{class:"action-label"},"Excel",-1))])])),_:1}),T("div",{class:"action-item primary-action",onClick:ni},[T("div",so,[V(s,{size:24},{default:z((()=>[V(S(L))])),_:1})]),o[122]||(o[122]=T("span",{class:"action-label"},"新增",-1))]),T("div",{class:"action-item",onClick:Mo},[T("div",mo,[V(s,{size:20},{default:z((()=>[V(S(F))])),_:1})]),o[123]||(o[123]=T("span",{class:"action-label"},"刷新",-1))])])])):O("",!0)])}}}),[["__scopeId","data-v-d0496866"]]);export{vo as default};
