/**
 * 控制台日志过滤器
 * 用于过滤第三方库的调试日志和不必要的输出
 */

interface ConsoleFilterConfig {
  /** 是否启用过滤 */
  enabled: boolean
  /** 需要过滤的关键词 */
  filterKeywords: string[]
  /** 需要过滤的文件名 */
  filterFiles: string[]
  /** 是否在开发环境启用 */
  enableInDev: boolean
}

const defaultConfig: ConsoleFilterConfig = {
  enabled: true,
  filterKeywords: [
    'Image URL being set',
    'store installed',
    '🍍',
    '🆕',
    'HMR',
    '[vite]',
    '[Vue warn]'
  ],
  filterFiles: [
    'inspector.js',
    'prepare.js',
    'vue-img-cutter'
  ],
  enableInDev: false
}

class ConsoleFilter {
  private config: ConsoleFilterConfig
  private originalMethods: {
    log: typeof console.log
    warn: typeof console.warn
    info: typeof console.info
    debug: typeof console.debug
  }

  constructor(config: Partial<ConsoleFilterConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
    
    // 保存原始方法
    this.originalMethods = {
      log: console.log,
      warn: console.warn,
      info: console.info,
      debug: console.debug
    }
  }

  /**
   * 初始化控制台过滤器
   */
  init(): void {
    // 检查是否应该启用过滤
    if (!this.shouldEnableFilter()) {
      return
    }

    // 重写console方法
    console.log = this.createFilteredMethod('log')
    console.warn = this.createFilteredMethod('warn')
    console.info = this.createFilteredMethod('info')
    console.debug = this.createFilteredMethod('debug')
  }

  /**
   * 恢复原始console方法
   */
  restore(): void {
    console.log = this.originalMethods.log
    console.warn = this.originalMethods.warn
    console.info = this.originalMethods.info
    console.debug = this.originalMethods.debug
  }

  /**
   * 检查是否应该启用过滤
   */
  private shouldEnableFilter(): boolean {
    if (!this.config.enabled) {
      return false
    }

    // 生产环境总是启用
    if (import.meta.env.PROD) {
      return true
    }

    // 开发环境根据配置决定
    return this.config.enableInDev
  }

  /**
   * 创建过滤后的console方法
   */
  private createFilteredMethod(methodName: keyof typeof this.originalMethods) {
    const originalMethod = this.originalMethods[methodName]
    
    return (...args: any[]) => {
      // 检查是否应该过滤这条日志
      if (this.shouldFilterLog(args)) {
        return
      }

      // 调用原始方法
      originalMethod.apply(console, args)
    }
  }

  /**
   * 检查是否应该过滤日志
   */
  private shouldFilterLog(args: any[]): boolean {
    const message = args.join(' ')

    // 检查关键词过滤
    for (const keyword of this.config.filterKeywords) {
      if (message.includes(keyword)) {
        return true
      }
    }

    // 检查调用栈中的文件名
    const stack = new Error().stack
    if (stack) {
      for (const fileName of this.config.filterFiles) {
        if (stack.includes(fileName)) {
          return true
        }
      }
    }

    return false
  }

  /**
   * 添加过滤关键词
   */
  addFilterKeyword(keyword: string): void {
    if (!this.config.filterKeywords.includes(keyword)) {
      this.config.filterKeywords.push(keyword)
    }
  }

  /**
   * 移除过滤关键词
   */
  removeFilterKeyword(keyword: string): void {
    const index = this.config.filterKeywords.indexOf(keyword)
    if (index > -1) {
      this.config.filterKeywords.splice(index, 1)
    }
  }

  /**
   * 添加过滤文件
   */
  addFilterFile(fileName: string): void {
    if (!this.config.filterFiles.includes(fileName)) {
      this.config.filterFiles.push(fileName)
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ConsoleFilterConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ConsoleFilterConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}

// 创建全局实例
export const consoleFilter = new ConsoleFilter()

// 便捷方法
export const initConsoleFilter = (config?: Partial<ConsoleFilterConfig>) => {
  if (config) {
    consoleFilter.updateConfig(config)
  }
  consoleFilter.init()
}

export const restoreConsole = () => {
  consoleFilter.restore()
}

// 开发环境下的调试方法
export const debugConsoleFilter = () => {
  // 调试方法已禁用console输出
}

export default ConsoleFilter
