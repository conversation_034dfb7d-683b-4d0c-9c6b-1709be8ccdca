import"./index-C5Q-N6Yp.js";/* empty css               *//* empty css               */import{C as a}from"./vue3-count-to.esm-Dsfbf00q.js";import{k as s,r as e,O as l,C as n,bp as t,S as c,x as o,D as r,F as i,Z as d,u,B as p,aY as v,W as b,X as m,a6 as f,aX as g}from"./vendor-9ydHGNSq.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";const h={class:"custom-card art-custom-card today-sales"},_={class:"sales-summary"},y={class:b(["sales-card"])},j=["innerHTML"],T=x(s({__name:"TodaySales",setup(s){const x=e([{label:"总销售额",value:999,change:"+10%",iconfont:"&#xe7d9",class:"bg-primary"},{label:"总订单量",value:300,change:"+15%",iconfont:"&#xe70f",class:"bg-warning"},{label:"产品销售量",value:56,change:"-5%",iconfont:"&#xe712",class:"bg-error"},{label:"新客户数",value:68,change:"+8%",iconfont:"&#xe77f",class:"bg-success"}]);return(s,e)=>{const T=v,k=g;return n(),l("div",h,[e[1]||(e[1]=t('<div class="custom-card-header" data-v-8183b386><span class="title" data-v-8183b386>今日销售</span><span class="subtitle" data-v-8183b386>销售总结</span><div class="export-btn" data-v-8183b386><i class="iconfont-sys" data-v-8183b386></i><span data-v-8183b386>导出</span></div></div>',1)),c("div",_,[o(k,{gutter:20},{default:r((()=>[(n(!0),l(i,null,d(u(x),((s,l)=>(n(),p(T,{span:6,xs:24,key:l},{default:r((()=>[c("div",y,[c("i",{class:"iconfont-sys",innerHTML:s.iconfont},null,8,j),c("h2",null,[o(u(a),{class:"number box-title",endVal:s.value,duration:1e3,separator:""},null,8,["endVal"])]),c("p",null,m(s.label),1),c("small",null,[e[0]||(e[0]=f(" 较昨天 ")),c("span",{class:b([-1===s.change.indexOf("+")?"text-danger":"text-success"])},m(s.change),3)])])])),_:2},1024)))),128))])),_:1})])])}}}),[["__scopeId","data-v-8183b386"]]);export{T as default};
