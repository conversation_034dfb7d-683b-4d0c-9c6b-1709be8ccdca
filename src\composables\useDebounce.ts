import { ref, watch, type Ref } from 'vue'
import { 
  useDebounceFn, 
  useThrottleFn,
  debounce,
  throttle
} from '@vueuse/core'

export interface DebounceOptions {
  /** 延迟时间（毫秒） */
  delay?: number
  /** 是否立即执行 */
  immediate?: boolean
  /** 最大等待时间 */
  maxWait?: number
}

export interface ThrottleOptions {
  /** 延迟时间（毫秒） */
  delay?: number
  /** 是否在开始时执行 */
  leading?: boolean
  /** 是否在结束时执行 */
  trailing?: boolean
}

/**
 * 防抖Hook
 * 基于VueUse的useDebounceFn，提供更便捷的API
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  options: DebounceOptions = {}
): T {
  const { delay = 300, immediate = false, maxWait } = options

  return useDebounceFn(fn, delay, { maxWait }) as T
}

/**
 * 节流Hook
 * 基于VueUse的useThrottleFn，提供更便捷的API
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  options: ThrottleOptions = {}
): T {
  const { delay = 300, leading = true, trailing = true } = options

  return useThrottleFn(fn, delay, trailing, leading) as T
}

/**
 * 防抖值Hook
 * 对响应式值进行防抖处理
 */
export function useDebouncedValue<T>(
  value: Ref<T>,
  delay = 300
): Ref<T> {
  const debouncedValue = ref<T>(value.value) as Ref<T>

  const updateValue = useDebounceFn((newValue: T) => {
    debouncedValue.value = newValue
  }, delay)

  watch(value, updateValue, { immediate: true })

  return debouncedValue
}

/**
 * 节流值Hook
 * 对响应式值进行节流处理
 */
export function useThrottledValue<T>(
  value: Ref<T>,
  delay = 300
): Ref<T> {
  const throttledValue = ref<T>(value.value) as Ref<T>

  const updateValue = useThrottleFn((newValue: T) => {
    throttledValue.value = newValue
  }, delay)

  watch(value, updateValue, { immediate: true })

  return throttledValue
}

/**
 * 搜索防抖Hook
 * 专门用于搜索输入的防抖处理
 */
export function useSearchDebounce(
  searchFn: (keyword: string) => void | Promise<void>,
  delay = 500
) {
  const keyword = ref('')
  const loading = ref(false)

  const debouncedSearch = useDebounceFn(async (value: string) => {
    if (!value.trim()) return

    try {
      loading.value = true
      await searchFn(value.trim())
    } finally {
      loading.value = false
    }
  }, delay)

  watch(keyword, debouncedSearch)

  const search = (value: string) => {
    keyword.value = value
  }

  const clearSearch = () => {
    keyword.value = ''
  }

  return {
    keyword,
    loading,
    search,
    clearSearch
  }
}

/**
 * API调用防抖Hook
 * 防止重复的API调用
 */
export function useApiDebounce<T extends (...args: any[]) => Promise<any>>(
  apiFunction: T,
  delay = 300
): T & { cancel: () => void } {
  const debouncedFn = useDebounceFn(apiFunction, delay)
  
  return debouncedFn as T & { cancel: () => void }
}

/**
 * 表单提交防抖Hook
 * 防止重复提交表单
 */
export function useSubmitDebounce(
  submitFn: () => void | Promise<void>,
  delay = 1000
) {
  const submitting = ref(false)

  const debouncedSubmit = useDebounceFn(async () => {
    if (submitting.value) return

    try {
      submitting.value = true
      await submitFn()
    } finally {
      submitting.value = false
    }
  }, delay)

  return {
    submitting,
    submit: debouncedSubmit
  }
}

/**
 * 窗口大小变化防抖Hook
 * 防抖处理窗口大小变化事件
 */
export function useResizeDebounce(
  callback: () => void,
  delay = 300
) {
  const debouncedCallback = useDebounceFn(callback, delay)

  return {
    onResize: debouncedCallback
  }
}

/**
 * 滚动防抖Hook
 * 防抖处理滚动事件
 */
export function useScrollDebounce(
  callback: (event: Event) => void,
  delay = 100
) {
  const debouncedCallback = useDebounceFn(callback, delay)

  return {
    onScroll: debouncedCallback
  }
}

/**
 * 输入防抖Hook
 * 专门用于输入框的防抖处理
 */
export function useInputDebounce(
  callback: (value: string) => void,
  delay = 300
) {
  const inputValue = ref('')
  
  const debouncedCallback = useDebounceFn((value: string) => {
    callback(value)
  }, delay)

  watch(inputValue, debouncedCallback)

  const setValue = (value: string) => {
    inputValue.value = value
  }

  const clearValue = () => {
    inputValue.value = ''
  }

  return {
    inputValue,
    setValue,
    clearValue
  }
}

/**
 * 批量操作防抖Hook
 * 将多个操作合并为一次执行
 */
export function useBatchDebounce<T>(
  batchFn: (items: T[]) => void | Promise<void>,
  delay = 500
) {
  const pendingItems = ref<T[]>([])

  const debouncedBatch = useDebounceFn(async () => {
    if (pendingItems.value.length === 0) return

    const items = [...pendingItems.value]
    pendingItems.value = []
    
    await batchFn(items)
  }, delay)

  const addItem = (item: T) => {
    pendingItems.value.push(item)
    debouncedBatch()
  }

  const addItems = (items: T[]) => {
    pendingItems.value.push(...items)
    debouncedBatch()
  }

  const clear = () => {
    pendingItems.value = []
  }

  return {
    pendingItems,
    addItem,
    addItems,
    clear
  }
}
