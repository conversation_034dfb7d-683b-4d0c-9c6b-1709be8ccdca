import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-tovWoMSq.js";import{k as a,O as t,C as e,x as i,bp as n,S as r,F as c,Z as p,X as l}from"./vendor-9ydHGNSq.js";import{_ as m}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const d={class:"card art-custom-card"},u={class:"list"},o={class:"subtitle"},v=m(a({__name:"ActiveUser",setup(a){const m=[{name:"总用户量",num:"32k"},{name:"总访问量",num:"128k"},{name:"日访问量",num:"1.2k"},{name:"周同比",num:"+5%"}];return(a,v)=>{const _=s;return e(),t("div",d,[i(_,{class:"chart",barWidth:"50%",height:"13.7rem",showAxisLine:!1,data:[160,100,150,80,190,100,175,120,160],xAxisData:["1","2","3","4","5","6","7","8","9"]}),v[0]||(v[0]=n('<div class="text" data-v-04738e0c><h3 class="box-title" data-v-04738e0c>用户概述</h3><p class="subtitle" data-v-04738e0c>比上周 <span class="text-success" data-v-04738e0c>+23%</span></p><p class="subtitle" data-v-04738e0c>我们为您创建了多个选项，可将它们组合在一起并定制为像素完美的页面</p></div>',1)),r("div",u,[(e(),t(c,null,p(m,((s,a)=>r("div",{key:a},[r("p",null,l(s.num),1),r("p",o,l(s.name),1)]))),64))])])}}}),[["__scopeId","data-v-04738e0c"]]);export{v as default};
