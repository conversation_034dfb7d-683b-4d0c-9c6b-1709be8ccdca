import { ElMessage } from 'element-plus'

// 请求节流记录
interface ThrottleRecord {
  timestamp: number
  count: number
}

// 配置选项
interface ThrottleOptions {
  interval: number // 间隔时间(毫秒)
  maxRequests: number // 间隔内最大请求次数
  message?: string // 自定义提示消息
}

// 默认配置
const DEFAULT_OPTIONS: ThrottleOptions = {
  interval: 5000, // 默认5秒
  maxRequests: 3, // 默认间隔内最多3次请求
  message: '请求频繁'
}

// 请求记录映射表，key为请求URL
const requestRecords: Map<string, ThrottleRecord> = new Map()

// 防止重复显示弹窗的标记
let throttleMessageVisible = false
let throttleMessageTimer: number | null = null

/**
 * 显示节流警告消息
 * @param message 警告消息
 */
function showThrottleWarning(message: string): void {
  // 如果已经显示了警告消息，就不再显示
  if (throttleMessageVisible) return
  
  // 设置标记并显示消息
  throttleMessageVisible = true
  
  ElMessage({
    type: 'warning',
    message,
    duration: 3000,
    onClose: () => {
      throttleMessageVisible = false
      if (throttleMessageTimer) {
        window.clearTimeout(throttleMessageTimer)
        throttleMessageTimer = null
      }
    }
  })
  
  // 3秒后自动重置状态
  throttleMessageTimer = window.setTimeout(() => {
    throttleMessageVisible = false
    throttleMessageTimer = null
  }, 3000)
}

/**
 * API请求节流控制
 * @param url 请求URL
 * @param options 节流选项
 * @returns 是否允许请求
 */
export function throttleRequest(url: string, options: Partial<ThrottleOptions> = {}): boolean {
  const config: ThrottleOptions = { ...DEFAULT_OPTIONS, ...options }
  const now = Date.now()
  const key = url
  
  // 获取该URL的请求记录，如果不存在则创建
  let record = requestRecords.get(key)
  if (!record) {
    record = { timestamp: now, count: 0 }
    requestRecords.set(key, record)
  }
  
  // 检查是否超过了间隔时间，如果是则重置计数
  if (now - record.timestamp > config.interval) {
    record.timestamp = now
    record.count = 1
    return true
  }
  
  // 检查是否超过了最大请求次数
  if (record.count >= config.maxRequests) {
    // 显示简单提示消息
    const message = config.message || '请求频繁'
    showThrottleWarning(message)
    return false
  }
  
  // 允许请求，计数+1
  record.count++
  return true
}

/**
 * 清除特定URL的节流记录
 * @param url 请求URL
 */
export function clearThrottleRecord(url: string): void {
  requestRecords.delete(url)
}

/**
 * 清除所有节流记录
 */
export function clearAllThrottleRecords(): void {
  requestRecords.clear()
}
