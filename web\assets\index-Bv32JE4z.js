var A=Object.defineProperty,e=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,r=(e,s,a)=>s in e?A(e,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[s]=a;import"./index-C5Q-N6Yp.js";/* empty css                    *//* empty css                        *//* empty css                  */import{k as n,aa as p,d,p as o,O as i,C as Q,S as u,F as c,Z as g,X as f,x as E,D as m,a3 as B,a6 as w,bA as v,bq as I}from"./vendor-9ydHGNSq.js";import{_ as C}from"./_plugin-vue_export-helper-BCo6x5W8.js";const R={class:"page-content server"},h={class:"list"},k={class:"middle"},U={class:"header"},j={class:"name"},F={class:"ip"},b={class:"box"},O={class:"left"},z={class:"right"},y=n((M=((A,e)=>{for(var s in e||(e={}))t.call(e,s)&&r(A,s,e[s]);if(a)for(var s of a(e))l.call(e,s)&&r(A,s,e[s]);return A})({},{name:"SafeguardServer"}),e(M,s({__name:"index",setup(A){const e=p([{name:"开发服务器",ip:"*************",cup:85,memory:65,swap:45,disk:92},{name:"测试服务器",ip:"*************",cup:32,memory:78,swap:90,disk:45},{name:"预发布服务器",ip:"*************",cup:95,memory:42,swap:67,disk:88},{name:"线上服务器",ip:"*************",cup:58,memory:93,swap:25,disk:73}]);function s(A=0,e=100){return Math.floor(Math.random()*(e-A+1))+A}function a(){e.forEach((A=>{A.cup=s(),A.memory=s(),A.swap=s(),A.disk=s()}))}let t=null;return d((()=>{t=window.setInterval(a,3e3)})),o((()=>{null!==t&&(window.clearInterval(t),t=null)})),(A,s)=>{const a=B,t=v,l=I;return Q(),i("div",R,[u("div",h,[u("div",k,[(Q(!0),i(c,null,g(e,(A=>(Q(),i("div",{class:"item",key:A.name},[u("div",U,[u("span",j,f(A.name),1),u("span",F,f(A.ip),1)]),u("div",b,[u("div",O,[s[3]||(s[3]=u("img",{src:"data:image/png;base64,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",alt:"服务器"},null,-1)),E(t,{class:"ml-4"},{default:m((()=>[E(a,{type:"primary",size:"default"},{default:m((()=>s[0]||(s[0]=[w("开机")]))),_:1,__:[0]}),E(a,{type:"danger",size:"default"},{default:m((()=>s[1]||(s[1]=[w("关机")]))),_:1,__:[1]}),E(a,{type:"warning",size:"default"},{default:m((()=>s[2]||(s[2]=[w("重启")]))),_:1,__:[2]})])),_:1})]),u("div",z,[u("div",null,[s[4]||(s[4]=u("p",null,"CPU",-1)),E(l,{percentage:A.cup,"text-inside":!0,"stroke-width":17},null,8,["percentage"])]),u("div",null,[s[5]||(s[5]=u("p",null,"RAM",-1)),E(l,{percentage:A.memory,status:"success","text-inside":!0,"stroke-width":17},null,8,["percentage"])]),u("div",null,[s[6]||(s[6]=u("p",null,"SWAP",-1)),E(l,{percentage:A.swap,status:"warning","text-inside":!0,"stroke-width":17},null,8,["percentage"])]),u("div",null,[s[7]||(s[7]=u("p",null,"DISK",-1)),E(l,{percentage:A.disk,status:"success","text-inside":!0,"stroke-width":17},null,8,["percentage"])])])])])))),128))])])])}}}))));var M;const T=C(y,[["__scopeId","data-v-06d7433a"]]);export{T as default};
