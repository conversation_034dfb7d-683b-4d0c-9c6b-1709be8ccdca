import lightTheme from '@imgs/settings/theme_styles/light.png'
import darkTheme from '@imgs/settings/theme_styles/dark.png'
import systemTheme from '@imgs/settings/theme_styles/system.png'
import verticalLayout from '@imgs/settings/menu_layouts/vertical.png'
import horizontalLayout from '@imgs/settings/menu_layouts/horizontal.png'
import mixedLayout from '@imgs/settings/menu_layouts/mixed.png'
import dualColumnLayout from '@imgs/settings/menu_layouts/dual_column.png'
import designStyle from '@imgs/settings/menu_styles/design.png'
import darkStyle from '@imgs/settings/menu_styles/dark.png'
import lightStyle from '@imgs/settings/menu_styles/light.png'

// 配置设置中心图片
export const configImages = {
  themeStyles: {
    light: lightTheme,
    dark: darkTheme,
    system: systemTheme
  },
  menuLayouts: {
    vertical: verticalLayout,
    horizontal: horizontalLayout,
    mixed: mixedLayout,
    dualColumn: dualColumnLayout
  },
  menuStyles: {
    design: designStyle,
    dark: darkStyle,
    light: lightStyle
  }
}
