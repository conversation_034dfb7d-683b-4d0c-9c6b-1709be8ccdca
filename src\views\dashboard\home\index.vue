<template>
  <div class="admin-home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner-section">
      <div class="custom-banner">
        <div class="banner-content">
          <div class="banner-text-area">
            <h1 class="banner-title">
              <span class="greeting">欢迎回来&nbsp</span>
              <span class="user-identity">{{ userIdentity }}！</span>
            </h1>
            <p class="banner-subtitle">
              <span class="date-info">{{ currentDate }}</span>
              <span class="divider">·</span>
              <span class="wish-text">祝您工作愉快</span>
            </p>
          </div>

          <div class="banner-actions-area">
            <div class="action-buttons">
              <el-button type="primary" size="large" :icon="Plus" @click="handleQuickAction('appointment')"
                class="primary-action-btn">
                新建预约
              </el-button>
              <el-button size="large" :icon="Document" @click="handleQuickAction('report')"
                class="secondary-action-btn">
                查看报表
              </el-button>
            </div>
            <div class="quick-stats">
              <div class="stat-item">
                <span class="stat-number">{{ quickStats.todayAppointments }}</span>
                <span class="stat-label">今日预约</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ quickStats.monthlyCompleted }}</span>
                <span class="stat-label">本月完成</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-section">
      <div class="section-header">
        <h2 class="section-title">数据概览</h2>
        <p class="section-subtitle">实时业务数据统计</p>
      </div>
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="12" :lg="6" v-for="stat in statsData" :key="stat.id">
          <div class="modern-stat-card" v-loading="loading">
            <div class="stat-icon-wrapper" :style="{ backgroundColor: stat.iconBg }">
              <i class="iconfont-sys stat-icon" v-html="stat.icon"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">
                <CountTo :endVal="stat.value" :duration="1500" />
              </div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-change" :class="`change-${stat.changeType}`">
                {{ stat.change }}
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <div class="section-header">
        <h2 class="section-title">快捷操作</h2>
        <p class="section-subtitle">常用功能快速入口</p>
      </div>
      <el-row :gutter="20">
        <el-col :xs="12" :sm="8" :md="6" v-for="action in quickActions" :key="action.id">
          <div class="action-card" @click="handleQuickAction(action.key)">
            <div class="action-icon" :style="{ backgroundColor: action.bgColor }">
              <i class="iconfont-sys" v-html="action.icon"></i>
            </div>
            <div class="action-title">{{ action.title }}</div>
            <div class="action-desc">{{ action.description }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动和待办事项 -->
    <el-row :gutter="20" class="activity-section">
      <el-col :xs="24" :md="12">
        <div class="activity-card">
          <div class="card-header">
            <h3 class="card-title">最近活动</h3>
            <el-button text type="primary" @click="viewAllActivities">查看全部</el-button>
          </div>
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-avatar" :style="{ backgroundColor: activity.color }">
                <i class="iconfont-sys" v-html="activity.icon"></i>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-status" :class="activity.statusClass">
                {{ activity.status }}
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :md="12">
        <div class="todo-card">
          <div class="card-header">
            <h3 class="card-title">待办事项</h3>
            <el-button text type="primary" @click="addTodo">添加</el-button>
          </div>
          <div class="todo-list">
            <div v-for="todo in todoList" :key="todo.id" class="todo-item">
              <el-checkbox v-model="todo.completed" @change="toggleTodo(todo)">
                <span :class="{ 'completed': todo.completed }">{{ todo.title }}</span>
              </el-checkbox>
              <div class="todo-priority" :class="todo.priority">
                {{ todo.priorityText }}
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <div class="system-status-section">
      <div class="section-header">
        <h2 class="section-title">系统状态</h2>
        <p class="section-subtitle">实时监控系统运行状态</p>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="status in systemStatus" :key="status.id">
          <div class="status-card">
            <div class="status-header">
              <span class="status-title">{{ status.title }}</span>
              <div class="status-indicator" :class="status.status"></div>
            </div>
            <div class="status-value">{{ status.value }}</div>
            <div class="status-desc">{{ status.description }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import { Plus, Document } from '@element-plus/icons-vue'
import { CountTo } from 'vue3-count-to'
import { AppointmentService } from '@/api/appointmentApi'
import type { AppointmentStatsResponse, CustomerStatsResponse, TodayAppointmentsResponse } from '@/types/appointment'

defineOptions({ name: 'AdminHome' })

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.getUserInfo)

// 用户身份显示
const userIdentity = computed(() => {
  const nickname = userInfo.value.nickname

  // 如果昵称是"超级管理员"，直接返回，避免重复
  if (nickname === '超级管理员') {
    return '超级管理员'
  }

  // 如果昵称是"管理员"，直接返回，避免重复
  if (nickname === '管理员') {
    return '管理员'
  }

  // 否则使用用户名
  const displayName = userInfo.value.name || userInfo.value.username || '用户'
  return displayName
})

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }
  return now.toLocaleDateString('zh-CN', options)
})

// 快速统计数据（横幅中的数据）
const quickStats = ref({
  todayAppointments: 0,
  monthlyCompleted: 0
})

// 统计数据
const statsData = ref([
  {
    id: 1,
    label: '今日预约',
    value: 0,
    icon: '&#xe7f6;',
    iconBg: '#3b82f6',
    change: '今日',
    changeType: 'neutral'
  },
  {
    id: 2,
    label: '待处理',
    value: 0,
    icon: '&#xe724;',
    iconBg: '#f59e0b',
    change: '待处理',
    changeType: 'neutral'
  },
  {
    id: 3,
    label: '已完成',
    value: 0,
    icon: '&#xe82a;',
    iconBg: '#10b981',
    change: '已完成',
    changeType: 'positive'
  },
  {
    id: 4,
    label: '总客户数',
    value: 0,
    icon: '&#xe813;',
    iconBg: '#8b5cf6',
    change: '总客户',
    changeType: 'neutral'
  }
])

// 数据加载状态
const loading = ref(false)

// 快捷操作
const quickActions = ref([
  {
    id: 1,
    key: 'appointment',
    title: '预约管理',
    description: '查看和管理预约',
    icon: '&#xe7f6;',
    bgColor: '#2196f3'
  },
  {
    id: 2,
    key: 'users',
    title: '用户管理',
    description: '管理系统用户',
    icon: '&#xe813;',
    bgColor: '#4caf50'
  },
  {
    id: 3,
    key: 'roles',
    title: '角色权限',
    description: '配置角色权限',
    icon: '&#xe7b9;',
    bgColor: '#ff9800'
  },
  {
    id: 4,
    key: 'settings',
    title: '系统设置',
    description: '系统配置管理',
    icon: '&#xe721;',
    bgColor: '#9c27b0'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '新用户注册',
    time: '5分钟前',
    icon: '&#xe813;',
    color: '#4caf50',
    status: '已处理',
    statusClass: 'success'
  },
  {
    id: 2,
    title: '预约申请',
    time: '10分钟前',
    icon: '&#xe7f6;',
    color: '#2196f3',
    status: '待审核',
    statusClass: 'warning'
  },
  {
    id: 3,
    title: '系统更新',
    time: '1小时前',
    icon: '&#xe721;',
    color: '#ff9800',
    status: '已完成',
    statusClass: 'success'
  },
  {
    id: 4,
    title: '数据备份',
    time: '2小时前',
    icon: '&#xe806;',
    color: '#9c27b0',
    status: '进行中',
    statusClass: 'info'
  }
])

// 待办事项
const todoList = ref([
  {
    id: 1,
    title: '审核待处理预约',
    completed: false,
    priority: 'high',
    priorityText: '高'
  },
  {
    id: 2,
    title: '更新系统配置',
    completed: false,
    priority: 'medium',
    priorityText: '中'
  },
  {
    id: 3,
    title: '检查服务器状态',
    completed: true,
    priority: 'low',
    priorityText: '低'
  },
  {
    id: 4,
    title: '备份数据库',
    completed: false,
    priority: 'high',
    priorityText: '高'
  }
])

// 系统状态
const systemStatus = ref([
  {
    id: 1,
    title: '服务器状态',
    value: '正常运行',
    description: '99.9% 可用性',
    status: 'online'
  },
  {
    id: 2,
    title: '数据库',
    value: '连接正常',
    description: '响应时间 < 100ms',
    status: 'online'
  },
  {
    id: 3,
    title: '存储空间',
    value: '75% 已使用',
    description: '剩余 250GB',
    status: 'warning'
  },
  {
    id: 4,
    title: '内存使用',
    value: '68% 已使用',
    description: '8GB / 12GB',
    status: 'online'
  }
])

// 方法
const handleQuickAction = (key: string) => {
  switch (key) {
    case 'appointment':
      router.push('/appointment/list')
      break
    case 'users':
      router.push('/system/user')
      break
    case 'roles':
      router.push('/system/role')
      break
    case 'settings':
      router.push('/appointment/config')
      break
    case 'report':
      ElMessage.info('报表功能开发中...')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}



const viewAllActivities = () => {
  ElMessage.info('查看全部活动功能开发中...')
}

const addTodo = () => {
  ElMessage.info('添加待办事项功能开发中...')
}

const toggleTodo = (todo: any) => {
  ElMessage.success(todo.completed ? '任务已完成' : '任务已重新激活')
}

// 获取预约统计数据
const fetchAppointmentStats = async () => {
  try {
    loading.value = true

    // 使用预约列表接口获取真实数据进行统计
    await fetchStatsFromAppointmentList()

  } catch (error) {
    console.error('获取预约统计数据失败:', error)
    // 使用默认数据
    setDefaultStats()
  } finally {
    loading.value = false
  }
}

// 从预约列表数据计算统计信息
const fetchStatsFromAppointmentList = async () => {
  try {
    // 获取预约列表数据
    const appointmentResponse = await AppointmentService.getAppointmentList({
      limit: 1000,
      page: 1
    })

    // 检查预约响应数据结构
    let appointments = []
    if (appointmentResponse && Array.isArray(appointmentResponse)) {
      // 如果直接返回数组
      appointments = appointmentResponse
    } else if (appointmentResponse && appointmentResponse.items && Array.isArray(appointmentResponse.items)) {
      // 如果返回对象包含items数组
      appointments = appointmentResponse.items
    } else if (appointmentResponse && appointmentResponse.data && Array.isArray(appointmentResponse.data)) {
      // 如果返回对象包含data数组
      appointments = appointmentResponse.data
    } else {
      appointments = []
    }

    // 同时获取客户总数
    const customerResponse = await AppointmentService.getCustomerList({ limit: 1000 })
    let totalCustomers = 0
    if (customerResponse && Array.isArray(customerResponse)) {
      totalCustomers = customerResponse.length
    } else if (customerResponse && customerResponse.items && Array.isArray(customerResponse.items)) {
      totalCustomers = customerResponse.items.length
    } else if (customerResponse && customerResponse.total) {
      totalCustomers = customerResponse.total
    }

    if (appointments.length > 0) {
      const today = new Date()
      const todayStr = today.toISOString().split('T')[0]

      // 统计今日预约
      const todayAppointments = appointments.filter(appointment => {
        if (appointment.appointmentTime) {
          const appointmentDate = new Date(appointment.appointmentTime).toISOString().split('T')[0]
          return appointmentDate === todayStr
        }
        return false
      }).length

      // 统计待处理预约（待确认状态）
      const pendingAppointments = appointments.filter(appointment =>
        appointment.status === 'pending' || appointment.status === '待确认'
      ).length

      // 统计已完成预约
      const completedAppointments = appointments.filter(appointment =>
        appointment.status === 'completed' || appointment.status === '已完成'
      ).length

      // 计算本月完成数（用于横幅显示）
      const currentMonth = today.getMonth()
      const currentYear = today.getFullYear()
      const monthlyCompleted = appointments.filter(appointment => {
        if ((appointment.status === 'completed' || appointment.status === '已完成') && appointment.appointmentTime) {
          const appointmentDate = new Date(appointment.appointmentTime)
          return appointmentDate.getMonth() === currentMonth && appointmentDate.getFullYear() === currentYear
        }
        return false
      }).length

      // 更新数据
      statsData.value[0].value = todayAppointments
      statsData.value[0].change = '今日'
      statsData.value[0].changeType = 'positive'

      statsData.value[1].value = pendingAppointments
      statsData.value[1].change = '待处理'
      statsData.value[1].changeType = pendingAppointments > 0 ? 'positive' : 'neutral'

      statsData.value[2].value = completedAppointments
      statsData.value[2].change = '已完成'
      statsData.value[2].changeType = 'positive'

      statsData.value[3].value = totalCustomers
      statsData.value[3].change = '总客户'
      statsData.value[3].changeType = 'positive'

      // 更新快速统计数据
      quickStats.value.todayAppointments = todayAppointments
      quickStats.value.monthlyCompleted = monthlyCompleted

    } else {
      // 如果没有预约数据，但有客户数据
      statsData.value[0].value = 0
      statsData.value[0].change = '今日'
      statsData.value[0].changeType = 'neutral'

      statsData.value[1].value = 0
      statsData.value[1].change = '待处理'
      statsData.value[1].changeType = 'neutral'

      statsData.value[2].value = 0
      statsData.value[2].change = '已完成'
      statsData.value[2].changeType = 'neutral'

      statsData.value[3].value = totalCustomers
      statsData.value[3].change = '总客户'
      statsData.value[3].changeType = totalCustomers > 0 ? 'positive' : 'neutral'

      // 更新快速统计数据
      quickStats.value.todayAppointments = 0
      quickStats.value.monthlyCompleted = 0
    }

  } catch (error) {
    console.error('从预约列表获取统计数据失败:', error)
    setDefaultStats()
  }
}

// 设置默认统计数据
const setDefaultStats = () => {
  statsData.value[0].value = 0
  statsData.value[0].change = '今日'
  statsData.value[0].changeType = 'positive'

  statsData.value[1].value = 0
  statsData.value[1].change = '待处理'
  statsData.value[1].changeType = 'positive'

  statsData.value[2].value = 0
  statsData.value[2].change = '已完成'
  statsData.value[2].changeType = 'positive'

  statsData.value[3].value = 0
  statsData.value[3].change = '总数'
  statsData.value[3].changeType = 'positive'

  quickStats.value.todayAppointments = 0
  quickStats.value.monthlyCompleted = 0
}

// 获取今日预约数据（用于横幅显示）
const fetchTodayAppointments = async () => {
  try {
    const response = await AppointmentService.getTodayAppointments() as TodayAppointmentsResponse
    quickStats.value.todayAppointments = response.total
  } catch (error) {
    // 不设置默认值，让统计数据函数处理
  }
}

onMounted(async () => {
  // 确保用户信息是最新的
  await userStore.updateUserInfo()

  // 页面加载时获取真实数据
  fetchAppointmentStats()
  // fetchTodayAppointments() // 今日预约数据已在 fetchAppointmentStats 中处理
})
</script>

<style lang="scss" scoped>
.admin-home {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 60px);

  .welcome-banner-section {
    margin-bottom: 32px;

    .custom-banner {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(93, 135, 255, 0.1) 50%, rgba(73, 190, 255, 0.15) 100%);
      border-radius: 20px;
      padding: 0;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(93, 135, 255, 0.12);
      position: relative;
      border: 1px solid rgba(93, 135, 255, 0.08);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(93, 135, 255, 0.03) 0%, rgba(73, 190, 255, 0.05) 100%);
        pointer-events: none;
      }

      .banner-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 40px 48px;
        position: relative;
        z-index: 1;
        gap: 40px;
        min-height: 160px;

        .banner-text-area {
          flex: 1;
          max-width: 60%;

          .banner-title {
            margin: 0 0 16px 0;
            font-size: 2.25rem;
            font-weight: 700;
            line-height: 1.2;
            color: rgb(var(--art-gray-800));
            letter-spacing: -0.02em;

            .greeting {
              display: inline-block;
              opacity: 0.9;
              font-weight: 500;
              color: rgb(var(--art-gray-700));
            }

            .user-identity {
              display: inline-block;
              background: linear-gradient(135deg, rgb(var(--art-primary)) 0%, rgb(var(--art-secondary)) 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              font-weight: 700;

              @supports not (background-clip: text) {
                background: none;
                color: rgb(var(--art-primary));
              }
            }
          }

          .banner-subtitle {
            margin: 0;
            font-size: 1.1rem;
            color: rgb(var(--art-gray-600));
            font-weight: 400;
            line-height: 1.5;
            display: flex;
            align-items: center;
            gap: 12px;

            .date-info {
              font-weight: 500;
              color: rgb(var(--art-gray-700));
            }

            .divider {
              opacity: 0.6;
              font-size: 0.9rem;
              color: rgb(var(--art-gray-500));
            }

            .wish-text {
              opacity: 0.85;
              color: rgb(var(--art-gray-600));
            }
          }
        }

        .banner-actions-area {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 24px;
          flex-shrink: 0;

          .action-buttons {
            display: flex;
            gap: 16px;
            align-items: center;
            justify-content: center;

            .primary-action-btn,
            .secondary-action-btn {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              white-space: nowrap;
              text-align: center;
              min-width: 120px;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .primary-action-btn {
              background: rgb(var(--art-primary));
              border: 2px solid rgb(var(--art-primary));
              color: white;
              font-weight: 600;
              padding: 12px 24px;
              border-radius: 12px;
              box-shadow: 0 4px 16px rgba(var(--art-primary), 0.25);

              &:hover {
                background: rgb(var(--art-secondary));
                border-color: rgb(var(--art-secondary));
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(var(--art-primary), 0.35);
                color: white;
              }

              &:active {
                transform: translateY(0);
              }
            }

            .secondary-action-btn {
              background: rgba(255, 255, 255, 0.9);
              border: 2px solid rgba(var(--art-primary), 0.2);
              color: rgb(var(--art-primary));
              font-weight: 500;
              padding: 12px 24px;
              border-radius: 12px;
              backdrop-filter: blur(10px);

              &:hover {
                background: rgba(255, 255, 255, 1);
                border-color: rgba(var(--art-primary), 0.4);
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(var(--art-primary), 0.15);
                color: rgb(var(--art-secondary));
              }

              &:active {
                transform: translateY(0);
              }
            }
          }

          .quick-stats {
            display: flex;
            gap: 32px;
            align-items: center;

            .stat-item {
              text-align: center;
              color: rgb(var(--art-gray-700));

              .stat-number {
                display: block;
                font-size: 1.75rem;
                font-weight: 700;
                line-height: 1;
                margin-bottom: 4px;
                background: linear-gradient(135deg, rgb(var(--art-primary)) 0%, rgb(var(--art-secondary)) 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

                @supports not (background-clip: text) {
                  background: none;
                  color: rgb(var(--art-primary));
                }
              }

              .stat-label {
                display: block;
                font-size: 0.875rem;
                opacity: 0.8;
                font-weight: 500;
                letter-spacing: 0.02em;
                color: rgb(var(--art-gray-600));
              }
            }
          }
        }
      }
    }
  }

  .stats-section {
    margin-bottom: 40px;

    .section-header {
      margin-bottom: 32px;
      text-align: left;

      .section-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        color: rgb(var(--art-gray-800));
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, rgb(var(--art-primary)) 0%, rgb(var(--art-secondary)) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        @supports not (background-clip: text) {
          background: none;
          color: rgb(var(--art-primary));
        }
      }

      .section-subtitle {
        font-size: 1rem;
        color: rgb(var(--art-gray-600));
        font-weight: 400;
        margin: 0;
        opacity: 0.85;
      }
    }

    .modern-stat-card {
      display: flex;
      align-items: center;
      height: 8rem;
      padding: 0 20px;
      cursor: pointer;
      background-color: var(--el-bg-color);
      border-radius: calc(var(--el-border-radius-base) + 4px);
      border: 1px solid var(--el-border-color-light);
      transition: all 0.3s ease;
      margin-bottom: 24px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        border-color: var(--el-color-primary);
      }

      .stat-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 46px;
        height: 46px;
        margin-right: 16px;
        border-radius: 50%;
        flex-shrink: 0;

        .stat-icon {
          font-size: 24px;
          color: #fff;
          font-weight: 600;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          line-height: 1.2;
        }

        .stat-label {
          margin: 4px 0 0;
          font-size: 14px;
          color: var(--el-text-color-regular);
          font-weight: 500;
        }

        .stat-change {
          margin-top: 2px;
          font-size: 12px;
          font-weight: 500;

          &.change-positive {
            color: var(--el-color-success);
          }

          &.change-negative {
            color: var(--el-color-danger);
          }

          &.change-neutral {
            color: var(--el-text-color-secondary);
          }
        }

      }
    }
  }

  .quick-actions-section {
    margin-bottom: 30px;

    .section-header {
      margin-bottom: 32px;
      text-align: left;

      .section-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        color: rgb(var(--art-gray-800));
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        @supports not (background-clip: text) {
          background: none;
          color: #3b82f6;
        }
      }

      .section-subtitle {
        font-size: 1rem;
        color: var(--el-text-color-regular);
        font-weight: 400;
        margin: 0;
        opacity: 0.85;
      }
    }

    .action-card {
      background: var(--el-bg-color);
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      height: 140px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;

        .iconfont-sys {
          font-size: 20px;
          color: white;
        }
      }

      .action-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      .action-desc {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .activity-section {
    margin-bottom: 30px;

    .activity-card,
    .todo-card {
      background: var(--el-bg-color);
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      height: 400px;
      display: flex;
      flex-direction: column;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--el-border-color-lighter);

        .card-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }

      .activity-list,
      .todo-list {
        flex: 1;
        overflow-y: auto;
      }

      .activity-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }

        .activity-avatar {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .iconfont-sys {
            font-size: 16px;
            color: white;
          }
        }

        .activity-content {
          flex: 1;

          .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .activity-time {
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }

        .activity-status {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;
          font-weight: 500;

          &.success {
            background-color: var(--el-color-success-light-9);
            color: var(--el-color-success);
          }

          &.warning {
            background-color: var(--el-color-warning-light-9);
            color: var(--el-color-warning);
          }

          &.info {
            background-color: var(--el-color-info-light-9);
            color: var(--el-color-info);
          }
        }
      }

      .todo-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }

        .completed {
          text-decoration: line-through;
          color: var(--el-text-color-disabled);
        }

        .todo-priority {
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 4px;
          font-weight: 500;

          &.high {
            background-color: var(--el-color-danger-light-9);
            color: var(--el-color-danger);
          }

          &.medium {
            background-color: var(--el-color-warning-light-9);
            color: var(--el-color-warning);
          }

          &.low {
            background-color: var(--el-color-info-light-9);
            color: var(--el-color-info);
          }
        }
      }
    }
  }

  .system-status-section {
    .section-header {
      margin-bottom: 32px;
      text-align: left;

      .section-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        color: rgb(var(--art-gray-800));
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        @supports not (background-clip: text) {
          background: none;
          color: #3b82f6;
        }
      }

      .section-subtitle {
        font-size: 1rem;
        color: var(--el-text-color-regular);
        font-weight: 400;
        margin: 0;
        opacity: 0.85;
      }
    }

    .status-card {
      background: var(--el-bg-color);
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      height: 120px;

      .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .status-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online {
            background-color: var(--el-color-success);
          }

          &.warning {
            background-color: var(--el-color-warning);
          }

          &.offline {
            background-color: var(--el-color-danger);
          }
        }
      }

      .status-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
      }

      .status-desc {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

// 渐变动画
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

// 平板端适配
@media (max-width: 1024px) and (min-width: 769px) {
  .admin-home {
    .welcome-banner-section {
      .custom-banner {
        .banner-content {
          padding: 36px 32px;
          gap: 32px;

          .banner-text-area {
            .banner-title {
              font-size: 2rem;
            }

            .banner-subtitle {
              font-size: 1.05rem;
            }
          }

          .banner-actions-area {
            .action-buttons {
              gap: 12px;

              .primary-action-btn,
              .secondary-action-btn {
                padding: 10px 20px;
              }
            }

            .quick-stats {
              gap: 24px;

              .stat-item {
                .stat-number {
                  font-size: 1.5rem;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 中等屏幕适配 (768px - 990px)
@media (min-width: 768px) and (max-width: 990px) {
  .admin-home {
    .stats-section {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }

      .stat-card {
        padding: 20px;
        height: 110px;

        .stat-content {
          .stat-value {
            font-size: 1.8rem;
          }

          .stat-label {
            font-size: 0.85rem;
          }

          .stat-change {
            font-size: 0.75rem;
          }
        }

        .stat-icon {
          width: 48px;
          height: 48px;

          i {
            font-size: 20px;
          }
        }
      }
    }
  }
}

// 大屏幕适配 (990px - 2000px)
@media (min-width: 990px) and (max-width: 2000px) {
  .admin-home {
    .stats-section {
      .el-row {
        max-width: 1400px;
        margin: 0 auto;

        .el-col {
          margin-bottom: 20px;
        }
      }

      .stat-card {
        padding: 28px;
        height: 130px;
        max-width: 320px;
        margin: 0 auto;

        .stat-content {
          .stat-value {
            font-size: 2.2rem;
            font-weight: 600;
          }

          .stat-label {
            font-size: 0.95rem;
            margin-top: 8px;
          }

          .stat-change {
            font-size: 0.8rem;
            margin-top: 6px;
          }
        }

        .stat-icon {
          width: 56px;
          height: 56px;

          i {
            font-size: 24px;
          }
        }
      }
    }

    .quick-actions-section {
      .el-row {
        max-width: 1400px;
        margin: 0 auto;
      }

      .action-card {
        max-width: 320px;
        margin: 0 auto;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .admin-home {
    padding: 12px;

    .welcome-banner-section {
      margin-bottom: 20px;

      .custom-banner {
        border-radius: 16px;

        .banner-content {
          flex-direction: column;
          padding: 28px 20px;
          gap: 24px;
          text-align: center;
          min-height: auto;
          align-items: center;
          justify-content: center;

          .banner-text-area {
            width: 100%;
            max-width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            .banner-title {
              font-size: 1.65rem;
              margin-bottom: 12px;
              text-align: center;
              width: 100%;

              .greeting,
              .user-identity {
                display: block;
                line-height: 1.3;
                text-align: center;
              }

              .greeting {
                margin-bottom: 4px;
              }
            }

            .banner-subtitle {
              font-size: 0.95rem;
              flex-direction: column;
              gap: 6px;
              text-align: center;
              width: 100%;
              justify-content: center;
              align-items: center;

              .divider {
                display: none;
              }

              .date-info,
              .wish-text {
                text-align: center;
                width: 100%;
              }
            }
          }

          .banner-actions-area {
            align-items: center;
            gap: 20px;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .action-buttons {
              flex-direction: column;
              gap: 12px;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              .primary-action-btn,
              .secondary-action-btn {
                width: 180px;
                padding: 12px 20px;
                font-size: 0.95rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                margin: 0 auto;
              }
            }

            .quick-stats {
              gap: 20px;
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;

              .stat-item {
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;

                .stat-number {
                  font-size: 1.4rem;
                  text-align: center;
                }

                .stat-label {
                  font-size: 0.8rem;
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }

    .stats-section {
      margin-bottom: 24px;

      .section-header {
        margin-bottom: 20px;
        text-align: center;

        .section-title {
          font-size: 1.5rem;
          margin: 0 0 6px 0;
        }

        .section-subtitle {
          font-size: 0.9rem;
        }
      }

      .modern-stat-card {
        height: 120px;
        margin-bottom: 16px;
        border-radius: 16px;

        .card-content {
          padding: 20px;

          .stat-header {
            margin-bottom: 12px;

            .stat-icon-wrapper {
              width: 40px;
              height: 40px;
              border-radius: 12px;

              .stat-icon {
                font-size: 18px;
              }
            }

            .stat-trend {
              padding: 3px 6px;
              font-size: 10px;

              .trend-text {
                font-size: 10px;
              }
            }
          }

          .stat-body {
            .stat-value {
              font-size: 2rem;
              margin-bottom: 6px;
              font-weight: 800;
            }

            .stat-label {
              font-size: 0.85rem;
              font-weight: 600;
            }
          }
        }
      }
    }

    .quick-actions-section {
      margin-bottom: 20px;

      .action-card {
        height: auto;
        padding: 16px;
        margin-bottom: 12px;
      }
    }

    .activity-section {
      margin-bottom: 20px;

      .activity-card,
      .todo-card {
        height: 280px;
        margin-bottom: 16px;
        padding: 16px;
      }
    }

    .system-status-section {
      .status-card {
        height: auto;
        padding: 16px;
        margin-bottom: 12px;
      }
    }

    // 优化栅格间距
    .el-row {
      margin-left: -8px !important;
      margin-right: -8px !important;

      .el-col {
        padding-left: 8px !important;
        padding-right: 8px !important;
      }
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .admin-home {
    padding: 8px;

    .welcome-banner-section {
      .custom-banner {
        border-radius: 12px;

        .banner-content {
          padding: 24px 16px;
          gap: 20px;

          .banner-text-area {
            .banner-title {
              font-size: 1.45rem;
              margin-bottom: 10px;

              .greeting,
              .user-identity {
                line-height: 1.25;
              }
            }

            .banner-subtitle {
              font-size: 0.9rem;
              gap: 4px;
            }
          }

          .banner-actions-area {
            gap: 16px;

            .action-buttons {
              gap: 10px;

              .primary-action-btn,
              .secondary-action-btn {
                width: 160px;
                padding: 10px 16px;
                font-size: 0.9rem;
                border-radius: 10px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                margin: 0 auto;
              }
            }

            .quick-stats {
              gap: 16px;

              .stat-item {
                .stat-number {
                  font-size: 1.25rem;
                }

                .stat-label {
                  font-size: 0.75rem;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
