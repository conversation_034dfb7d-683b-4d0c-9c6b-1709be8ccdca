import request from '@/utils/http'

/**
 * 预约服务类
 */
export class AppointmentService {
  /**
   * 获取客户列表
   * @param params 查询参数
   */
  static getCustomerList(params: any) {
    return request.get({
      url: '/customers',
      params
    })
  }

  /**
   * 获取预约列表
   * @param params 查询参数
   */
  static getAppointmentList(params: any) {
    return request.get({
      url: '/appointments',
      params
    })
  }

  /**
   * 获取预约详情
   * @param id 预约ID
   */
  static getAppointmentDetail(id: string) {
    return request.get({
      url: `/appointments/${id}`
    })
  }

  /**
   * 创建预约
   * @param data 预约数据
   */
  static createAppointment(data: any) {
    return request.post({
      url: '/appointments',
      data,
      showErrorMessage: true
    })
  }

  /**
   * 更新预约
   * @param id 预约ID
   * @param data 更新数据
   */
  static updateAppointment(id: string, data: any) {
    return request.put({
      url: `/appointments/${id}`,
      data,
      showErrorMessage: true
    })
  }

  /**
   * 删除预约
   * @param id 预约ID
   */
  static deleteAppointment(id: string) {
    return request.del({
      url: `/appointments/${id}`,
      showErrorMessage: true
    })
  }

  /**
   * 更新预约备注
   * @param id 预约ID
   * @param remark 备注内容
   */
  static updateAppointmentRemark(id: string, remark: string) {
    return request.put({
      url: `/appointments/${id}/remark`,
      data: { remark },
      showErrorMessage: true
    })
  }

  /**
   * 更新预约状态
   * @param id 预约ID
   * @param data 状态更新数据
   */
  static updateAppointmentStatus(id: string, data: any) {
    return request.put({
      url: `/appointments/${id}/status`,
      data,
      showErrorMessage: true
    })
  }

  /**
   * 获取套餐类型列表
   */
  static getPackageTypes() {
    return request.get({
      url: '/package-types'
    })
  }

  /**
   * 获取随心换选项列表
   */
  static getChangeOptions() {
    return request.get({
      url: '/change-options'
    })
  }

  /**
   * 获取内存卡列表
   */
  static getMemoryCards() {
    return request.get({
      url: '/memory-cards'
    })
  }

  /**
   * 获取预约统计数据
   */
  static getAppointmentStats() {
    return request.get({
      url: '/appointments/stats'
    })
  }

  /**
   * 获取客户统计数据
   */
  static getCustomerStats() {
    return request.get({
      url: '/customers/stats'
    })
  }

  /**
   * 获取今日预约数据
   */
  static getTodayAppointments() {
    return request.get({
      url: '/appointments/today'
    })
  }

  /**
   * 获取预约状态统计
   */
  static getAppointmentStatusStats() {
    return request.get({
      url: '/appointments/status-stats'
    })
  }
}
