var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,i=(e,a)=>{for(var l in a||(a={}))o.call(a,l)&&r(e,l,a[l]);if(t)for(var l of t(a))n.call(a,l)&&r(e,l,a[l]);return e},p=(e,a,l)=>new Promise(((t,o)=>{var n=e=>{try{i(l.next(e))}catch(a){o(a)}},r=e=>{try{i(l.throw(e))}catch(a){o(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,r);i((l=l.apply(e,a)).next())}));import"./index-C5Q-N6Yp.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css                  *//* empty css               *//* empty css               *//* empty css                      *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        *//* empty css                         *//* empty css                *//* empty css                       */import"./el-form-item-l0sNRNKZ.js";import{k as s,aa as d,r as u,c as m,V as c,O as g,C as f,S as _,a2 as b,x as h,D as v,ac as y,$ as V,a0 as j,al as w,am as C,aU as D,B as x,a3 as k,a6 as Y,ab as z,u as U,aH as P,aV as T,Y as A,aw as N,az as R,aA as S,aB as O,aG as M,aJ as q,X as E,aK as B,R as H,aW as F,aX as I,aY as K,F as Z,Z as $,aZ as L,ak as Q,aQ as X,aR as G,ae as J,aD as W,E as ee,a_ as ae}from"./vendor-9ydHGNSq.js";import{A as le}from"./appointmentApi-CAeIur-8.js";import{u as te,a as oe}from"./useForm-CMcsaOzW.js";import"./index-DEP0vMzR.js";/* empty css                       *//* empty css                   */import{_ as ne}from"./_plugin-vue_export-helper-BCo6x5W8.js";const re={class:"appointment-management"},ie={class:"search-bar"},pe={class:"action-bar"},se={class:"price-text"},de={class:"pagination-wrapper"},ue={class:"dialog-footer"};var me;const ce=ne(s((me=i({},{name:"AppointmentManagement"}),a(me,l({__name:"index-optimized",setup(e){const a=d({customerName:"",phone:"",status:"",dateRange:[]}),l=u([{label:"基础套餐",value:"basic"},{label:"标准套餐",value:"standard"},{label:"高级套餐",value:"premium"},{label:"定制套餐",value:"custom"}]),{tableData:t,loading:o,pagination:n,refresh:r,search:s,handlePageChange:ne,handleSizeChange:me,handleSelectionChange:ce}=te({api:e=>le.getAppointmentList(e),deleteApi:e=>le.deleteAppointment(e),immediate:!0}),{formRef:ge,formData:fe,rules:_e,submitting:be,submit:he,setCreateMode:ve}=oe({initialData:{customerName:"",phone:"",serviceType:"",appointmentDate:"",appointmentTime:"",totalPrice:0,notes:"",status:"pending"},rules:{customerName:[{required:!0,message:"请输入客户姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],serviceType:[{required:!0,message:"请选择服务类型",trigger:"change"}],appointmentDate:[{required:!0,message:"请选择预约日期",trigger:"change"}],appointmentTime:[{required:!0,message:"请选择预约时间",trigger:"change"}],totalPrice:[{required:!0,message:"请输入总价",trigger:"blur"}]},submitApi:e=>fe._id?le.updateAppointment(fe._id,e):le.createAppointment(e),onSuccess:()=>{ye.value=!1,r()}}),ye=u(!1),Ve=m((()=>!!fe._id)),je=()=>{ve(),ye.value=!0},we=e=>p(this,null,(function*(){try{yield W.confirm(`确定要删除客户"${e.customerName}"的预约吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield le.deleteAppointment(e._id),ee.success("删除成功"),r()}catch(a){"cancel"!==a&&ee.error("删除失败")}})),Ce=e=>p(this,null,(function*(){try{yield le.updateAppointment(e._id,{status:"confirmed"}),ee.success("预约确认成功"),r()}catch(a){ee.error("确认失败")}})),De=()=>p(this,null,(function*(){yield he()})),xe=()=>{const e=i({},a);e.dateRange&&2===e.dateRange.length&&(e.startDate=e.dateRange[0],e.endDate=e.dateRange[1],delete e.dateRange),s(e)},ke=()=>{Object.assign(a,{customerName:"",phone:"",status:"",dateRange:[]}),s({})},Ye=e=>{"import"===e?ee.info("导入功能开发中..."):"export"===e&&ee.info("导出功能开发中...")},ze=e=>({pending:"待确认",confirmed:"已确认",completed:"已完成",cancelled:"已取消"}[e]||e);return(e,i)=>{const p=V,s=y,d=C,u=w,m=D,W=k,ee=z,le=A,te=S,oe=R,he=O,ve=q,Ue=B,Pe=ae,Te=F,Ae=K,Ne=I,Re=L,Se=Q,Oe=G,Me=X,qe=J,Ee=c("ripple"),Be=M;return f(),g("div",re,[_("div",ie,[h(ee,{model:a,inline:"",class:"search-form"},{default:v((()=>[h(s,{label:"客户姓名"},{default:v((()=>[h(p,{modelValue:a.customerName,"onUpdate:modelValue":i[0]||(i[0]=e=>a.customerName=e),placeholder:"请输入客户姓名",clearable:"",onKeyup:j(xe,["enter"])},null,8,["modelValue"])])),_:1}),h(s,{label:"手机号"},{default:v((()=>[h(p,{modelValue:a.phone,"onUpdate:modelValue":i[1]||(i[1]=e=>a.phone=e),placeholder:"请输入手机号",clearable:"",onKeyup:j(xe,["enter"])},null,8,["modelValue"])])),_:1}),h(s,{label:"预约状态"},{default:v((()=>[h(u,{modelValue:a.status,"onUpdate:modelValue":i[2]||(i[2]=e=>a.status=e),placeholder:"请选择状态",clearable:""},{default:v((()=>[h(d,{label:"待确认",value:"pending"}),h(d,{label:"已确认",value:"confirmed"}),h(d,{label:"已完成",value:"completed"}),h(d,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),h(s,{label:"预约日期"},{default:v((()=>[h(m,{modelValue:a.dateRange,"onUpdate:modelValue":i[3]||(i[3]=e=>a.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),h(s,null,{default:v((()=>[b((f(),x(W,{type:"primary",onClick:xe},{default:v((()=>i[16]||(i[16]=[Y("搜索")]))),_:1,__:[16]})),[[Ee]]),b((f(),x(W,{onClick:ke},{default:v((()=>i[17]||(i[17]=[Y("重置")]))),_:1,__:[17]})),[[Ee]])])),_:1})])),_:1},8,["model"])]),_("div",pe,[b((f(),x(W,{type:"primary",icon:U(P),onClick:je},{default:v((()=>i[18]||(i[18]=[Y(" 新增预约 ")]))),_:1,__:[18]},8,["icon"])),[[Ee]]),b((f(),x(W,{onClick:U(r),loading:U(o)},{default:v((()=>i[19]||(i[19]=[Y(" 刷新 ")]))),_:1,__:[19]},8,["onClick","loading"])),[[Ee]]),h(he,{onCommand:Ye,trigger:"click"},{dropdown:v((()=>[h(oe,null,{default:v((()=>[h(te,{command:"import"},{default:v((()=>i[21]||(i[21]=[Y("导入Excel")]))),_:1,__:[21]}),h(te,{command:"export"},{default:v((()=>i[22]||(i[22]=[Y("导出Excel")]))),_:1,__:[22]})])),_:1})])),default:v((()=>[b((f(),x(W,{icon:U(T)},{default:v((()=>[i[20]||(i[20]=Y(" Excel操作 ")),h(le,{class:"el-icon--right"},{default:v((()=>[h(U(N))])),_:1})])),_:1,__:[20]},8,["icon"])),[[Ee]])])),_:1})]),b((f(),x(Pe,{data:U(t),border:"",stripe:"",onSelectionChange:U(ce)},{default:v((()=>[h(ve,{type:"selection",width:"55"}),h(ve,{type:"index",label:"序号",width:"60",align:"center"}),h(ve,{prop:"customerName",label:"客户姓名","min-width":"100"}),h(ve,{prop:"phone",label:"手机号",width:"120"}),h(ve,{prop:"serviceType",label:"服务类型",width:"120"}),h(ve,{prop:"appointmentDate",label:"预约日期",width:"120",align:"center"},{default:v((({row:e})=>{return[Y(E((a=e.appointmentDate,a?new Date(a).toLocaleDateString("zh-CN"):"-")),1)];var a})),_:1}),h(ve,{prop:"appointmentTime",label:"预约时间",width:"100",align:"center"}),h(ve,{prop:"status",label:"状态",width:"100",align:"center"},{default:v((({row:e})=>{return[h(Ue,{type:(a=e.status,{pending:"warning",confirmed:"primary",completed:"success",cancelled:"danger"}[a]||"info")},{default:v((()=>[Y(E(ze(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),h(ve,{prop:"totalPrice",label:"总价",width:"100",align:"center"},{default:v((({row:e})=>[_("span",se,"¥"+E(e.totalPrice),1)])),_:1}),h(ve,{prop:"notes",label:"备注","min-width":"150","show-overflow-tooltip":""}),h(ve,{label:"操作",width:"200",align:"center",fixed:"right"},{default:v((({row:e})=>[b((f(),x(W,{type:"primary",size:"small",onClick:a=>(e=>{Object.assign(fe,e),ye.value=!0})(e)},{default:v((()=>i[23]||(i[23]=[Y(" 编辑 ")]))),_:2,__:[23]},1032,["onClick"])),[[Ee]]),"pending"===e.status?b((f(),x(W,{key:0,type:"success",size:"small",onClick:a=>Ce(e)},{default:v((()=>i[24]||(i[24]=[Y(" 确认 ")]))),_:2,__:[24]},1032,["onClick"])),[[Ee]]):H("",!0),b((f(),x(W,{type:"danger",size:"small",onClick:a=>we(e)},{default:v((()=>i[25]||(i[25]=[Y(" 删除 ")]))),_:2,__:[25]},1032,["onClick"])),[[Ee]])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[Be,U(o)]]),_("div",de,[h(Te,{"current-page":U(n).page,"onUpdate:currentPage":i[4]||(i[4]=e=>U(n).page=e),"page-size":U(n).pageSize,"onUpdate:pageSize":i[5]||(i[5]=e=>U(n).pageSize=e),"page-sizes":[10,20,50,100],total:U(n).total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:U(me),onCurrentChange:U(ne)},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),h(qe,{modelValue:ye.value,"onUpdate:modelValue":i[15]||(i[15]=e=>ye.value=e),title:Ve.value?"编辑预约":"新增预约",width:"600px","close-on-click-modal":!1},{footer:v((()=>[_("div",ue,[h(W,{onClick:i[14]||(i[14]=e=>ye.value=!1)},{default:v((()=>i[30]||(i[30]=[Y("取消")]))),_:1,__:[30]}),b((f(),x(W,{type:"primary",onClick:De,loading:U(be)},{default:v((()=>i[31]||(i[31]=[Y(" 确定 ")]))),_:1,__:[31]},8,["loading"])),[[Ee]])])])),default:v((()=>[h(ee,{ref_key:"formRef",ref:ge,model:U(fe),rules:U(_e),"label-width":"100px",class:"appointment-form"},{default:v((()=>[h(Ne,{gutter:20},{default:v((()=>[h(Ae,{span:12},{default:v((()=>[h(s,{label:"客户姓名",prop:"customerName"},{default:v((()=>[h(p,{modelValue:U(fe).customerName,"onUpdate:modelValue":i[6]||(i[6]=e=>U(fe).customerName=e),placeholder:"请输入客户姓名"},null,8,["modelValue"])])),_:1})])),_:1}),h(Ae,{span:12},{default:v((()=>[h(s,{label:"手机号",prop:"phone"},{default:v((()=>[h(p,{modelValue:U(fe).phone,"onUpdate:modelValue":i[7]||(i[7]=e=>U(fe).phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),h(Ne,{gutter:20},{default:v((()=>[h(Ae,{span:12},{default:v((()=>[h(s,{label:"服务类型",prop:"serviceType"},{default:v((()=>[h(u,{modelValue:U(fe).serviceType,"onUpdate:modelValue":i[8]||(i[8]=e=>U(fe).serviceType=e),placeholder:"请选择服务类型"},{default:v((()=>[(f(!0),g(Z,null,$(l.value,(e=>(f(),x(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(Ae,{span:12},{default:v((()=>[h(s,{label:"预约日期",prop:"appointmentDate"},{default:v((()=>[h(m,{modelValue:U(fe).appointmentDate,"onUpdate:modelValue":i[9]||(i[9]=e=>U(fe).appointmentDate=e),type:"date",placeholder:"选择预约日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),h(Ne,{gutter:20},{default:v((()=>[h(Ae,{span:12},{default:v((()=>[h(s,{label:"预约时间",prop:"appointmentTime"},{default:v((()=>[h(Re,{modelValue:U(fe).appointmentTime,"onUpdate:modelValue":i[10]||(i[10]=e=>U(fe).appointmentTime=e),placeholder:"选择预约时间",format:"HH:mm","value-format":"HH:mm",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),h(Ae,{span:12},{default:v((()=>[h(s,{label:"总价",prop:"totalPrice"},{default:v((()=>[h(Se,{modelValue:U(fe).totalPrice,"onUpdate:modelValue":i[11]||(i[11]=e=>U(fe).totalPrice=e),min:0,precision:2,placeholder:"请输入总价",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),h(s,{label:"备注",prop:"notes"},{default:v((()=>[h(p,{modelValue:U(fe).notes,"onUpdate:modelValue":i[12]||(i[12]=e=>U(fe).notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),h(s,{label:"状态",prop:"status"},{default:v((()=>[h(Me,{modelValue:U(fe).status,"onUpdate:modelValue":i[13]||(i[13]=e=>U(fe).status=e)},{default:v((()=>[h(Oe,{label:"pending"},{default:v((()=>i[26]||(i[26]=[Y("待确认")]))),_:1,__:[26]}),h(Oe,{label:"confirmed"},{default:v((()=>i[27]||(i[27]=[Y("已确认")]))),_:1,__:[27]}),h(Oe,{label:"completed"},{default:v((()=>i[28]||(i[28]=[Y("已完成")]))),_:1,__:[28]}),h(Oe,{label:"cancelled"},{default:v((()=>i[29]||(i[29]=[Y("已取消")]))),_:1,__:[29]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}})))),[["__scopeId","data-v-dcaffecb"]]);export{ce as default};
