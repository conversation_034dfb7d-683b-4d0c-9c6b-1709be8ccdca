import request from '@/utils/http'

export interface UserListParams {
  page?: number
  limit?: number
  keyword?: string
}

export interface UserItem {
  userId: string
  username: string
  nickname?: string
  avatar?: string
  roles: string[]
  perms?: string[]
  createdAt: string
  updatedAt: string
  status?: 'active' | 'disabled'
}

export interface UserListResponse {
  total: number
  items: UserItem[]
  page: number
  limit: number
}

export interface CreateUserData {
  username: string
  password: string
  nickname?: string
  avatar?: string
  roles: string[]
  perms?: string[]
}

export interface UpdateUserData {
  userId: string
  username?: string
  nickname?: string
  avatar?: string
  roles?: string[]
  perms?: string[]
}

export interface UpdateUserResponse {
  message: string
  user: UserItem
  needRelogin: boolean
}

export interface DeleteUserResponse {
  message: string
  deletedUser: {
    userId: string
    username: string
  }
}

export interface ApiErrorResponse {
  status: number
  message: string
  error?: string
}

export interface UpdateUserStatusData {
  userId: string
  status: 'active' | 'disabled'
}

export interface UpdateUserStatusResponse {
  message: string
  user: UserItem
}

// 安全码生成请求数据
export interface GenerateSecurityCodeData {
  adminPassword: string
}

// 安全码生成响应
export interface SecurityCodeResponse {
  message: string
  securityCode: string
  targetUser: {
    userId: string
    username: string
    nickname: string
  }
  validUntil: string
  warning: string
  securityNotice?: string
}

// 密码重置请求数据（双重安全验证）
export interface ResetPasswordData {
  newPassword: string
  confirmPassword: string
  securityCode: string
  adminPassword: string
}

// 密码重置响应
export interface ResetPasswordResponse {
  message: string
  targetUser: {
    userId: string
    username: string
    nickname: string
  }
  timestamp: string
}

export class UserManageService {
  // 获取用户列表
  static getUserList(params: UserListParams = { page: 1, limit: 10 }) {
    return request.get<UserListResponse>({
      url: '/users',
      params
    })
  }

  // 获取单个用户详情
  static getUserDetail(userId: string) {
    return request.get<UserItem>({
      url: `/users/${userId}`
    })
  }

  // 创建用户
  static createUser(data: CreateUserData) {
    return request.post<UserItem>({
      url: '/users',
      data
    })
  }

  // 更新用户信息
  static updateUser(data: UpdateUserData) {
    return request.put<UpdateUserResponse>({
      url: `/users/${data.userId}`,
      data
    })
  }

  // 删除用户
  static deleteUser(userId: string) {
    return request.del<DeleteUserResponse>({
      url: `/users/${userId}`
    })
  }

  // 更新用户状态（启用/禁用）
  static updateUserStatus(data: UpdateUserStatusData) {
    return request.put<UpdateUserStatusResponse>({
      url: `/users/${data.userId}/status`,
      data: { status: data.status }
    })
  }

  // 生成密码重置安全码（需要管理员密码验证）
  static generateResetSecurityCode(userId: string, data: GenerateSecurityCodeData) {
    return request.post<SecurityCodeResponse>({
      url: `/users/${userId}/reset-security-code`,
      data,
      showErrorMessage: false
    })
  }

  // 超级管理员重置用户密码（双重安全验证）
  static resetUserPassword(userId: string, data: ResetPasswordData) {
    return request.post<ResetPasswordResponse>({
      url: `/users/${userId}/reset-password`,
      data,
      showErrorMessage: false
    })
  }
}
