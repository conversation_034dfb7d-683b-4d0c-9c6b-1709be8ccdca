var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,r=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,i=(e,l,a)=>new Promise(((t,s)=>{var o=e=>{try{i(a.next(e))}catch(l){s(l)}},r=e=>{try{i(a.throw(e))}catch(l){s(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,r);i((a=a.apply(e,l)).next())}));import{d as u,k as n,x as d,B as c,z as p}from"./index-C5Q-N6Yp.js";/* empty css                  *//* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                  *//* empty css                    */import{_ as m}from"./ArtWangEditor.vue_vue_type_style_index_0_lang-BphJvq20.js";/* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css               *//* empty css                 */import{k as v,J as f,r as h,d as j,O as y,C as _,S as b,x as g,D as w,aY as x,$ as V,i as E,u as O,al as k,F as P,Z as A,B as Y,am as D,aX as q,ac as B,bo as M,Y as U,aH as $,aj as z,ab as C,a6 as T,X as I,a3 as S,E as W}from"./vendor-9ydHGNSq.js";import{E as X}from"./emojo-Ben6gd8J.js";import{P as F}from"./formEnum-BLgiZVxV.js";import{d as G}from"./index-DiJiIjk5.js";import{_ as H}from"./_plugin-vue_export-helper-BCo6x5W8.js";const J={class:"article-edit"},N={class:"editor-wrap"},Z={class:"form-wrap"},K={class:"el-top upload-container"},L={key:0,class:"upload-placeholder"},Q=["src"],R={style:{display:"flex","justify-content":"flex-end"}},ee=v((le=((e,l)=>{for(var a in l||(l={}))s.call(l,a)&&r(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&r(e,a,l[a]);return e})({},{name:"ArticlePublish"}),l(le,a({__name:"index",setup(e){const l=f(),a=u();let{accessToken:t}=a;const s={Authorization:t};let o=F.Add;const r=h(""),v=h(),H=h(),ee=h(""),le=h(""),ae=h(""),te=h(!0);j((()=>{n().scrollToTop(),ie(),se()}));const se=()=>{const{id:e}=l.query;o=e?F.Edit:F.Add,o===F.Edit&&e?oe():re()},oe=()=>{ue()},re=()=>{le.value=de(G().value)},ie=()=>i(this,null,(function*(){try{const e=yield d.get("https://www.qiniu.lingchen.kim/classify.json");200===e.data.code&&(H.value=e.data.data)}catch(e){}})),ue=()=>i(this,null,(function*(){const e=yield d.get("https://www.qiniu.lingchen.kim/blog_list.json");if(e.data.code===c.success){let{title:l,blog_class:a,html_content:t}=e.data.data;r.value=l,v.value=Number(a),ee.value=t}})),ne=()=>{o===F.Edit?me():pe()},de=e=>p(e,"YYYY-MM-DD").value,ce=()=>r.value?v.value?"<p><br></p>"===ee.value?(W.error("请输入文章内容"),!1):!!ae.value||(W.error("请上传图片"),!1):(W.error("请选择文章类型"),!1):(W.error("请输入文章标题"),!1),pe=()=>i(this,null,(function*(){try{if(!ce())return;ee.value=ve(ee.value)}catch(e){}})),me=()=>i(this,null,(function*(){try{if(!ce())return;ee.value=ve(ee.value)}catch(e){}})),ve=e=>e.replace(/(\s*)<\/code>/g,"</code>"),fe=e=>{ae.value=e.data.url,W.success(`图片上传成功 ${X[200]}`)},he=()=>{W.error(`图片上传失败 ${X[500]}`)},je=e=>{const l=e.type.startsWith("image/"),a=e.size/1024/1024<2;return l?!!a||(W.error("图片大小不能超过 2MB!"),!1):(W.error("只能上传图片文件!"),!1)};return(e,l)=>{const a=V,t=x,i=D,u=k,n=q,d=m,c=U,p=M,f=B,h=z,j=C,W=S;return _(),y("div",J,[b("div",null,[b("div",N,[g(n,{gutter:10},{default:w((()=>[g(t,{span:18},{default:w((()=>[g(a,{modelValue:O(r),"onUpdate:modelValue":l[0]||(l[0]=e=>E(r)?r.value=e:null),modelModifiers:{trim:!0},placeholder:"请输入文章标题（最多100个字符）",maxlength:"100"},null,8,["modelValue"])])),_:1}),g(t,{span:6},{default:w((()=>[g(u,{modelValue:O(v),"onUpdate:modelValue":l[1]||(l[1]=e=>E(v)?v.value=e:null),placeholder:"请选择文章类型",filterable:""},{default:w((()=>[(_(!0),y(P,null,A(O(H),(e=>(_(),Y(i,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),g(d,{class:"el-top",modelValue:O(ee),"onUpdate:modelValue":l[2]||(l[2]=e=>E(ee)?ee.value=e:null)},null,8,["modelValue"]),b("div",Z,[l[6]||(l[6]=b("h2",null,"发布设置",-1)),g(j,null,{default:w((()=>[g(f,{label:"封面"},{default:w((()=>[b("div",K,[g(p,{class:"cover-uploader",action:"http://www.ddbbcnm.icu:3034/api/common/upload",headers:s,"show-file-list":!1,"on-success":fe,"on-error":he,"before-upload":je},{default:w((()=>[O(ae)?(_(),y("img",{key:1,src:O(ae),class:"cover-image"},null,8,Q)):(_(),y("div",L,[g(c,{class:"upload-icon"},{default:w((()=>[g(O($))])),_:1}),l[4]||(l[4]=b("div",{class:"upload-text"},"点击上传封面",-1))]))])),_:1}),l[5]||(l[5]=b("div",{class:"el-upload__tip"},"建议尺寸 16:9，jpg/png 格式",-1))])])),_:1}),g(f,{label:"可见"},{default:w((()=>[g(h,{modelValue:O(te),"onUpdate:modelValue":l[3]||(l[3]=e=>E(te)?te.value=e:null)},null,8,["modelValue"])])),_:1})])),_:1}),b("div",R,[g(W,{type:"primary",onClick:ne,style:{width:"100px"}},{default:w((()=>[T(I(O(o)===O(F).Edit?"保存":"发布"),1)])),_:1})])])])])])}}}))));var le;const ae=H(ee,[["__scopeId","data-v-844204ed"]]);export{ae as default};
