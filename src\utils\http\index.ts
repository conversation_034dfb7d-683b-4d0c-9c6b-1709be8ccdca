import axios, { InternalAxiosRequestConfig, AxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStore } from '@/store/modules/user'
import { ApiStatus } from './status'
import { HttpError, handleError, showError } from './error'
import { $t } from '@/locales'
import { throttleRequest } from '@/utils/throttle'

// 常量定义
const REQUEST_TIMEOUT = 15000 // 请求超时时间(毫秒)
const LOGOUT_DELAY = 1000 // 退出登录延迟时间(毫秒)
const MAX_RETRIES = 2 // 最大重试次数
const RETRY_DELAY = 1000 // 重试延迟时间(毫秒)

// 扩展 AxiosRequestConfig 类型
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  showErrorMessage?: boolean
  skipNetworkCheck?: boolean // 是否跳过网络检查
  noRetry?: boolean // 是否禁止重试
  noThrottle?: boolean // 是否禁用请求节流
  throttleOptions?: {
    interval?: number // 间隔时间(毫秒)
    maxRequests?: number // 间隔内最大请求次数
    message?: string // 自定义提示消息
  }
}

// API基础URL - 从环境变量获取
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3033/api/v1'

const axiosInstance = axios.create({
  timeout: REQUEST_TIMEOUT, // 请求超时时间(毫秒)
  baseURL: API_BASE_URL, // API地址
  withCredentials: false, // 关闭携带凭证，解决CORS问题
  transformRequest: [(data) => JSON.stringify(data)], // 请求数据转换为 JSON 字符串
  validateStatus: (status) => status >= 200 && status < 300, // 只接受 2xx 的状态码
  headers: {
    get: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
    post: { 'Content-Type': 'application/json;charset=utf-8' }
  },
  transformResponse: [
    (data, headers) => {
      const contentType = headers['content-type']
      if (contentType && contentType.includes('application/json')) {
        try {
          return JSON.parse(data)
        } catch {
          return data
        }
      }
      return data
    }
  ]
})

// 检查网络状态
const checkNetworkStatus = (): boolean => {
  return navigator.onLine
}

// 创建网络监听事件
let isNetworkOffline = false
window.addEventListener('online', () => {
  isNetworkOffline = false
  // 网络已连接
})
window.addEventListener('offline', () => {
  isNetworkOffline = true
  // 网络已断开
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (request: InternalAxiosRequestConfig) => {
    // 检查网络状态
    const extendedConfig = request.headers as any
    const skipNetworkCheck = extendedConfig?.skipNetworkCheck === true

    if (!skipNetworkCheck && (isNetworkOffline || !checkNetworkStatus())) {
      return Promise.reject(new HttpError($t('httpMsg.networkOffline'), ApiStatus.networkError))
    }

    const { accessToken } = useUserStore()

    // 设置 token 和 请求头
    if (accessToken) {
      request.headers.set('Authorization', `Bearer ${accessToken}`)
      request.headers.set('Content-Type', 'application/json')
    }

    return request
  },
  (error) => {
    showError(new HttpError($t('httpMsg.requestConfigError'), ApiStatus.error))
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // 直接返回数据，不需要检查code
    return response
  },
  (error) => {
    // 检查是否是网络错误
    if (error.message && (error.message.includes('Network Error') || !navigator.onLine)) {
      return Promise.reject(new HttpError($t('httpMsg.networkOffline'), ApiStatus.networkError))
    }

    // 处理请求超时
    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
      return Promise.reject(new HttpError($t('httpMsg.requestTimeout'), ApiStatus.timeout))
    }

    // 处理401未授权错误或token过期情况
    if (
      // 状态码为401
      (error.response && error.response.status === 401) ||
      // 或响应中包含token过期信息
      (error.response &&
        error.response.data &&
        (error.response.data.message === 'Token expired' ||
          error.response.data.message === '令牌已过期' ||
          error.response.data.message === 'Invalid token' ||
          error.response.data.message === '无效的令牌' ||
          error.response.data.code === 'token_expired' ||
          error.response.data.code === 'token_invalid' ||
          error.response.data.code === 'token_not_provided'))
    ) {
      // 检查是否是登录请求
      const isLoginRequest =
        error.config && error.config.url && error.config.url.includes('/auth/login')

      // 如果是登录请求，使用后端返回的错误信息
      if (isLoginRequest) {
        const errorData = error.response.data
        const errorMessage = errorData?.message || errorData?.msg || $t('httpMsg.unauthorized')
        return Promise.reject(
          new HttpError(errorMessage, ApiStatus.unauthorized, { data: errorData })
        )
      }

      // 其他401错误或token过期，执行登出操作
      logOut($t('httpMsg.sessionExpired'))
      return Promise.reject(new HttpError($t('httpMsg.unauthorized'), ApiStatus.unauthorized))
    }
    return Promise.reject(handleError(error))
  }
)

// 判断是否应该重试
function shouldRetry(code: number): boolean {
  // 网络错误、超时和服务器错误(5xx)可以重试
  return (
    code === ApiStatus.networkError || code === ApiStatus.timeout || (code >= 500 && code < 600)
  )
}

// 请求重试函数
async function retryRequest<T>(
  config: ExtendedAxiosRequestConfig,
  retries: number = MAX_RETRIES
): Promise<T> {
  try {
    return await request<T>(config)
  } catch (error) {
    // 如果配置中禁止重试，则不进行重试
    if (config.noRetry) {
      throw error
    }

    if (retries > 0 && error instanceof HttpError && shouldRetry(error.code)) {
      // 请求失败，进行重试
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY))
      // 重试前再次检查网络状态
      if (!checkNetworkStatus()) {
        throw new HttpError($t('httpMsg.networkOffline'), ApiStatus.networkError)
      }
      return retryRequest<T>(config, retries - 1)
    }
    throw error
  }
}

// 请求函数
async function request<T = any>(config: ExtendedAxiosRequestConfig): Promise<T> {
  // 检查网络状态
  if (!config.skipNetworkCheck && !checkNetworkStatus()) {
    const error = new HttpError($t('httpMsg.networkOffline'), ApiStatus.networkError)
    if (config.showErrorMessage !== false) {
      showError(error)
    }
    return Promise.reject(error)
  }

  // 对 POST | PUT 请求特殊处理
  if (config.method?.toUpperCase() === 'POST' || config.method?.toUpperCase() === 'PUT') {
    // 如果传入了data，优先使用data
    // 如果没有data但有params，则将params作为data发送
    if (!config.data && config.params) {
      config.data = config.params
      config.params = undefined
    }
  }

  // 请求节流控制，仅当未禁用节流时检查
  if (!config.noThrottle) {
    const fullUrl = `${config.baseURL || API_BASE_URL}${config.url}`
    if (!throttleRequest(fullUrl, config.throttleOptions)) {
      // 如果请求被节流，返回一个错误
      return Promise.reject(new HttpError('请求频繁', ApiStatus.tooManyRequests))
    }
  }

  try {
    const res = await axiosInstance.request(config)
    return res.data
  } catch (error) {
    // 检查是否是节流错误
    if (error instanceof HttpError && error.code === ApiStatus.tooManyRequests) {
      // 节流错误不显示额外消息，因为throttle模块已经显示了
      return Promise.reject(error)
    }
    
    if (error instanceof HttpError) {
      // 根据配置决定是否显示错误消息
      const showErrorMessage = config.showErrorMessage !== false
      showError(error, showErrorMessage)
    }
    return Promise.reject(error)
  }
}

// API 方法集合
const api = {
  get<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'GET' })
  },
  post<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'POST' })
  },
  put<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'PUT' })
  },
  del<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'DELETE' })
  },
  request<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config })
  }
}

// 退出登录函数
const logOut = (reason?: string): void => {
  // 显示提示信息
  if (reason) {
    showError(new HttpError(reason, ApiStatus.unauthorized), true)
  }

  setTimeout(() => {
    useUserStore().logOut()
  }, LOGOUT_DELAY)
}

export default api
