import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-tovWoMSq.js";import{_ as s}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as d,C as t,S as i,x as e,bp as v}from"./vendor-9ydHGNSq.js";import"./index-C5Q-N6Yp.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";const c={class:"card art-custom-card yearly-card"};const o=s({},[["render",function(s,o){const n=a;return t(),d("div",c,[o[0]||(o[0]=i("div",{class:"card-header"},[i("p",{class:"title"},"年度销售额"),i("p",{class:"subtitle"},"按季度统计")],-1)),e(n,{showAxisLabel:!1,showAxisLine:!1,showSplitLine:!1,data:[50,80,50,90,60,70,50],barWidth:"26px",height:"calc(100% - 155px)"}),o[1]||(o[1]=v('<div class="icon-text-widget" data-v-d58b0a52><div class="item" data-v-d58b0a52><div class="icon" data-v-d58b0a52><i class="iconfont-sys" data-v-d58b0a52></i></div><div class="content" data-v-d58b0a52><p data-v-d58b0a52>¥200,858</p><span data-v-d58b0a52>线上销售</span></div></div><div class="item" data-v-d58b0a52><div class="icon" data-v-d58b0a52><i class="iconfont-sys" data-v-d58b0a52></i></div><div class="content" data-v-d58b0a52><p data-v-d58b0a52>¥102,927</p><span data-v-d58b0a52>线下销售</span></div></div></div>',1))])}],["__scopeId","data-v-d58b0a52"]]);export{o as default};
