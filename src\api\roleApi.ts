import request from '@/utils/http'

export interface RoleListParams {
  page?: number
  limit?: number
  keyword?: string
}

export interface Role {
  _id: string
  name: string
  description: string
  permissions: string[]
  isSystem: boolean
  createdAt: string
  updatedAt: string
}

export interface RoleListResponse {
  total: number
  items: Role[]
  page: number
  limit: number
}

export class RoleService {
  // 获取角色列表
  static getRoles(params: RoleListParams = { page: 1, limit: 10 }) {
    return request.get<RoleListResponse>({
      url: '/users/roles',
      params
    })
  }

  // 创建角色
  static createRole(data: { name: string; description: string; permissions: string[] }) {
    return request.post<Role>({
      url: '/users/roles',
      data,
      showErrorMessage: true
    })
  }

  // 更新角色
  static updateRole(
    id: string,
    data: { name?: string; description?: string; permissions?: string[] }
  ) {
    return request.put<Role>({
      url: `/users/roles/${id}`,
      data,
      showErrorMessage: true
    })
  }

  // 删除角色
  static deleteRole(id: string) {
    return request.del<void>({
      url: `/users/roles/${id}`,
      showErrorMessage: true
    })
  }

  // 获取角色详情
  static getRole(id: string) {
    return request.get<Role>({
      url: `/users/roles/${id}`
    })
  }
}
