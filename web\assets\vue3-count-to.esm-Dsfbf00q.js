import{_ as t}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{O as e,C as i,X as a}from"./vendor-9ydHGNSq.js";let r=0;const s="webkit moz ms o".split(" ");let l,n;if("undefined"==typeof window)l=function(){},n=function(){};else{let t;l=window.requestAnimationFrame,n=window.cancelAnimationFrame;for(let e=0;e<s.length&&(!l||!n);e++)t=s[e],l=l||window[t+"RequestAnimationFrame"],n=n||window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"];l&&n||(l=function(t){const e=(new Date).getTime(),i=Math.max(0,16-(e-r)),a=window.setTimeout((()=>{t(e+i)}),i);return r=e+i,a},n=function(t){window.clearTimeout(t)})}const o=t({props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator:t=>t>=0},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0},easingFn:{type:Function,default:(t,e,i,a)=>i*(1-Math.pow(2,-10*t/a))*1024/1023+e}},data(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown(){return this.startVal>this.endVal}},watch:{startVal(){this.autoplay&&this.start()},endVal(){this.autoplay&&this.start()}},mounted(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{start(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=l(this.count)},pauseResume(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause(){n(this.rAF)},resume(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,l(this.count)},reset(){this.startTime=null,n(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count(t){this.startTime||(this.startTime=t),this.timestamp=t;const e=t-this.startTime;this.remaining=this.localDuration-e,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(e,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(e,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(e/this.localDuration):this.printVal=this.localStartVal+(this.endVal-this.localStartVal)*(e/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),e<this.localDuration?this.rAF=l(this.count):this.$emit("callback")},isNumber:t=>!isNaN(parseFloat(t)),formatNumber(t){t=t.toFixed(this.decimals);const e=(t+="").split(".");let i=e[0];const a=e.length>1?this.decimal+e[1]:"",r=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))for(;r.test(i);)i=i.replace(r,"$1"+this.separator+"$2");return this.prefix+i+a+this.suffix}},destroyed(){n(this.rAF)}},[["render",function(t,r,s,l,n,o){return i(),e("span",null,a(n.displayValue),1)}]]);function u(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function h(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}o.unmounted=o.destroyed,Reflect.deleteProperty(o,"destroyed");var c=function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?h(Object(i),!0).forEach((function(e){u(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):h(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({name:"CountTo",emits:["callback","mountedCallback"]},o);export{c as C};
