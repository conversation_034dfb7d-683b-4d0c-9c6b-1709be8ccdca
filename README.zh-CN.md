简体中文 | [English](./README.md)

# 点点后台管理

## 项目介绍

点点后台管理是一款专注于用户体验和快速开发的现代化管理系统框架。采用Vue3、TypeScript、Vite和Element-Plus等最新技术栈，提供美观精致的UI设计和全面的功能组件，帮助开发者快速构建高质量的后台管理系统。

## 系统预览

### 浅色主题

![浅色主题](https://www.qiniu.lingchen.kim/art_design_pro_readme_cover1.png) ![浅色主题](https://www.qiniu.lingchen.kim/art_design_pro_readme_cover2.png)

### 暗黑主题

![暗黑主题](https://www.qiniu.lingchen.kim/art_design_pro_readme_cover3.png) ![暗黑主题](https://www.qiniu.lingchen.kim/art_design_pro_readme_cover4.png)

## 核心特性

- **最新技术栈**: Vue3、TypeScript、Vite、Element-Plus
- **优雅的UI设计**: 精心优化的视觉效果，注重细节处理
- **丰富的主题**: 支持多种主题模式和自定义主题配置
- **完善的权限系统**: 提供路由级别鉴权、菜单权限和指令权限
- **多种组件模板**: 内置丰富的业务组件和常用模板
- **完全可定制**: 系统全面支持自定义设置，满足个性化需求

## 功能亮点

- 🌈 **主题切换**: 支持明暗主题和自定义主题色
- 🔍 **全局搜索**: 快速查找功能和内容
- 🔒 **安全锁屏**: 提供系统锁屏保护
- 📑 **多标签页**: 支持多页面同时操作
- 🧭 **全局面包屑**: 清晰的页面导航
- 🌍 **国际化**: 支持多语言切换
- 📊 **数据可视化**: 集成Echarts图表组件
- 📝 **富文本编辑**: 提供完善的文本编辑功能
- 📱 **响应式设计**: 完美适配移动端
- 🛡️ **数据持久化**: 优秀的本地存储方案

## 技术栈

- **前端框架**: Vue3、TypeScript、Vite
- **UI组件**: Element-Plus
- **状态管理**: Pinia
- **代码规范**: ESLint、Prettier、Stylelint
- **提交规范**: Husky、Lint-staged、cz-git

## 快速开始

```bash
# 安装依赖
pnpm install

# 如果安装失败，可尝试以下命令
pnpm install --ignore-scripts

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

## 最近更新

- ✨ 优化菜单权限管理，支持父子菜单权限关联
- 🐞 修复部分UI组件在暗黑模式下的显示问题
- 🚀 提升系统整体性能和响应速度
- 📦 更新依赖包至最新版本

## 浏览器兼容性

支持所有现代浏览器，包括Chrome、Firefox、Safari等。

## 许可证

[MIT](LICENSE) © 2023-present
