import{_ as e}from"./index-lryViu9D.js";import{k as t,O as s,C as o,x as r}from"./vendor-9ydHGNSq.js";import{_ as n}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-DiJiIjk5.js";import"./index-C5Q-N6Yp.js";const i={class:"page-content"},p=n(t({__name:"index",setup(t){const n=()=>{};return(t,p)=>{const a=e;return o(),s("div",i,[r(a,{text:"Art Design Pro 是一款专注于用户体验和视觉设计的后台管理系统模版 <a target='_blank' href='https://www.lingchen.kim/art-design-pro/docs/'>点击我 </a>访问官方文档",showClose:""}),r(a,{type:"success",text:"这是一条成功类型的滚动公告",showClose:""}),r(a,{type:"warning",text:"这是一条警告类型的滚动公告",showClose:""}),r(a,{type:"danger",text:"这是一条危险类型的滚动公告",showClose:""}),r(a,{type:"info",text:"这是一条信息类型的滚动公告",showClose:""}),r(a,{text:"这是一条可关闭的滚动公告",showClose:"",onClose:n}),r(a,{text:"这是一条打字机效果的滚动公告，打字机速度为 200 毫秒",typewriter:"","typewriter-speed":200}),r(a,{text:"这是一条速度较慢、向右滚动的公告",speed:30,direction:"right"})])}}}),[["__scopeId","data-v-c0dfeb6f"]]);export{p as default};
