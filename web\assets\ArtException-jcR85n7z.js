import{H as a}from"./index-C5Q-N6Yp.js";/* empty css                  */import{k as s,O as t,S as r,a2 as e,X as o,B as p,D as n,a3 as i,C as d,a6 as l,M as c,V as m}from"./vendor-9ydHGNSq.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";const _={class:"page-content state-page"},g={class:"tips"},v=["src"],f={class:"right-wrap"},x=u(s({__name:"ArtException",props:{data:{}},setup(s){const u=c(),x=()=>{u.push(a)};return(a,s)=>{const c=i,u=m("ripple");return d(),t("div",_,[r("div",g,[r("img",{src:a.data.imgUrl},null,8,v),r("div",f,[r("p",null,o(a.data.desc),1),e((d(),p(c,{color:"#47A7FF",onClick:x},{default:n((()=>[l(o(a.data.btnText),1)])),_:1})),[[u]])])])])}}}),[["__scopeId","data-v-d1f78d23"]]);export{x as _};
