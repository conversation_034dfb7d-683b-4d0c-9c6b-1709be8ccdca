var t=Object.defineProperty,e=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,i=(e,r,o)=>r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o;import"./index-C5Q-N6Yp.js";/* empty css               *//* empty css               */import l from"./Banner-CQAL1kg5.js";import p from"./TotalOrderVolume-C-_rdOmZ.js";import a from"./TotalProducts-Dbhdtxmm.js";import n from"./SalesTrend-CTU4uJHM.js";import u from"./SalesClassification-B-7N3wQb.js";import{_ as d}from"./TransactionList.vue_vue_type_script_setup_true_lang-DJzsRyIl.js";import _ from"./HotCommodity-C4kt5qMS.js";import{_ as j}from"./RecentTransaction.vue_vue_type_script_setup_true_lang-DYcyYdhq.js";import f from"./AnnualSales-DPtzkQpe.js";import c from"./ProductSales-CLlWYK6C.js";import g from"./SalesGrowth-CfgQPiWA.js";import v from"./CartConversionRate-DKhKhkcI.js";import b from"./HotProductsList-fUY7l06h.js";import{k as x,O as y,C as O,x as P,D as w,aY as T,aX as C}from"./vendor-9ydHGNSq.js";import{_ as S}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-CWUKzE_Q.js";import"./index-pwV1nyK9.js";/* empty css                   */import"./index-BqI1XISX.js";import"./useChart-CoPw7zBp.js";import"./index-DEP0vMzR.js";import"./index.vue_vue_type_script_setup_true_lang-tovWoMSq.js";import"./index.vue_vue_type_script_setup_true_lang-C-OqvfhG.js";import"./index-Dgm4PPPz.js";/* empty css                  *//* empty css                     */import"./index-Degvv3hI.js";import"./index-ArFw1yvF.js";import"./index-CoyPwaSJ.js";import"./ArtTable-CBEgSUvC.js";/* empty css                      *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./formEnum-BLgiZVxV.js";import"./index-DiJiIjk5.js";/* empty css                    */import"./icon4-mCHIfvQe.js";const h={class:"ecommerce"},E=x((I=((t,e)=>{for(var r in e||(e={}))s.call(e,r)&&i(t,r,e[r]);if(o)for(var r of o(e))m.call(e,r)&&i(t,r,e[r]);return t})({},{name:"Ecommerce"}),e(I,r({__name:"index",setup:t=>(t,e)=>{const r=T,o=C;return O(),y("div",h,[P(o,{gutter:20},{default:w((()=>[P(r,{sm:24,md:24,lg:16},{default:w((()=>[P(l)])),_:1}),P(r,{sm:12,md:12,lg:4},{default:w((()=>[P(p)])),_:1}),P(r,{sm:12,md:12,lg:4},{default:w((()=>[P(a)])),_:1})])),_:1}),P(o,{gutter:20},{default:w((()=>[P(r,{sm:12,md:12,lg:8},{default:w((()=>[P(n)])),_:1}),P(r,{sm:12,md:12,lg:8},{default:w((()=>[P(u)])),_:1}),P(r,{sm:24,md:24,lg:8},{default:w((()=>[P(o,{gutter:20},{default:w((()=>[P(r,{sm:24,md:12,lg:12},{default:w((()=>[P(c)])),_:1}),P(r,{sm:24,md:12,lg:12},{default:w((()=>[P(g)])),_:1}),P(r,{span:24,class:"no-margin-bottom"},{default:w((()=>[P(v)])),_:1})])),_:1})])),_:1})])),_:1}),P(o,{gutter:20},{default:w((()=>[P(r,{sm:24,md:12,lg:8},{default:w((()=>[P(_)])),_:1}),P(r,{sm:24,md:12,lg:8},{default:w((()=>[P(f)])),_:1}),P(r,{sm:24,md:24,lg:8},{default:w((()=>[P(d)])),_:1})])),_:1}),P(o,{gutter:20},{default:w((()=>[P(r,{md:24,lg:8},{default:w((()=>[P(j)])),_:1}),P(r,{md:24,lg:16,class:"no-margin-bottom"},{default:w((()=>[P(b)])),_:1})])),_:1})])}}))));var I;const k=S(E,[["__scopeId","data-v-a5cb9078"]]);export{k as default};
