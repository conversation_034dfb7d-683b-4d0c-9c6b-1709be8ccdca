<template>
  <div class="page-content">
    <ArtCutterImg
      v-model:imgUrl="imageUrl"
      :boxWidth="540"
      :boxHeight="300"
      :cutWidth="360"
      :cutHeight="200"
      :quality="1"
      :tool="true"
      :watermarkText="'My Watermark'"
      watermarkColor="#ff0000"
      :showPreview="true"
      :originalGraph="false"
      :title="'图片裁剪'"
      :previewTitle="'预览效果'"
      @error="handleError"
      @imageLoadComplete="handleLoadComplete"
      @imageLoadError="handleLoadError"
    />
  </div>
</template>

<script setup lang="ts">
  import lockImg from '@imgs/lock/lock_screen_1.webp'
  const imageUrl = ref(lockImg)

  const handleError = (error: any) => {
    // 裁剪错误
  }

  const handleLoadComplete = (result: any) => {
    // 图片加载完成
  }

  const handleLoadError = (error: any) => {
    // 图片加载失败
  }
</script>
