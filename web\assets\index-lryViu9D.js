var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,o=(t,r,l)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[r]=l;import{g as n}from"./index-DiJiIjk5.js";import{k as i,r as c,c as u,d as p,p as d,w as v,O as f,C as y,W as m,S as w,R as x,Q as b}from"./vendor-9ydHGNSq.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const _={class:"scroll-wrapper"},g=["innerHTML"],j=["innerHTML"],T=i((h=((e,t)=>{for(var r in t||(t={}))a.call(t,r)&&o(e,r,t[r]);if(l)for(var r of l(t))s.call(t,r)&&o(e,r,t[r]);return e})({},{name:"ArtTextScroll"}),L={__name:"index",props:{text:{},speed:{default:70},direction:{default:"left"},type:{default:"default"},showClose:{type:Boolean,default:!1},typewriter:{type:Boolean,default:!1},typewriterSpeed:{default:100}},emits:["close"],setup(e,{emit:t}){const r=t,l=e,a=c(null),s=n(a),o=c(null),i=c(0),O=c("");let T=null;const h=c(!1),L=u((()=>l.typewriter?!s.value&&h.value:!s.value)),k=u((()=>l.typewriter?O.value:l.text)),C=u((()=>({"--animation-duration":`${i.value}s`,"--animation-play-state":L.value?"running":"paused","--animation-direction":"left"===l.direction?"normal":"reverse"}))),P=()=>{if(o.value){const e=o.value.scrollWidth/2;i.value=e/l.speed}},S=()=>{r("close")},H=()=>{let e=0;O.value="",h.value=!1;const t=()=>{e<l.text.length?(O.value+=l.text[e],e++,T=setTimeout(t,l.typewriterSpeed)):h.value=!0};t()};return p((()=>{P(),window.addEventListener("resize",P),l.typewriter&&H()})),d((()=>{window.removeEventListener("resize",P),T&&clearTimeout(T)})),v((()=>l.text),(()=>{l.typewriter&&(T&&clearTimeout(T),H())})),(e,t)=>(y(),f("div",{ref_key:"containerRef",ref:a,class:m(["text-scroll-container",[`text-scroll--${l.type}`]])},[t[1]||(t[1]=w("div",{class:"left-icon"},[w("i",{class:"iconfont-sys"},"")],-1)),w("div",_,[w("div",{class:m(["text-scroll-content",{scrolling:L.value}]),style:b(C.value),ref_key:"scrollContent",ref:o},[w("div",{class:"scroll-item",innerHTML:k.value},null,8,g),w("div",{class:"scroll-item",innerHTML:k.value},null,8,j)],6)]),e.showClose?(y(),f("div",{key:0,class:"right-icon",onClick:S},t[0]||(t[0]=[w("i",{class:"iconfont-sys"},"",-1)]))):x("",!0)],2))}},t(h,r(L))));var h,L;const k=O(T,[["__scopeId","data-v-1bcf9101"]]);export{k as _};
