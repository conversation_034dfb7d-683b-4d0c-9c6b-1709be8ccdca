import{A as s,W as a}from"./index-C5Q-N6Yp.js";import{k as t,O as n,C as l,S as o,X as c,u as i}from"./vendor-9ydHGNSq.js";import{_ as r}from"./_plugin-vue_export-helper-BCo6x5W8.js";const e=""+new URL("draw1-Ce1WF34i.png",import.meta.url).href,u={class:"card about-project art-custom-card"},p={class:"button-wrap"},d=r(t({__name:"AboutProject",setup(t){const r=s.systemInfo.name,d=s=>{window.open(s)};return(s,t)=>(l(),n("div",u,[o("div",null,[t[8]||(t[8]=o("h2",{class:"box-title"},"关于项目",-1)),o("p",null,c(i(r))+" 是一款专注于用户体验和视觉设计的后台管理系统模版",1),t[9]||(t[9]=o("p",null,"使用了 Vue3、TypeScript、Vite、Element Plus 等前沿技术",-1)),o("div",p,[o("div",{class:"btn art-custom-card",onClick:t[0]||(t[0]=s=>d(i(a).DOCS))},t[4]||(t[4]=[o("span",null,"项目官网",-1),o("i",{class:"iconfont-sys"},"",-1)])),o("div",{class:"btn art-custom-card",onClick:t[1]||(t[1]=s=>d(i(a).INTRODUCE))},t[5]||(t[5]=[o("span",null,"文档",-1),o("i",{class:"iconfont-sys"},"",-1)])),o("div",{class:"btn art-custom-card",onClick:t[2]||(t[2]=s=>d(i(a).GITHUB_HOME))},t[6]||(t[6]=[o("span",null,"Github",-1),o("i",{class:"iconfont-sys"},"",-1)])),o("div",{class:"btn art-custom-card",onClick:t[3]||(t[3]=s=>d(i(a).BLOG))},t[7]||(t[7]=[o("span",null,"博客",-1),o("i",{class:"iconfont-sys"},"",-1)]))])]),t[10]||(t[10]=o("img",{class:"right-img",src:e},null,-1))]))}}),[["__scopeId","data-v-11a9f549"]]);export{d as default};
