<template>
  <ArtLayouts>
    <!-- 网络状态监视器 -->
    <NetworkStatus @reconnect="handleNetworkReconnect" />
    <!-- 顶栏、水平/混合菜单 -->
    <ArtHeaderBar />
    <!-- 左侧/双列菜单 -->
    <ArtSidebarMenu />
    <!-- 页面内容 -->
    <ArtPageContent />
    <!-- 设置面板 -->
    <ArtSettingsPanel />
    <!-- 全局搜索 -->
    <ArtGlobalSearch />
    <!-- 屏幕锁定 -->
    <ArtScreenLock />
    <!-- 聊天窗口 -->
    <ArtChatWindow />
    <!-- 礼花效果 -->
    <ArtFireworksEffect />
    <!-- 水印效果 -->
    <ArtWatermark />
  </ArtLayouts>
</template>

<script setup>
  import NetworkStatus from '@/components/common/NetworkStatus.vue'
  import { ElMessage } from 'element-plus'
  import { $t } from '@/locales'

  // 网络重连处理
  const handleNetworkReconnect = () => {
    ElMessage.success($t('common.networkRecovered'))
    // 可以在这里添加页面重载或其他处理
    window.location.reload()
  }
</script>

<style lang="scss" scoped>
  @use './style';
</style>
