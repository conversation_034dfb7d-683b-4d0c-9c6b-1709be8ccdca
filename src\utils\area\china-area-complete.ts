/**
 * 完整的中国地区数据 - 使用 china-division 库
 * 提供完整的省市区三级数据
 */

import { provinces, cities, areas } from 'china-division'

/**
 * 地区数据转换器
 * 将 china-division 库数据转换为项目需要的格式
 */
class ChinaAreaConverter {
  /**
   * 转换为级联选择器格式
   */
  static toCascaderFormat() {
    return provinces.map(province => ({
      value: province.code,
      label: province.name,
      children: cities
        .filter(city => city.provinceCode === province.code)
        .map(city => {
          const cityAreas = areas.filter(area => area.cityCode === city.code)
          return {
            value: city.code,
            label: city.name,
            // 只有当有区县数据时才添加children属性
            ...(cityAreas.length > 0 ? {
              children: cityAreas.map(area => ({
                value: area.code,
                label: area.name
              }))
            } : {})
          }
        })
    }))
  }

  /**
   * 转换为原有格式（兼容性）
   */
  static toOriginalFormat() {
    // 省份数据
    const provinceData = provinces.map(p => ({ 
      value: p.code, 
      label: p.name 
    }))

    // 城市数据 - 按省份分组
    const cityData: Record<string, Array<{ value: string; label: string }>> = {}
    provinces.forEach(province => {
      const provinceCities = cities
        .filter(city => city.provinceCode === province.code)
        .map(city => ({ value: city.code, label: city.name }))
      
      if (provinceCities.length > 0) {
        cityData[province.code] = provinceCities
      }
    })

    // 区县数据 - 按城市分组
    const districtData: Record<string, Array<{ value: string; label: string }>> = {}
    cities.forEach(city => {
      const cityAreas = areas
        .filter(area => area.cityCode === city.code)
        .map(area => ({ value: area.code, label: area.name }))
      
      if (cityAreas.length > 0) {
        districtData[city.code] = cityAreas
      }
    })

    return {
      provinceData,
      cityData,
      districtData
    }
  }

  /**
   * 获取省份列表
   */
  static getProvinces() {
    return provinces
  }

  /**
   * 获取指定省份的城市列表
   */
  static getCitiesByProvince(provinceCode: string) {
    return cities.filter(city => city.provinceCode === provinceCode)
  }

  /**
   * 获取指定城市的区县列表
   */
  static getAreasByCity(cityCode: string) {
    return areas.filter(area => area.cityCode === cityCode)
  }

  /**
   * 根据代码查找名称
   */
  static getNameByCode(code: string): string {
    // 查找省份
    const province = provinces.find(p => p.code === code)
    if (province) return province.name

    // 查找城市
    const city = cities.find(c => c.code === code)
    if (city) return city.name

    // 查找区县
    const area = areas.find(a => a.code === code)
    if (area) return area.name

    return ''
  }

  /**
   * 根据代码数组获取完整地址
   */
  static getFullAddress(codes: string[]): string {
    const names = codes.map(code => this.getNameByCode(code)).filter(Boolean)
    return names.join('')
  }

  /**
   * 解析地址字符串，尝试匹配省市区
   */
  static parseAddress(address: string) {
    const result = {
      province: { code: '', name: '' },
      city: { code: '', name: '' },
      area: { code: '', name: '' },
      codes: [] as string[],
      detail: address
    }

    // 查找省份
    for (const province of provinces) {
      const provinceName = province.name.replace(/省|市|自治区|特别行政区/g, '')
      if (address.includes(provinceName)) {
        result.province = { code: province.code, name: province.name }
        result.codes.push(province.code)
        
        // 查找城市
        const provinceCities = cities.filter(city => city.provinceCode === province.code)
        for (const city of provinceCities) {
          const cityName = city.name.replace(/市|区|县|自治州|地区|盟/g, '')
          if (address.includes(cityName)) {
            result.city = { code: city.code, name: city.name }
            result.codes.push(city.code)
            
            // 查找区县
            const cityAreas = areas.filter(area => area.cityCode === city.code)
            for (const area of cityAreas) {
              const areaName = area.name.replace(/区|县|市/g, '')
              if (address.includes(areaName)) {
                result.area = { code: area.code, name: area.name }
                result.codes.push(area.code)
                break
              }
            }
            break
          }
        }
        break
      }
    }

    return result
  }

  /**
   * 获取数据统计信息
   */
  static getStatistics() {
    const totalProvinces = provinces.length
    const totalCities = cities.length
    const totalAreas = areas.length
    
    // 统计有区县数据的城市
    const citiesWithAreas = new Set(areas.map(area => area.cityCode)).size
    
    // 按省份统计
    const provinceStats = provinces.map(province => {
      const provinceCities = cities.filter(city => city.provinceCode === province.code)
      const provinceAreas = areas.filter(area => {
        return provinceCities.some(city => city.code === area.cityCode)
      })
      
      return {
        province: province.name,
        cities: provinceCities.length,
        areas: provinceAreas.length
      }
    })

    return {
      total: {
        provinces: totalProvinces,
        cities: totalCities,
        areas: totalAreas
      },
      coverage: {
        citiesWithAreas,
        coverageRate: ((citiesWithAreas / totalCities) * 100).toFixed(1) + '%'
      },
      byProvince: provinceStats
    }
  }

  /**
   * 验证代码的有效性
   */
  static validateCodes(codes: string[]): boolean {
    if (!codes || codes.length === 0) return false

    const [provinceCode, cityCode, areaCode] = codes

    // 验证省份
    if (!provinces.find(p => p.code === provinceCode)) {
      return false
    }

    // 验证城市（如果提供）
    if (cityCode && !cities.find(c => c.code === cityCode && c.provinceCode === provinceCode)) {
      return false
    }

    // 验证区县（如果提供）
    if (areaCode && !areas.find(a => a.code === areaCode && a.cityCode === cityCode)) {
      return false
    }

    return true
  }

  /**
   * 搜索地区
   */
  static searchArea(keyword: string) {
    const results = {
      provinces: [] as typeof provinces,
      cities: [] as typeof cities,
      areas: [] as typeof areas
    }

    if (!keyword) return results

    const lowerKeyword = keyword.toLowerCase()

    // 搜索省份
    results.provinces = provinces.filter(p => 
      p.name.toLowerCase().includes(lowerKeyword)
    )

    // 搜索城市
    results.cities = cities.filter(c => 
      c.name.toLowerCase().includes(lowerKeyword)
    )

    // 搜索区县
    results.areas = areas.filter(a => 
      a.name.toLowerCase().includes(lowerKeyword)
    )

    return results
  }
}

// 导出转换后的数据
export const completeAreaData = ChinaAreaConverter.toOriginalFormat()
export const completeCascaderOptions = ChinaAreaConverter.toCascaderFormat()
export const chinaAreaConverter = ChinaAreaConverter

// 导出原始数据
export { provinces, cities, areas }

export default completeAreaData
