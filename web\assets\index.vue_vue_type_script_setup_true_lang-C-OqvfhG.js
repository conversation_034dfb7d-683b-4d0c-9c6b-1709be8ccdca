var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,r=(t,a,o)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[a]=o,n=(e,t)=>{for(var a in t||(t={}))i.call(t,a)&&r(e,a,t[a]);if(o)for(var a of o(t))s.call(t,a)&&r(e,a,t[a]);return e},l=(e,o)=>t(e,a(o));import"./index-C5Q-N6Yp.js";/* empty css                   */import{_ as d}from"./index-BqI1XISX.js";import{u as p,a as u}from"./useChart-CoPw7zBp.js";import{k as g,a2 as y,aG as f,O as m,C as b,Q as h,B as c,R as x,u as v}from"./vendor-9ydHGNSq.js";const L=g(l(n({},{name:"ArtDualBarCompareChart"}),{__name:"index",props:{positiveData:{default:()=>[]},negativeData:{default:()=>[]},xAxisData:{default:()=>[]},positiveName:{default:"正向数据"},negativeName:{default:"负向数据"},barWidth:{default:16},yAxisMin:{default:-100},yAxisMax:{default:100},showDataLabel:{type:Boolean,default:!1},positiveBorderRadius:{default:()=>[10,10,0,0]},negativeBorderRadius:{default:()=>[0,0,10,10]},height:{default:p().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>p().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!1},showSplitLine:{type:Boolean,default:!1},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(e){const t=e,a=e=>{const{fontColor:a}=p(),o=L();return n({name:e.name,type:"bar",stack:"total",barWidth:t.barWidth,barGap:"-100%",data:e.data,itemStyle:{borderRadius:e.borderRadius,color:t.colors[e.colorIndex]},label:{show:t.showDataLabel,position:e.labelPosition,formatter:e.formatter||(e=>String(e.value)),color:a,fontSize:12}},o)},{chartRef:o,getAxisLineStyle:i,getAxisLabelStyle:s,getAxisTickStyle:r,getSplitLineStyle:g,getAnimationConfig:L,getTooltipStyle:w,getLegendStyle:A,getGridWithLegend:D,isEmpty:S}=u({props:t,checkEmpty:()=>t.isEmpty||!t.positiveData.length||!t.negativeData.length||t.positiveData.every((e=>0===e))&&t.negativeData.every((e=>0===e)),watchSources:[()=>t.positiveData,()=>t.negativeData,()=>t.xAxisData,()=>t.colors],generateOptions:()=>{const e=t.negativeData.map((e=>e>0?-e:e)),o={top:t.showLegend?50:20,right:0,left:0,bottom:0,containLabel:!0};return{backgroundColor:"transparent",animation:!0,animationDuration:1e3,animationEasing:"cubicOut",grid:D(t.showLegend,t.legendPosition,o),tooltip:t.showTooltip?l(n({},w()),{trigger:"axis",axisPointer:{type:"none"}}):void 0,legend:t.showLegend?l(n({},A(t.legendPosition)),{data:[t.negativeName,t.positiveName]}):void 0,xAxis:{type:"category",data:t.xAxisData,axisTick:r(),axisLine:i(t.showAxisLine),axisLabel:s(t.showAxisLabel),boundaryGap:!0},yAxis:{type:"value",min:t.yAxisMin,max:t.yAxisMax,axisLabel:s(t.showAxisLabel),axisLine:i(t.showAxisLine),splitLine:g(t.showSplitLine)},series:[a({name:t.negativeName,data:e,borderRadius:t.negativeBorderRadius,labelPosition:"bottom",colorIndex:1,formatter:e=>String(Math.abs(e.value))}),a({name:t.positiveName,data:t.positiveData,borderRadius:t.positiveBorderRadius,labelPosition:"top",colorIndex:0})]}}});return(e,a)=>{const i=d,s=f;return y((b(),m("div",{ref_key:"chartRef",ref:o,style:h({height:t.height})},[v(S)?(b(),c(i,{key:0})):x("",!0)],4)),[[s,t.loading]])}}}));export{L as _};
