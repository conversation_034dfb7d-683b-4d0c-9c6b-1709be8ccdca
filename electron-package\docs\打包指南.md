# Electron打包指南

## 📦 打包流程

### 1. 环境准备
```bash
# 确保依赖已安装
pnpm install

# 安装Electron相关依赖
pnpm add electron electron-builder -D
```

### 2. 构建Vue项目
```bash
# 构建生产版本
npm run build

# 确认web目录生成
ls web/
# 应该包含: index.html, assets/
```

### 3. 配置检查
确保以下文件配置正确：

#### package.json
```json
{
  "main": "main.js",
  "scripts": {
    "electron:build": "npx vite build && npx pnpm exec electron-builder build --config config/electron-builder.json"
  }
}
```

#### vite.config.ts
```typescript
export default defineConfig({
  base: "./",  // 重要：相对路径
  build: {
    outDir: "web"  // 输出到web目录
  }
})
```

### 4. 执行打包
```bash
# 方法1：使用npm脚本
npm run electron:build

# 方法2：直接使用electron-builder
npx pnpm exec electron-builder build --config config/electron-builder.json
```

## ⚙️ 打包配置详解

### electron-builder.json 完整配置
```json
{
  "productName": "点点管理系统",
  "appId": "com.diandian.admin",
  "version": "1.0.0",
  "description": "基于Vue3的管理系统",
  "author": "点点科技",
  "files": [
    "./main.js",
    "./preload.js", 
    "./web"
  ],
  "directories": {
    "output": "electron-dist"
  },
  "npmRebuild": false,
  "win": {
    "target": [
      {
        "target": "portable",
        "arch": ["x64"]
      }
    ],
    "icon": "assets/icons/icon.ico",
    "artifactName": "${productName} ${version}.${ext}"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true
  }
}
```

### 配置项说明

#### 基本信息
- `productName`: 产品显示名称
- `appId`: 应用唯一标识符
- `version`: 应用版本号
- `description`: 应用描述
- `author`: 作者信息

#### 文件配置
- `files`: 要打包的文件和目录
- `directories.output`: 输出目录
- `npmRebuild`: 是否重新构建原生模块

#### Windows配置
- `win.target`: 目标格式（portable/nsis/zip等）
- `win.arch`: 目标架构（x64/ia32/arm64）
- `win.icon`: 应用图标路径
- `win.artifactName`: 输出文件命名规则

## 🎯 不同打包格式

### 1. 便携版 (portable)
```json
{
  "win": {
    "target": "portable"
  }
}
```
- 优点：单文件，无需安装
- 缺点：文件较大，启动稍慢

### 2. 安装包 (nsis)
```json
{
  "win": {
    "target": "nsis"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true
  }
}
```
- 优点：标准安装体验，文件关联
- 缺点：需要安装过程

### 3. 压缩包 (zip)
```json
{
  "win": {
    "target": "zip"
  }
}
```
- 优点：文件小，解压即用
- 缺点：需要手动解压

## 🔧 常见问题解决

### 1. 依赖安装失败
```bash
# 清理缓存
npm cache clean --force
pnpm store prune

# 重新安装
pnpm install
```

### 2. 图标问题
- 确保图标是256x256的ico格式
- 路径使用相对路径
- 检查文件是否存在

### 3. 文件占用错误
```bash
# 关闭所有Electron进程
taskkill /f /im electron.exe
taskkill /f /im "点点管理系统.exe"

# 删除输出目录
rm -rf electron-dist
```

### 4. 网络下载问题
```bash
# 设置镜像
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
npm config set electron-builder-binaries_mirror https://npmmirror.com/mirrors/electron-builder-binaries/
```

## 📊 打包优化

### 1. 减小包体积
```json
{
  "files": [
    "!node_modules",
    "!src",
    "!public",
    "!*.md",
    "./main.js",
    "./web"
  ]
}
```

### 2. 启用压缩
```json
{
  "compression": "maximum",
  "asar": true
}
```

### 3. 多架构支持
```json
{
  "win": {
    "target": [
      {
        "target": "portable",
        "arch": ["x64", "ia32"]
      }
    ]
  }
}
```

## 🚀 自动化打包

### GitHub Actions示例
```yaml
name: Build Electron App
on: [push]
jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run build
      - run: npm run electron:build
```

### 本地脚本
```bash
#!/bin/bash
echo "开始构建Electron应用..."
npm run build
npx pnpm exec electron-builder build --config config/electron-builder.json
echo "构建完成！"
```

## 📋 打包检查清单

- [ ] Vue项目构建成功 (web目录存在)
- [ ] main.js配置正确
- [ ] electron-builder.json配置完整
- [ ] 图标文件格式正确 (256x256 ico)
- [ ] 依赖安装完成
- [ ] 网络连接正常
- [ ] 磁盘空间充足 (至少2GB)

## 📈 版本管理

### 版本号规则
- 主版本号：重大功能变更
- 次版本号：新功能添加
- 修订号：bug修复

### 更新配置
```json
{
  "version": "1.2.3",
  "publish": {
    "provider": "github",
    "repo": "your-repo"
  }
}
```

## 🎉 打包成功

成功打包后，您将在 `electron-dist` 目录中看到：
- `点点管理系统 1.0.0.exe` - 便携版应用
- `win-unpacked/` - 解压版本
- 其他构建产物和日志文件

恭喜！您的Vue3应用已成功打包为Electron桌面应用！
